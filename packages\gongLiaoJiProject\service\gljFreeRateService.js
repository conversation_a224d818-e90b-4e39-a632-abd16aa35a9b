const {Service} = require('../../../core');
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {FreeRateModel} = require("../models/FreeRateModel");
const gljFreeRate = require("../jsonData/glj_free_rate.json");
const WildcardMap = require("../core/container/WildcardMap");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {FreeRateProjectModel} = require("../models/FreeRateProjectModel");
const {PricingGSUtils} = require("../utils/PricingGSUtils");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const CommonConstants = require("../../gongLiaoJiProject/constants/CommonConstants");
const {BaseFeeFileProject2022} = require("../models/BaseFeeFileProject");
const {NumberUtil} = require("../utils/NumberUtil");


/**
 * 费率 service
 * @class
 */
class GljFreeRateService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取所有费率
     * @returns {Promise<GsBaseFreeRate[]|Error>}
     */
    async getByFreeRateAll() {
        return await this.gsBaseDeLibraryDao.find({});
    }


    /**
     * 初始化费率说明
     * @returns {Promise<FreeRateProjectModel>}
     */
    async initFreeDescribe(){
        let freeRateProjectModel = new FreeRateProjectModel()
        freeRateProjectModel.constructId = this.constructId;
        freeRateProjectModel.projectType = FreeRateModel.DEFAULT_PROJECT_TYPE;
        freeRateProjectModel.projectLocation = FreeRateModel.DEFAULT_PROJECT_LOCATION;
        freeRateProjectModel.roadSurfaceNum = FreeRateModel.DEFAULT_ROAD_SURFACE_NUM;
        freeRateProjectModel.floorSpace = FreeRateModel.DEFAULT_FLOOR_SPACE;
        freeRateProjectModel.municipalConstructionCost = FreeRateModel.DEFAULT_MUNICIPCAL_CONSTRUCTION_COST;
        return freeRateProjectModel;
    }


    /**
     * 获取费率说明
     * @param args
     * @returns {Promise<*>}
     */
    async feeDescriptionData(args) {
        return gljFreeRate.feeDescriptionData;
    }

    /**
     * 获取费率说明
     * @param args
     * @returns {Promise<*>}
     */
    async getProjectQfbDes(args) {
        let {constructId} = args;
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_FLSM);
        let freeRateProjectModelList = ConvertUtil.deepCopy(gljFreeRate.feeDescriptionData);
        for (let item of freeRateProjectModelList) {
            if (item.field === "projectType") {
                item.default = freeRateProjectModel.projectType;
            }
            if (item.field === "projectLocation") {
                item.default = freeRateProjectModel.projectLocation;
            }
            if (item.field === "roadSurfaceNum") {
                item.default = freeRateProjectModel.roadSurfaceNum;
            }
            if (item.field === "floorSpace") {
                item.default = freeRateProjectModel.floorSpace;
            }
            if (item.field === "municipalConstructionCost") {
                item.default = freeRateProjectModel.municipalConstructionCost;
            }
        }
        return freeRateProjectModelList;
    }


    /**
     * 获取费率说明
     * @param args
     * @returns {Promise<*>}
     */
    async getProjectQfbDesSingle(args) {
        let {constructId,singleId} = args;
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
        let freeKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_FLSM);
        let freeRateSingleModel = singleQfbMap.get(freeKey);

        let freeRateProjectModelList = ConvertUtil.deepCopy(gljFreeRate.feeDescriptionData);
        for (let item of freeRateProjectModelList) {
            if (item.field === "projectType") {
                item.default = freeRateSingleModel.projectType;
            }
            if (item.field === "projectLocation") {
                item.default = freeRateSingleModel.projectLocation;
            }
            if (item.field === "roadSurfaceNum") {
                item.default = freeRateSingleModel.roadSurfaceNum;
            }
            if (item.field === "floorSpace") {
                item.default = freeRateSingleModel.floorSpace;
            }
            if (item.field === "municipalConstructionCost") {
                item.default = freeRateSingleModel.municipalConstructionCost;
            }
        }
        return freeRateProjectModelList;
    }


    /**
     * 获取单位工程费率说明
     * @param args
     * @returns {Promise<*>}
     */
    async getUnitQfbDes(args) {
        let {constructId, unitId} = args;
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM);
        let freeKey = WildcardMap.generateKey(unitId);
        let unitfreeRate = unitQfbMap.get(freeKey);
        let freeRateProjectModelList = ConvertUtil.deepCopy(gljFreeRate.feeDescriptionData);
        for (let item of freeRateProjectModelList) {
            if (item.field === "projectType") {
                item.default = unitfreeRate.projectType
            }
            if (item.field === "projectLocation") {
                item.default = unitfreeRate.projectLocation
            }
            if (item.field === "roadSurfaceNum") {
                item.default = unitfreeRate.roadSurfaceNum;
            }
            if (item.field === "floorSpace") {
                item.default = unitfreeRate.floorSpace;
            }
            if (item.field === "municipalConstructionCost") {
                item.default = unitfreeRate.municipalConstructionCost;
            }
        }
        return freeRateProjectModelList;
    }

    /**
     * 获取费率目录树
     * @param args
     * @returns {Promise<*>}
     */
    async feeCatalogueData(args) {
        return gsFreeRate.feeCatalogueData;
    }

    /**
     * 恢复单位工程费率
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode 定额库编码
     * @param projectType 所属工程专业
     * @param projectLocation 工程所在地
     * @param roadSurfaceNum 临路面数
     * @param floorSpace 建筑面积
     * @param municipalConstructionCost 市政工程造价
     */
    async recoverUnitFreeRate(args) {
        let {
            constructId,
            unitId,
            singleId,
        } = args;
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
        let keys = Array.from(unitQfbMap.keys());
        for(const key of keys){
            if(key.includes(unitId)){
                //获取每一个取费数据
                let qfObj = unitQfbMap.get(key);
                let freeKey = WildcardMap.generateKey(unitId,qfObj.qfCode);
                args.libraryCode=qfObj.libraryCode;
                args.type=3;
                args.singleId=unitProject.parentId;
                args.freeFileOld=qfObj;
                args.qfCode=qfObj.qfCode;
                //恢复默认费率
                let newVar = await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateData(args);
                //更新取费表数据
                ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, newVar));

                try {
                    await this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, newVar);
                } catch (e) {
                    console.error("费用汇总出错" + e.toString())
                }
            }
        }
        // let freeRate = await this.getBaseFreeRate(args);
        return true;
    }


    /**
     * 根据条件查询 单位工程费率
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode 定额库编码
     * @param projectType 所属工程专业
     * @param projectLocation 工程所在地
     * @param roadSurfaceNum 临路面数
     * @param floorSpace 建筑面积
     * @param municipalConstructionCost 市政工程造价
     */
    async getBaseFreeRate(args) {
        let {
            constructId,
            unitId,
            libraryCode,
            projectType,
            projectLocation,
            roadSurfaceNum,
            floorSpace,
            municipalConstructionCost
        } = args;
        let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseFreeRateService.searchFreeRateInfo(args);
        let freeRate = ObjectUtils.copyProp(gsBaseFreeRate, new FreeRateModel());
        freeRate.init();
        freeRate.constructId = constructId;
        freeRate.unitId = unitId;
        return freeRate;
    }

    /**
     * 根据条件查询 单位工程费率
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode 定额库编码
     * @param projectType 所属工程专业
     * @param projectLocation 工程所在地
     * @param roadSurfaceNum 临路面数
     * @param floorSpace 建筑面积
     * @param municipalConstructionCost 市政工程造价
     */
    async getBaseFreeRateGlj(args) {
        let {
            constructId,
            unitId,
            libraryCode,
            projectType,
            projectLocation,
            roadSurfaceNum,
            floorSpace,
            municipalConstructionCost
        } = args;
        let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljInitUnitProjectService.getFeeData(args);
        return gsBaseFreeRate;
    }

    /**
     * 修改单位工程费率
     * @param freeRateModel
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode  定额库编码'
     * @param manageFeeRate  企业管理费（%）'
     * @param profitRate  利润（%）'
     * @param taxRate  税金（%）'
     * @param gfRate  规费（%）'
     * @param anwenRate  安全生产、文明施工费（%）'
     */
    async updateUnitFreeRate(freeRateModel) {
        let {constructId, unitId, libraryCode, manageFeeRate, profitRate, taxRate, gfRate, anwenRate,qfCode} = freeRateModel;

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;


        // 获取费率工程项目id
        let freeKey = WildcardMap.generateKey(unitId, qfCode);
        let freeRate = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB).get(freeKey);
        freeRate.manageFeeRate = ObjectUtils.isEmpty(manageFeeRate) ? freeRate.manageFeeRate : Number(manageFeeRate);
        freeRate.manageFeeRateUpdate = ObjectUtils.isNotEmpty(freeRateModel.manageFeeRateUpdate) ? freeRateModel.manageFeeRateUpdate : false;
        freeRate.profitRate = ObjectUtils.isEmpty(profitRate) ? freeRate.profitRate : Number(profitRate);
        freeRate.profitRateUpdate = ObjectUtils.isNotEmpty(freeRateModel.profitRateUpdate) ? freeRateModel.profitRateUpdate : false;
        freeRate.taxRate = ObjectUtils.isEmpty(taxRate) ? freeRate.taxRate : Number(taxRate);
        freeRate.taxRateUpdate = ObjectUtils.isNotEmpty(freeRateModel.taxRateUpdate) ? freeRateModel.taxRateUpdate : false;
        freeRate.gfRate = ObjectUtils.isEmpty(gfRate) ? freeRate.gfRate : Number(gfRate);
        freeRate.gfRateUpdate = ObjectUtils.isNotEmpty(freeRateModel.gfRateUpdate) ? freeRateModel.gfRateUpdate : false;
        freeRate.anwenRate = ObjectUtils.isEmpty(anwenRate) ? freeRate.anwenRate : Number(anwenRate);
        freeRate.anwenRateUpdate = ObjectUtils.isNotEmpty(freeRateModel.anwenRateUpdate) ? freeRateModel.anwenRateUpdate : false;

        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, freeRate));
        this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, freeRate);
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
        return freeRate;
    }


    /**
     * 修改单位工程费率说明
     * @param freeRateModel
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     */
    async updateUnitFreeRateDescribe(freeRateModel) {
        let {constructId, unitId} = freeRateModel
        // 获取费率工程项目id
        let freeRate = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM).get(unitId);
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
        freeRate.projectType = freeRateModel.projectType ;
        freeRate.projectLocation = freeRateModel.projectLocation;
        freeRate.roadSurfaceNum = freeRateModel.roadSurfaceNum;
        freeRate.floorSpace = freeRateModel.floorSpace;
        freeRate.municipalConstructionCost = freeRateModel.municipalConstructionCost ;
        freeRate.libraryCode=unitProject.constructMajorType;
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_FLSM, unitQfbMap.set(unitId, freeRate));
        let args={constructId,unitId,"type":3}
        await this.recoverUnitFreeRate(args);
        // await this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, freeRate);
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
        return freeRate;
    }

    /**
     * 修改单位工程费率
     * @param freeRateModel
     * @param constructId 工程项目id
     * @param unitId 单位工程id
     * @param libraryCode  定额库编码'
     * @param manageFeeRate  企业管理费（%）'
     * @param profitRate  利润（%）'
     * @param taxRate  税金（%）'
     * @param gfRate  规费（%）'
     * @param anwenRate  安全生产、文明施工费（%）'
     */
    async updateUnitFree(freeRateModel) {
        let {constructId, unitId, libraryCode, manageFeeRate, profitRate, taxRate, gfRate, anwenRate,qfCode} = freeRateModel;

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        // 获取费率工程项目id
        let freeKey = WildcardMap.generateKey(unitId, qfCode);
        let freeRate = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB).get(freeKey);

        freeRate.manageFeeRate = ObjectUtils.isEmpty(manageFeeRate) ? freeRate.manageFeeRate : Number(manageFeeRate);
        if(manageFeeRate!=freeRate.manageFeeRate){
            freeRate.manageFeeRateUpdate = true;
        }

        freeRate.profitRate = ObjectUtils.isEmpty(profitRate) ? freeRate.profitRate : Number(profitRate);
        if(profitRate!=freeRate.profitRate){
            freeRate.profitRateUpdate = true;
        }

        freeRate.taxRate = ObjectUtils.isEmpty(taxRate) ? freeRate.taxRate : Number(taxRate);
        if(taxRate!=freeRate.taxRate){
            freeRate.taxRateUpdate = true;
        }

        freeRate.gfRate = ObjectUtils.isEmpty(gfRate) ? freeRate.gfRate : Number(gfRate);
        if(gfRate!=freeRate.gfRate){
            freeRate.gfRateUpdate = true;
        }

        freeRate.anwenRate = ObjectUtils.isEmpty(anwenRate) ? freeRate.anwenRate : Number(anwenRate);
        if(anwenRate!=freeRate.anwenRate){
            freeRate.anwenRateUpdate = true;
        }

        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, freeRate));
    }

    /**
     * 新增费率  如果存在直接使用   如果不存在就新增一条
     * @param constructId
     * @param unitId
     * @param libraryCode
     * @returns {Promise<void>}
     */
    async addUnitFreeRateCostSummary(constructId, unitId, libraryCode) {
        //查一个默认的qfcode
        let baseFeeFileProject2022 = await this.getFeeFilesByQfCode(libraryCode);
        //非标准定额不需要取费文件
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
        let freeKey = WildcardMap.generateKey(unitId,baseFeeFileProject2022.qfCode);
        let freeRate = unitQfbMap.get(freeKey);
        //如果有对应的取费文件 就不增加
        if(ObjectUtils.isNotEmpty(freeRate)){
            return freeRate ;
        }
        let freeRateModel = new FreeRateModel();
        freeRateModel.constructId=constructId;
        freeRateModel.unitId=unitId;
        freeRateModel.qfCode=baseFeeFileProject2022.qfCode;
        freeRateModel.libraryCode=baseFeeFileProject2022.libraryCode;

        let args={};
        args.libraryCode=baseFeeFileProject2022.libraryCode;
        args.type=3;
        args.singleId=unitProject.parentId;
        args.freeFileOld=freeRateModel;
        args.constructId=constructId;
        args.unitId=unitId;
        args.qfCode=baseFeeFileProject2022.qfCode;
        //恢复默认费率
        let newVar = await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateData(args);
        let baseFeeFile =await this.service.gongLiaoJiProject.baseFeeFileService.getBaseFeeFile(newVar.qfCode);
        newVar.freeProfession=baseFeeFile.qfName;
        newVar.diffFreeRate=[];
        //更新取费表数据
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, newVar));
        //同步工程项目和单项工程数据
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        freeRateProjectModel.childFreeRate.set(freeRateModel.qfCode, newVar);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        //同步单项工程
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let freeKeySingle = WildcardMap.generateKey(unitProject.parentId, FunctionTypeConstants.SINGLE_QFB);
        let freeRateSingleModel = singleQfbMap.get(freeKeySingle);
        freeRateSingleModel.childFreeRate.set(freeRateModel.qfCode, newVar);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));
        //多层级单项数据同步
        this.service.gongLiaoJiProject.gljBaseFreeRateService.dealLevelSingleFee(constructId, unitProject.parentId,newVar);
        return newVar;
    }


    /**
     * 修改定额取费专业后同步取费文件
     * @param constructId
     * @param unitId
     * @param libraryCode
     * @param qfCode
     * @returns {Promise<*>}
     */
    async updateDeFeeMajorAddFeeRate(constructId, unitId, libraryCode,qfCode) {
        if(ObjectUtils.isEmpty(qfCode)){
            return;
        }
        if(ObjectUtils.isNotEmpty(qfCode) && qfCode === CommonConstants.SYSTEM_SZGC){
            return;
        }
        // 通过定额的取费专业，查询当前取费专业的libraryCode
        let baseFeeFileProject2022 = await this.getFeeFilesByQfCode(qfCode);
        //非标准定额不需要取费文件
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
        let freeKey = WildcardMap.generateKey(unitId,qfCode);
        let freeRate = unitQfbMap.get(freeKey);
        //如果有对应的取费文件 就不增加
        if(ObjectUtils.isNotEmpty(freeRate)){
            return freeRate ;
        }
        let freeRateModel = new FreeRateModel();
        freeRateModel.constructId=constructId;
        freeRateModel.unitId=unitId;
        freeRateModel.qfCode=qfCode;
        freeRateModel.libraryCode=baseFeeFileProject2022.libraryCode;

        let args={};
        args.libraryCode=baseFeeFileProject2022.libraryCode;
        args.type=3;
        args.singleId=unitProject.parentId;
        args.freeFileOld=freeRateModel;
        args.constructId=constructId;
        args.unitId=unitId;
        args.qfCode=qfCode;

        let newVar = await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateData(args);
        let baseFeeFile =await this.service.gongLiaoJiProject.baseFeeFileService.getBaseFeeFile(newVar.qfCode);
        newVar.freeProfession=baseFeeFile.qfName;
        newVar.diffFreeRate=[];
        //更新取费表数据
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, newVar));
        //同步工程项目和单项工程数据
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        freeRateProjectModel.childFreeRate.set(freeRateModel.qfCode, ConvertUtil.deepCopy(newVar));
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        //同步单项工程
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let freeKeySingle = WildcardMap.generateKey(unitProject.parentId, FunctionTypeConstants.SINGLE_QFB);
        let freeRateSingleModel = singleQfbMap.get(freeKeySingle);
        freeRateSingleModel.childFreeRate.set(freeRateModel.qfCode, ConvertUtil.deepCopy(newVar));
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));
        //多层级单项数据同步
        this.service.gongLiaoJiProject.gljBaseFreeRateService.dealLevelSingleFee(constructId, unitProject.parentId, ConvertUtil.deepCopy(newVar));
        return newVar;
    }

    /**
     * 获取预算书、措施项目、独立费、费用汇总中所有的取费专业，并去重
     * @param constructId
     * @param unitId
     * @returns {Promise<any[]>}
     */
    async getQfMajorTypeSet(constructId, unitId){

        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let budgetBookTypes, csxmTypes, independentCostTypes;
        // 1. 获取预算书定额的所有取费专业类型    //  && item.type === DeTypeConstants.DE_TYPE_DE
        let yssDes = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
        if (ObjectUtils.isNotEmpty(yssDes)) {
            budgetBookTypes = yssDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        }

        // 2. 获取措施项目定额的所有取费专业类型   //  && item.type === DeTypeConstants.DE_TYPE_DE    '03'
        let csxmDes = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && !['0', '01', '02'].includes(item.type));
        if (ObjectUtils.isNotEmpty(csxmDes)) {
            csxmTypes = csxmDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        }

        // 3. 独立费的所有取费专业类型
        let independentCosts = businessMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
            .get(await this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unitId))?.filter(item => item.isRcj !== true)
        if (ObjectUtils.isNotEmpty(independentCosts)) {
            independentCostTypes = independentCosts.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costMajorCode)).map(item => item.costMajorCode);
        }

        // 4. 获取费用汇总的所有取费专业类型
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
        let unitCostSummarysTypes = Array.from(new Map(Object.entries(unitProject.qfMajorTypeMoneyMap)).keys());

        // 4. 获取该单位所有的专业：专业名称数组判空且合并去重，没有数据则返回
        let qfMajorTypeList = [budgetBookTypes, csxmTypes, independentCostTypes, unitCostSummarysTypes].filter(arr => ObjectUtils.isNotEmpty(arr));
        return qfMajorTypeList.flat();
    }


    /**
     * 删除定额取费专业后同步取费文件
     * @param constructId
     * @param unitId
     * @param libraryCode  没用
     * @param qfCode
     * @returns {Promise<*>}
     */
    async updateDeFeeMajorDelFeeRate(constructId, unitId, libraryCode, qfCode) {
        // 获取单位
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
       // 获取预算书、措施项目、独立费、费用汇总中所有的取费专业，并去重
        let qfMajorTypeListUnit = await this.getQfMajorTypeSet(constructId, unitId);
        let qfMajorTypeSetUnit = [...new Set(qfMajorTypeListUnit.flat())];
        let qfMajorTypes = [];
        if (ObjectUtils.isEmpty(qfMajorTypeSetUnit)) {
            return;
        } else {
            for (let qfMajorType of qfMajorTypeSetUnit) {
                let freeKey = WildcardMap.generateKey(unitId, qfMajorType);
                qfMajorTypes.push(freeKey);
            }
        }

        // 获取单位层级取费表数据
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let freeRateMapUnit = new Map();
        if (ObjectUtils.isNotEmpty(unitQfbMap)) {
            for (let [key, value] of unitQfbMap) {
                if (!key.includes(unitId)) {
                    freeRateMapUnit.set(key, value);
                } else {
                    if (qfMajorTypes.includes(key)) {
                        freeRateMapUnit.set(key, value);
                    }
                }
            }
        }
        // 更新取费表数据
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, freeRateMapUnit);
        
        //同步工程项目和单项工程数据
        let projectObj = ProjectDomain.getDomain(constructId).getProjectById(constructId);
        // 获取该单项下的所有单位
        let unitProjectConstructs = [];
        PricingGSUtils.getUnitProjectsByCurrentNode(projectObj.children, unitProjectConstructs);
        let qfMajorTypeListConstruct = []
        for (let unitProject of unitProjectConstructs) {
            let qfMajorTypesUnit = await this.getQfMajorTypeSet(constructId, unitProject.sequenceNbr);
            if (!ObjectUtils.isEmpty(qfMajorTypesUnit)) {
                qfMajorTypeListConstruct.push(...qfMajorTypesUnit);
            }
        }
        let qfMajorTypeSetConstruct = [...new Set(qfMajorTypeListConstruct.flat())];

        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let freeRateMapConstruct = new Map();
        if (ObjectUtils.isNotEmpty(freeRateProjectModel.childFreeRate)) {
            for (let [key, value] of freeRateProjectModel.childFreeRate) {
                if (qfMajorTypeSetConstruct.includes(key)) {
                    freeRateMapConstruct.set(key, value);
                }
            }
        }
        freeRateProjectModel.childFreeRate = freeRateMapConstruct;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);

        //同步单项工程
        let singleObj = ProjectDomain.getDomain(constructId).getProjectById(unitProject.parentId);
        //增加单位上级类型判断，因为单位可直接挂在项目下
        if (singleObj.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            // 获取该单项下的所有单位
            let unitProjectSingles = [];
            PricingGSUtils.getUnitProjectsByCurrentNode(singleObj.children, unitProjectSingles);
            let qfMajorTypeListSingle = []
            for (let unitProject of unitProjectSingles) {
                let qfMajorTypesSingle = await this.getQfMajorTypeSet(constructId, unitProject.sequenceNbr);
                if (!ObjectUtils.isEmpty(qfMajorTypesSingle)) {
                    qfMajorTypeListSingle.push(...qfMajorTypesSingle);
                }
            }
            let qfMajorTypeSetSingle = [...new Set(qfMajorTypeListSingle.flat())];

            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
            let freeKeySingle = WildcardMap.generateKey(unitProject.parentId, FunctionTypeConstants.SINGLE_QFB);
            let freeRateSingleModel = singleQfbMap.get(freeKeySingle);

            let freeRateMapSingle = new Map();
            if (ObjectUtils.isNotEmpty(freeRateSingleModel.childFreeRate)) {
                for (let [key, value] of freeRateSingleModel.childFreeRate) {
                    if (qfMajorTypeSetSingle.includes(key)) {
                        freeRateMapSingle.set(key, value);
                    }
                }
            }
            freeRateSingleModel.childFreeRate = freeRateMapSingle;
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));

            //多层级单项数据同步
            await this.dealLevelSingleFee(constructId, unitProject.parentId, qfCode);
        }
    }


    /**
     * 同步单项工程取费文件
     * @param constructId
     * @param singleId
     * @param qfCode
     * @returns {Promise<void>}
     */
    async dealLevelSingleFee(constructId, singleId, qfCode) {
        let singleProject = ProjectDomain.getDomain(constructId).getProjectById(singleId);
        if (ObjectUtils.isNotEmpty(singleProject) && singleProject.type !== ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            // 获取该单项下的所有单位
            let unitProjectSingles = [];
            PricingGSUtils.getUnitProjectsByCurrentNode(singleProject.children, unitProjectSingles);
            let qfMajorTypeListSingle = []
            for (let unitProject of unitProjectSingles) {
                let qfMajorTypesSingle = await this.getQfMajorTypeSet(constructId, unitProject.sequenceNbr);
                if (!ObjectUtils.isEmpty(qfMajorTypesSingle)) {
                    qfMajorTypeListSingle.push(...qfMajorTypesSingle);
                }
            }
            let qfMajorTypeSetSingle = [...new Set(qfMajorTypeListSingle.flat())];

            // 同步至单项取费率
            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
            let freeKeySingle = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
            let freeRateSingleModel = ObjectUtils.isNotEmpty(singleQfbMap.get(freeKeySingle)) ? singleQfbMap.get(freeKeySingle) : {"childFreeRate": {}};
            let freeRateMapSingle = new Map();
            if (ObjectUtils.isNotEmpty(freeRateSingleModel.childFreeRate)) {
                for (let [key, value] of freeRateSingleModel.childFreeRate) {
                    if (qfMajorTypeSetSingle.includes(key)) {
                        freeRateMapSingle.set(key, value);
                    }
                }
            }
            freeRateSingleModel.childFreeRate = freeRateMapSingle;
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));

            if (ObjectUtils.isNotEmpty(singleProject.parentId)) {
                await this.dealLevelSingleFee(constructId, singleProject.parentId, qfCode);
            }
        }
    }

    /**
     * 通过libraryCode查询取费文件
     * @param libraryCode
     * @returns {Promise<BaseFeeFileProject2022>}
     */
    async getFeeFilesByLibraryCode(libraryCode) {
        let result = await this.app.db.gongLiaoJiProject.manager
            .getRepository(BaseFeeFileProject2022)
            .find({
                where: {
                    libraryCode: libraryCode
                },
            })
        return result.find(qf => qf.code.includes("0"));
    }

    /**
     * 通过qfCode查询取费文件
     * @param qfCode
     * @returns {Promise<BaseFeeFileProject2022>}
     */
    async getFeeFilesByQfCode(qfCode) {
        return await this.app.db.gongLiaoJiProject.manager
            .getRepository(BaseFeeFileProject2022)
            .findOne({
                where: {
                    qfCode: qfCode
                },
            });
    }


    /**
     * 修改工程项目费率
     * @param freeRateModel
     *  constructId 工程项目id
     *  unitId 单位工程id
     *  libraryCode  定额库编码'
     *  manageFeeRate  企业管理费（%）'
     *  profitRate  利润（%）'
     *  taxRate  税金（%）'
     *  gfRate  规费（%）'
     *  anwenRate  安全生产、文明施工费（%）'
     */
    async updateProjectFreeRate(freeRateModel) {
        let {constructId, unitId, libraryCode, manageFeeRate, profitRate, taxRate, gfRate, anwenRate,qfCode} = freeRateModel;

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        // 获取费率工程项目id
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let cacheFreeProject = freeRateProjectModel.cacheFreeProject ?
            ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject) :
            ConvertUtil.deepCopy(freeRateProjectModel);
        let freeRate = cacheFreeProject.childFreeRate.get(qfCode);
        freeRate.manageFeeRate = ObjectUtils.isEmpty(manageFeeRate) ? freeRate.manageFeeRate : Number(manageFeeRate);
        if(manageFeeRate!=freeRate.manageFeeRate){
            freeRate.manageFeeRateUpdate = true;
        }

        freeRate.profitRate = ObjectUtils.isEmpty(profitRate) ? freeRate.profitRate : Number(profitRate);
        if(profitRate!=freeRate.profitRate){
            freeRate.profitRateUpdate = true;
        }

        freeRate.taxRate = ObjectUtils.isEmpty(taxRate) ? freeRate.taxRate : Number(taxRate);
        if(taxRate!=freeRate.taxRate){
            freeRate.taxRate = true;
        }

        freeRate.gfRate = ObjectUtils.isEmpty(gfRate) ? freeRate.gfRate : Number(gfRate);
        if(gfRate!=freeRate.gfRate){
            freeRate.gfRate = true;
        }

        freeRate.anwenRate = ObjectUtils.isEmpty(anwenRate) ? freeRate.anwenRate : Number(anwenRate);
        if(anwenRate!=freeRate.anwenRate){
            freeRate.anwenRate = true;
        }

        cacheFreeProject.childFreeRate.set(qfCode, freeRate);

        freeRateProjectModel.cacheFreeProject = cacheFreeProject;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        await this._updateFreeRateCompare(cacheFreeProject, freeRateUnitMap); //单位工程和工程项目对应费率 进行比较
        return Array.from(cacheFreeProject.childFreeRate.values());
    }


    /**
     * 修改工程项目费率
     * @param freeRateModel
     *  constructId 工程项目id
     *  unitId 单位工程id
     *  libraryCode  定额库编码'
     *  manageFeeRate  企业管理费（%）'
     *  profitRate  利润（%）'
     *  taxRate  税金（%）'
     *  gfRate  规费（%）'
     *  anwenRate  安全生产、文明施工费（%）'
     */
    async updateProjectFreeRateSingle(freeRateModel) {
        let {constructId, unitId, singleId, libraryCode, manageFeeRate, profitRate, taxRate, gfRate, anwenRate,qfCode} = freeRateModel;

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        // 获取费率工程项目id
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let freeKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
        let freeRateProjectModel = singleQfbMap.get(freeKey);

        let cacheFreeProject = freeRateProjectModel.cacheFreeProject ?
            ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject) :
            ConvertUtil.deepCopy(freeRateProjectModel);
        let freeRate = cacheFreeProject.childFreeRate.get(qfCode);
        freeRate.manageFeeRate = ObjectUtils.isEmpty(manageFeeRate) ? freeRate.manageFeeRate : Number(manageFeeRate);
        if(manageFeeRate!=freeRate.manageFeeRate){
            freeRate.manageFeeRateUpdate = true;
        }

        freeRate.profitRate = ObjectUtils.isEmpty(profitRate) ? freeRate.profitRate : Number(profitRate);
        if(profitRate!=freeRate.profitRate){
            freeRate.profitRateUpdate = true;
        }

        freeRate.taxRate = ObjectUtils.isEmpty(taxRate) ? freeRate.taxRate : Number(taxRate);
        if(taxRate!=freeRate.taxRate){
            freeRate.taxRate = true;
        }

        freeRate.gfRate = ObjectUtils.isEmpty(gfRate) ? freeRate.gfRate : Number(gfRate);
        if(gfRate!=freeRate.gfRate){
            freeRate.gfRate = true;
        }

        freeRate.anwenRate = ObjectUtils.isEmpty(anwenRate) ? freeRate.anwenRate : Number(anwenRate);
        if(anwenRate!=freeRate.anwenRate){
            freeRate.anwenRate = true;
        }

        cacheFreeProject.childFreeRate.set(qfCode, freeRate);

        freeRateProjectModel.cacheFreeProject = cacheFreeProject;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKey, freeRateProjectModel));

        await this._updateFreeRateCompare(cacheFreeProject, freeRateUnitMap); //单位工程和工程项目对应费率 进行比较
        return Array.from(cacheFreeProject.childFreeRate.values());
    }


    /**
     * 修改单位工程费率 工程专业或地区
     * @param args
     * libraryCode  定额库编码'
     * projectType  所属工程专业'
     * projectLocation  工程所在地'
     * roadSurfaceNum 临路面数
     * floorSpace 建筑面积
     * municipalConstructionCost 市政工程造价
     */
    async changeUnitTypeAndAreas(args) {
        let {
            constructId,
            unitId,
            projectType,
            projectLocation,
            roadSurfaceNum,
            floorSpace,
            municipalConstructionCost
        } = args;
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
        let libraryCode = unitProject.constructMajorType;
        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let freeRateModelList = ObjectUtils.getMapWithKeysStartingWith2(unitQfbMap, args.unitId);
        for (let item of freeRateModelList) {
            let param = {
                constructId: constructId,
                projectType: projectType,
                projectLocation: projectLocation,
                roadSurfaceNum: roadSurfaceNum,
                floorSpace: floorSpace,
                municipalConstructionCost: municipalConstructionCost,
                libraryCode: item.libraryCode,
            }
            let freeRate = await this.getBaseFreeRateGlj(param);
            freeRate.unitId = item.unitId;
            let freeKey = WildcardMap.generateKey(unitId, item.libraryCode);
            unitQfbMap.set(freeKey, freeRate);

            try {
                await this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, freeRate);
            } catch (e) {
                console.log("调用异常" + e.toString());
            }
        }
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
    }

    /**
     * 修改单位工程费率 工程专业或地区
     * @param freeRateModel
     * isSub 是否统一应用 到单位工程
     * projectType  所属工程专业'
     * projectLocation  工程所在地'
     * roadSurfaceNum 临路面数
     * floorSpace 建筑面积
     * municipalConstructionCost 市政工程造价
     */
    async changeProjectTypeAndAreas(args) {
        let {constructId, projectType, projectLocation, roadSurfaceNum, floorSpace, municipalConstructionCost} = args;
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let cacheFreeProject = freeRateProjectModel.cacheFreeProject ?
            ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject) :
            ConvertUtil.deepCopy(freeRateProjectModel);
        cacheFreeProject.projectType = projectType;
        cacheFreeProject.projectLocation = projectLocation;
        cacheFreeProject.roadSurfaceNum = roadSurfaceNum;
        cacheFreeProject.floorSpace = floorSpace;
        cacheFreeProject.municipalConstructionCost = municipalConstructionCost;

        for (let libraryCode of cacheFreeProject.childFreeRate.keys()) {
            let param = {
                constructId: constructId,
                projectType: projectType,
                projectLocation: projectLocation,
                roadSurfaceNum: roadSurfaceNum,
                floorSpace: floorSpace,
                municipalConstructionCost: municipalConstructionCost,
                libraryCode: libraryCode,
            }
            let freeRate = await this.getBaseFreeRateGlj(param);
            cacheFreeProject.childFreeRate.set(libraryCode, freeRate);
        }
        freeRateProjectModel.cacheFreeProject = cacheFreeProject;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        await this._updateFreeRateCompare(cacheFreeProject, freeRateUnitMap); //单位工程和工程项目对应费率 进行比较
        return Array.from(cacheFreeProject.childFreeRate.values());
    }

    /**
     * 修改单位工程费率 工程专业或地区
     * @param freeRateModel
     * isSub 是否统一应用 到单位工程
     * projectType  所属工程专业'
     * projectLocation  工程所在地'
     * roadSurfaceNum 临路面数
     * floorSpace 建筑面积
     * municipalConstructionCost 市政工程造价
     */
    async changeProjectTypeAndAreasSingle(args) {
        let {
            constructId,
            singleId,
            projectType,
            projectLocation,
            roadSurfaceNum,
            floorSpace,
            municipalConstructionCost
        } = args;
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);

        let freeKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
        let freeRateProjectModel = singleQfbMap.get(freeKey);

        let cacheFreeProject = freeRateProjectModel.cacheFreeProject ?
            ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject) :
            ConvertUtil.deepCopy(freeRateProjectModel);
        cacheFreeProject.projectType = projectType;
        cacheFreeProject.projectLocation = projectLocation;
        cacheFreeProject.roadSurfaceNum = roadSurfaceNum;
        cacheFreeProject.floorSpace = floorSpace;
        cacheFreeProject.municipalConstructionCost = municipalConstructionCost;

        for (let libraryCode of cacheFreeProject.childFreeRate.keys()) {
            let param = {
                constructId: constructId,
                projectType: projectType,
                projectLocation: projectLocation,
                roadSurfaceNum: roadSurfaceNum,
                floorSpace: floorSpace,
                municipalConstructionCost: municipalConstructionCost,
                libraryCode: libraryCode,
            }
            let freeRate = await this.getBaseFreeRateGlj(param);
            cacheFreeProject.childFreeRate.set(libraryCode, freeRate);
        }
        freeRateProjectModel.cacheFreeProject = cacheFreeProject;
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKey, freeRateProjectModel));

        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        await this._updateFreeRateCompare(cacheFreeProject, freeRateUnitMap); //单位工程和工程项目对应费率 进行比较
        return Array.from(cacheFreeProject.childFreeRate.values());
    }

    /**
     * 获取工程项目费率表
     * @param args
     */
    async getProjectQfbList(args) {
        let constructId = args.constructId;
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
        //和自己默认费率说明对比
        await this._updateFreeRateCompareInit(args,freeRateProjectModel);
        if(ObjectUtils.isEmpty(freeRateProjectModel.childFreeRate)){
            return  [];
        }
        // 获取工程项目取费表
        return Array.from(freeRateProjectModel.childFreeRate.values());
    }

    /**
     * 获取工程项目费率表
     * @param args
     */
    async getProjectQfbListSingle(args) {
        let constructId = args.constructId;
        let singleId = args.singleId;
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let freeKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
        let freeRateSingleModel = freeRateProjectModel.get(freeKey);

        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2Single(constructId,singleId);
        //和自己默认费率说明对比
        await this._updateFreeRateCompareInit(args,freeRateSingleModel);
        if(ObjectUtils.isEmpty(freeRateSingleModel) || ObjectUtils.isEmpty(freeRateSingleModel.childFreeRate)){
             return  [];
        }
        // 获取工程项目取费表
        return Array.from(freeRateSingleModel.childFreeRate.values());
    }


    /**
     * 获取单位工程费率表
     * @param args
     */
    async getUnitQfbList(args) {
        let {constructId, unitId} = args;
        //单位工程和工程项目对应费率 进行比较
        await this._updateFreeRateCompare2(constructId);
        //和自己默认费率说明对比
        let freeRateUintModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        await this._updateFreeRateCompareInit(args,freeRateUintModel);
        // 获取单位工程取费表
        let mapWithKeysStartingWith2 = ObjectUtils.getMapWithKeysStartingWith2(ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB), unitId);
        return mapWithKeysStartingWith2;
    }

    /**
     * 单位工程和工程项目对应费率 进行比较
     */
    async _updateFreeRateCompare2(constructId) {
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        await this._updateFreeRateCompare(freeRateProjectModel, freeRateUnitMap); //单位工程和工程项目对应费率 进行比较
    }

    /**
     * 单位工程和工程项目对应费率 进行比较
     */
    async _updateFreeRateCompareInit(args, freeRateProjectModel) {
        let taxCalculationMethod = ProjectDomain.getDomain(args.constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        switch (Number(args.type)) {
            case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
                let freeRateProjectFlsm = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_FLSM);
                if (ObjectUtils.isNotEmpty(freeRateProjectModel.childFreeRate)) {
                    for (const [key, value] of freeRateProjectModel.childFreeRate) {
                        // let param = {};
                        // //获取工程项目的费率说明
                        // param.libraryCode = value.libraryCode;
                        // param.constructId = args.constructId;
                        // param.singleId = null;
                        // param.unitId = null;
                        // param.projectType = freeRateProjectFlsm.projectType;
                        // param.projectLocation = freeRateProjectFlsm.projectLocation;
                        // param.roadSurfaceNum = freeRateProjectFlsm.roadSurfaceNum;
                        // param.floorSpace = freeRateProjectFlsm.floorSpace;
                        // param.municipalConstructionCost = freeRateProjectFlsm.municipalConstructionCost;
                        // param.taxCalculationMethod = taxCalculationMethod;
                        // let freeRate = await this.service.gongLiaoJiProject.gljInitUnitProjectService.getFeeData(param);

                        args.libraryCode=value.libraryCode;
                        args.type=ProjectTypeConstants.PROJECT_TYPE_PROJECT;
                        args.freeFileOld=value;
                        args.qfCode=value.qfCode;
                        let freeRate = await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateData(args);

                        let diffFreeRateInit = [];
                        if(value.manageFeeRate!=freeRate.manageFeeRate){
                            diffFreeRateInit.push(FreeRateModel.MANAGE_FEE_RATE);
                        }
                        if(value.profitRate!=freeRate.profitRate){
                            diffFreeRateInit.push(FreeRateModel.PROFIT_RATE);
                        }
                        if(value.taxRate!=freeRate.taxRate){
                            diffFreeRateInit.push(FreeRateModel.TAX_RATE);
                        }
                        if(value.anwenRate!=freeRate.anwenRate){
                            diffFreeRateInit.push(FreeRateModel.ANWEN_RATE);
                        }
                        value.diffFreeRateInit = diffFreeRateInit;
                    }
                }
                break;
            case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
                let freeRateProjectFlsm1 = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
                let singleFeeKey = WildcardMap.generateKey(args.singleId, FunctionTypeConstants.SINGLE_FLSM);
                let freeRateSingleFlsm = freeRateProjectFlsm1.get(singleFeeKey);
                if (ObjectUtils.isNotEmpty(freeRateProjectModel.childFreeRate)) {
                    for (const [key, value] of freeRateProjectModel.childFreeRate) {
                        // let param = {};
                        // //获取工程项目的费率说明
                        // param.libraryCode = value.libraryCode;
                        // param.constructId = args.constructId;
                        // param.singleId = args.singleId;
                        // param.unitId = null;
                        // param.projectType = freeRateSingleFlsm.projectType;
                        // param.projectLocation = freeRateSingleFlsm.projectLocation;
                        // param.roadSurfaceNum = freeRateSingleFlsm.roadSurfaceNum;
                        // param.floorSpace = freeRateSingleFlsm.floorSpace;
                        // param.municipalConstructionCost = freeRateSingleFlsm.municipalConstructionCost;
                        // param.taxCalculationMethod = taxCalculationMethod;
                        // let freeRate = await this.service.gongLiaoJiProject.gljInitUnitProjectService.getFeeData(param);

                        args.libraryCode=value.libraryCode;
                        args.type=ProjectTypeConstants.PROJECT_TYPE_SINGLE;
                        args.freeFileOld=value;
                        args.qfCode=value.qfCode;
                        let freeRate = await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateData(args);

                        let diffFreeRateInit = [];
                        if(value.manageFeeRate!=freeRate.manageFeeRate){
                            diffFreeRateInit.push(FreeRateModel.MANAGE_FEE_RATE);
                        }
                        if(value.profitRate!=freeRate.profitRate){
                            diffFreeRateInit.push(FreeRateModel.PROFIT_RATE);
                        }
                        if(value.taxRate!=freeRate.taxRate){
                            diffFreeRateInit.push(FreeRateModel.TAX_RATE);
                        }
                        if(value.anwenRate!=freeRate.anwenRate){
                            diffFreeRateInit.push(FreeRateModel.ANWEN_RATE);
                        }
                        value.diffFreeRateInit = diffFreeRateInit;
                    }
                }
                break;
            case ProjectTypeConstants.PROJECT_TYPE_UNIT:
                let freeRateProjectFlsm2 = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM);
                let freeRateUnitFlsm = freeRateProjectFlsm2.get(args.unitId);
                for (const [key, value] of freeRateProjectModel) {
                    // let param = {};
                    // //获取工程项目的费率说明
                    // param.libraryCode = value.libraryCode;
                    // param.constructId = args.constructId;
                    // param.singleId = args.singleId;
                    // param.unitId = null;
                    // param.projectType = freeRateUnitFlsm.projectType;
                    // param.projectLocation = freeRateUnitFlsm.projectLocation;
                    // param.roadSurfaceNum = freeRateUnitFlsm.roadSurfaceNum;
                    // param.floorSpace = freeRateUnitFlsm.floorSpace;
                    // param.municipalConstructionCost = freeRateUnitFlsm.municipalConstructionCost;
                    // param.taxCalculationMethod = taxCalculationMethod;
                    // let freeRate = await this.service.gongLiaoJiProject.gljInitUnitProjectService.getFeeData(param);

                    args.libraryCode=value.libraryCode;
                    args.type=ProjectTypeConstants.PROJECT_TYPE_UNIT;
                    args.freeFileOld=value;
                    args.qfCode=value.qfCode;
                    let freeRate = await this.service.gongLiaoJiProject.gljBaseFreeRateService.getBaseFreeRateData(args);

                    let diffFreeRateInit = [];
                    if (value.manageFeeRate != freeRate.manageFeeRate) {
                        diffFreeRateInit.push(FreeRateModel.MANAGE_FEE_RATE);
                    }
                    if (value.profitRate != freeRate.profitRate) {
                        diffFreeRateInit.push(FreeRateModel.PROFIT_RATE);
                    }
                    if (value.taxRate != freeRate.taxRate) {
                        diffFreeRateInit.push(FreeRateModel.TAX_RATE);
                    }
                    if (value.anwenRate != freeRate.anwenRate) {
                        diffFreeRateInit.push(FreeRateModel.ANWEN_RATE);
                    }
                    value.diffFreeRateInit = diffFreeRateInit;
                }
                break;
            default:
        }

    }

    /**
     * 单位工程和工程项目对应费率 进行比较
     */
    async _updateFreeRateCompare2Single(constructId,singleId) {
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);

        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let freeKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
        let freeRateSingleModel = freeRateProjectModel.get(freeKey).childFreeRate;

        await this._updateFreeRateCompare( freeRateUnitMap,freeRateSingleModel); //单位工程和单项对应费率 进行比较
    }


    /**
     * 单位工程和工程项目对应费率 进行比较
     */
    async _updateFreeRateCompare(freeRateProjectModel, freeRateUnitMap) {
        if(ObjectUtils.isEmpty(freeRateProjectModel) ||ObjectUtils.isEmpty(freeRateUnitMap)  ||  ObjectUtils.isEmpty(freeRateProjectModel.childFreeRate)){
            return ;
        }
        for (let qfCode of freeRateProjectModel.childFreeRate.keys()) {
            let projectFreeRate = freeRateProjectModel.childFreeRate.get(qfCode);
            let diffProjectFreeRate = [];
            for (let freeKey of freeRateUnitMap.keys()) {
                let unitfreeRate = freeRateUnitMap.get(freeKey);
                let diffUnitFreeRate = [];
                if (qfCode === unitfreeRate.qfCode) {
                    if (!(projectFreeRate.manageFeeRate === unitfreeRate.manageFeeRate)) {
                        diffUnitFreeRate.push(FreeRateModel.MANAGE_FEE_RATE);
                        diffProjectFreeRate.push(FreeRateModel.MANAGE_FEE_RATE);
                    }
                    if (!(projectFreeRate.profitRate === unitfreeRate.profitRate)) {
                        diffUnitFreeRate.push(FreeRateModel.PROFIT_RATE);
                        diffProjectFreeRate.push(FreeRateModel.PROFIT_RATE);
                    }
                    if (!(projectFreeRate.taxRate === unitfreeRate.taxRate)) {
                        diffUnitFreeRate.push(FreeRateModel.TAX_RATE);
                        diffProjectFreeRate.push(FreeRateModel.TAX_RATE);
                    }
                    if (!(projectFreeRate.gfRate === unitfreeRate.gfRate)) {
                        diffUnitFreeRate.push(FreeRateModel.GF_RATE);
                        diffProjectFreeRate.push(FreeRateModel.GF_RATE);
                    }
                    if (!(projectFreeRate.anwenRate === unitfreeRate.anwenRate)) {
                        diffUnitFreeRate.push(FreeRateModel.ANWEN_RATE);
                        diffProjectFreeRate.push(FreeRateModel.ANWEN_RATE);
                    }
                    unitfreeRate.diffFreeRate = diffUnitFreeRate.filter((value, index, arr) => arr.indexOf(value) === index);
                }
            }
            projectFreeRate.diffFreeRate = diffProjectFreeRate.filter((value, index, arr) => arr.indexOf(value) === index);
        }
    }

    feeDescribeToModel(freeRateModelList){
        let freeRateDescribeModel={};
        for(const frm of freeRateModelList){
            if(frm.field=="projectType"){
                freeRateDescribeModel.projectType=frm.default;
            }
            if(frm.field=="projectLocation"){
                freeRateDescribeModel.projectLocation=frm.default;
            }
            if(frm.field=="roadSurfaceNum"){
                freeRateDescribeModel.roadSurfaceNum=frm.default;
            }
            if(frm.field=="floorSpace"){
                freeRateDescribeModel.floorSpace=frm.default;
            }
            if(frm.field=="municipalConstructionCost"){
                freeRateDescribeModel.municipalConstructionCost=frm.default;
            }
        }
        return freeRateDescribeModel;
    }


    /**
     * 统一应用工程项目
     * @param args
     */
    async unifiedApplication(args) {

        let {constructId,singleId,freeRateDescribe,freeFileList} = args;

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        let freeRateDescribeModel=this.feeDescribeToModel(freeRateDescribe);
        let constructorQfb = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let constructorFlsm = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_FLSM);
        if(ObjectUtils.isEmpty(constructorQfb.childFreeRate)){  constructorQfb.childFreeRate=[]}
        let oldFeeFileList = Array.from(constructorQfb.childFreeRate.values()); ;

        //更新取费说明
        this.updateFeeDescribe(constructorFlsm,freeRateDescribeModel);
        this.updateFeeFileRate(oldFeeFileList,freeFileList,precision);

        //获取下挂的所有单项  更新单位数据
        let singleList = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectSingleAll(constructId);
        await this.updateAllSingleData(singleList,constructId,freeRateDescribeModel,freeFileList);

        let unitlist = await this.service.gongLiaoJiProject.gljProjectCommonService.getProjectOneUnitAll(constructId);
        if (ObjectUtils.isEmpty(singleList) && ObjectUtils.isNotEmpty(unitlist)) {
            await this.updateAllUnitData(unitlist,constructId,freeRateDescribeModel,freeFileList);
        }

        // let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        // let cacheFreeProject = freeRateProjectModel.cacheFreeProject ?
        //     ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject) :
        //     ConvertUtil.deepCopy(freeRateProjectModel);
        // ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, cacheFreeProject);
        //
        // let freeRateUnitModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        // for (let libraryCode of cacheFreeProject.childFreeRate.keys()) {
        //     let projectFreeRate = cacheFreeProject.childFreeRate.get(libraryCode);
        //     for (let freeKey of freeRateUnitModel.keys()) {
        //         let unitfreeRate = freeRateUnitModel.get(freeKey);
        //         if (libraryCode === unitfreeRate.libraryCode) {
        //             unitfreeRate.projectType = projectFreeRate.projectType;
        //             unitfreeRate.projectLocation = projectFreeRate.projectLocation;
        //             unitfreeRate.roadSurfaceNum = projectFreeRate.roadSurfaceNum;
        //             unitfreeRate.floorSpace = projectFreeRate.floorSpace;
        //             unitfreeRate.municipalConstructionCost = projectFreeRate.municipalConstructionCost;
        //             unitfreeRate.manageFeeRate = projectFreeRate.manageFeeRate;
        //             unitfreeRate.profitRate = projectFreeRate.profitRate;
        //             unitfreeRate.taxRate = projectFreeRate.taxRate;
        //             unitfreeRate.gfRate = projectFreeRate.gfRate;
        //             unitfreeRate.anwenRate = projectFreeRate.anwenRate;
        //             let unitId = freeKey.split('--')[0]
        //             await this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, unitfreeRate);
        //         }
        //     }
        // }
    }


    /**
     * 统一应用--单项工程
     * @param args
     */
    async unifiedApplicationSingle(args) {
        let {constructId,singleId,freeRateDescribe,freeFileList} = args
        let freeRateDescribeModel=this.feeDescribeToModel(freeRateDescribe);
        //获取选中对象
        let singleObj = ProjectDomain.getDomain(constructId).getProjectTree().find(item => item.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE &&  item.sequenceNbr === singleId);
        //获取所有的单项
        let singleProjects = new Array();
        singleProjects.push(singleObj);
        PricingGSUtils.getSingleProjectsByCurrentNode(singleObj, singleProjects);

        await this.updateAllSingleData(singleProjects,constructId,freeRateDescribeModel,freeFileList);
        // //获取所有的单位
        // let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        // let cacheFreeProject = freeRateProjectModel.cacheFreeProject ?
        //     ConvertUtil.deepCopy(freeRateProjectModel.cacheFreeProject) :
        //     ConvertUtil.deepCopy(freeRateProjectModel);
        // ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, cacheFreeProject);
        //
        // let freeRateUnitModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        // for (let libraryCode of cacheFreeProject.childFreeRate.keys()) {
        //     let projectFreeRate = cacheFreeProject.childFreeRate.get(libraryCode);
        //     for (let freeKey of freeRateUnitModel.keys()) {
        //         let unitfreeRate = freeRateUnitModel.get(freeKey);
        //         if (libraryCode === unitfreeRate.libraryCode) {
        //             unitfreeRate.projectType = projectFreeRate.projectType;
        //             unitfreeRate.projectLocation = projectFreeRate.projectLocation;
        //             unitfreeRate.roadSurfaceNum = projectFreeRate.roadSurfaceNum;
        //             unitfreeRate.floorSpace = projectFreeRate.floorSpace;
        //             unitfreeRate.municipalConstructionCost = projectFreeRate.municipalConstructionCost;
        //             unitfreeRate.manageFeeRate = projectFreeRate.manageFeeRate;
        //             unitfreeRate.profitRate = projectFreeRate.profitRate;
        //             unitfreeRate.taxRate = projectFreeRate.taxRate;
        //             unitfreeRate.gfRate = projectFreeRate.gfRate;
        //             unitfreeRate.anwenRate = projectFreeRate.anwenRate;
        //             let unitId = freeKey.split('--')[0]
        //             await this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, unitfreeRate);
        //         }
        //     }
        // }
    }


    async updateAllSingleData(singleList,constructId,freeRateDescribeModel,freeFileList){

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        //给单项单位重新赋值
        for(const sg of singleList){
              //获取单项的取费说明 、取费文件
            let freeRateProjectQfb = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
            let freeRateProjectFlsm = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
            let freeKey = WildcardMap.generateKey(sg.sequenceNbr, FunctionTypeConstants.SINGLE_FLSM);
            let freeRateSingleModel = freeRateProjectFlsm.get(freeKey);

            let freeKeySingle = WildcardMap.generateKey(sg.sequenceNbr, FunctionTypeConstants.SINGLE_QFB);
            let freeRateSingleQfb = freeRateProjectQfb.get(freeKeySingle);
            if(ObjectUtils.isEmpty(freeRateSingleQfb.childFreeRate)){  freeRateSingleQfb.childFreeRate=[]}
            let oldFeeFileList = freeRateSingleQfb.childFreeRate.values();
            oldFeeFileList = Array.from(oldFeeFileList);
            //更新取费说明
            this.updateFeeDescribe(freeRateSingleModel,freeRateDescribeModel);
            this.updateFeeFileRate(oldFeeFileList,freeFileList,precision);

              //获取下挂的所有单位  更新单位数据
            let unitList = await this.service.gongLiaoJiProject.gljProjectCommonService.getSingleUnitAll(constructId,sg.sequenceNbr);
            for(const unit of unitList){
                let unitQfb = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
                let unitFlsm = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM);
                let oldUnitFeeFile = Array.from(unitQfb.values()).filter(unitQfb=>unitQfb.unitId==unit.sequenceNbr);
                let oldUnitfreeDes = unitFlsm.get(unit.sequenceNbr);
                //更新取费说明
                this.updateFeeDescribe(oldUnitfreeDes,freeRateDescribeModel);
                this.updateFeeFileRate(oldUnitFeeFile,freeFileList,precision);

                for(const fee of oldUnitFeeFile){
                    //重新计算费用汇总
                    this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unit.sequenceNbr, fee);
                }
            }
        }
    }

    async updateAllUnitData(unitList, constructId, freeRateDescribeModel, freeFileList) {
        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        for (const unit of unitList) {
            let unitQfb = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
            let unitFlsm = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM);
            let oldUnitFeeFile = Array.from(unitQfb.values()).filter(unitQfb => unitQfb.unitId == unit.sequenceNbr);
            let oldUnitfreeDes = unitFlsm.get(unit.sequenceNbr);
            //更新取费说明
            this.updateFeeDescribe(oldUnitfreeDes, freeRateDescribeModel);
            this.updateFeeFileRate(oldUnitFeeFile, freeFileList, precision);

            for (const fee of oldUnitFeeFile) {
                //重新计算费用汇总
                this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unit.sequenceNbr, fee);
            }
        }
    }


    /**
     * 费率说明
     * @param oldFeeDescribe
     * @param oldFeeFile
     * @param newFeeDescribe
     * @param newFeeFile
     */
    updateFeeDescribe(oldFeeDescribe,newFeeDescribe){
        oldFeeDescribe.projectType = newFeeDescribe.projectType;
        oldFeeDescribe.projectLocation = newFeeDescribe.projectLocation;
        oldFeeDescribe.roadSurfaceNum = newFeeDescribe.roadSurfaceNum;
        oldFeeDescribe.floorSpace = newFeeDescribe.floorSpace;
        oldFeeDescribe.municipalConstructionCost = newFeeDescribe.municipalConstructionCost;
    }

    /**
     * 取费文件
     * @param oldFeeDescribe
     * @param oldFeeFile
     * @param newFeeDescribe
     * @param newFeeFile
     */
    updateFeeFileRate(oldFeeFileList,newFeeFileList,precision){
         for(const nf of newFeeFileList){
             for(const off of oldFeeFileList){
                 if(nf.qfCode==off.qfCode){
                     off.manageFeeRate = parseFloat(nf.manageFeeRate);
                     off.profitRate = parseFloat(nf.profitRate);
                     off.taxRate = parseFloat(nf.taxRate);
                     off.anwenRate = parseFloat(nf.anwenRate);

                     off.manageFeeRateUpdate = ObjectUtils.isNotEmpty(nf.manageFeeRateUpdate) ? nf.manageFeeRateUpdate : false;
                     off.profitRateUpdate = ObjectUtils.isNotEmpty(nf.profitRateUpdate) ? nf.profitRateUpdate : false;
                     off.taxRateUpdate = ObjectUtils.isNotEmpty(nf.taxRateUpdate) ? nf.taxRateUpdate : false;
                     off.anwenRateUpdate = ObjectUtils.isNotEmpty(nf.anwenRateUpdate) ? nf.anwenRateUpdate : false;
                 }
             }
         }
    }


    /**
     * 导入项目后费率恢复
     * @param constructId
     */
    afterImportFreeInit(constructId) {
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let freeRateUnitMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);

        freeRateProjectModel = ObjectUtils.copyProp(freeRateProjectModel, new FreeRateProjectModel());
        freeRateProjectModel.childFreeRate = ObjectUtils.convertObjectToMap(freeRateProjectModel.childFreeRate);
        for (let [key, value] of freeRateProjectModel.childFreeRate) {
            freeRateProjectModel.childFreeRate.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
        }
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);

        freeRateUnitMap = ObjectUtils.convertObjectToMap(freeRateUnitMap);
        for (let [key, value] of freeRateUnitMap) {
            freeRateUnitMap.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
        }
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, freeRateUnitMap);
    }

    /**
     * 导入项目后，费率统一应用
     * @param constructId
     * @param unitIdList
     * @returns {Promise<void>}
     */
    async afterImportUnifiedApplication(constructId, unitIdList) {
        let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
        let freeRateUnitModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        if (ObjectUtils.isEmpty(unitIdList) || unitIdList.length < 1) {
            return;
        }
        for (let libraryCode of freeRateProjectModel.childFreeRate.keys()) {
            let projectFreeRate = freeRateProjectModel.childFreeRate.get(libraryCode);
            for (let unitId of unitIdList) {
                let freeRateUnitModels = ObjectUtils.getMapWithKeysStartingWith(freeRateUnitModel, unitId);
                for (const [key, unitfreeRate] of freeRateUnitModels) {
                    if (ObjectUtils.isNotEmpty(key) && libraryCode === unitfreeRate.libraryCode) {
                        unitfreeRate.projectType = projectFreeRate.projectType;
                        unitfreeRate.projectLocation = projectFreeRate.projectLocation;
                        unitfreeRate.roadSurfaceNum = projectFreeRate.roadSurfaceNum;
                        unitfreeRate.floorSpace = projectFreeRate.floorSpace;
                        unitfreeRate.municipalConstructionCost = projectFreeRate.municipalConstructionCost;
                        unitfreeRate.manageFeeRate = projectFreeRate.manageFeeRate;
                        unitfreeRate.profitRate = projectFreeRate.profitRate;
                        unitfreeRate.taxRate = projectFreeRate.taxRate;
                        unitfreeRate.gfRate = projectFreeRate.gfRate;
                        unitfreeRate.anwenRate = projectFreeRate.anwenRate;
                        this.service.gongLiaoJiProject.gljUnitCostSummaryService.updateUnitCostSummaryRate(constructId, null, unitId, unitfreeRate);
                    }
                }
            }
        }
    }


    /**
     * 导入项目后费率恢复
     * @param freeRateProjectModel
     */
    transProjectQfb(freeRateProjectModel) {
        freeRateProjectModel = ObjectUtils.copyProp(freeRateProjectModel, new FreeRateProjectModel());
        if (ObjectUtils.isNotEmpty(freeRateProjectModel.childFreeRate)) {
            freeRateProjectModel.childFreeRate = ObjectUtils.convertObjectToMap(freeRateProjectModel.childFreeRate);
            for (let [key, value] of freeRateProjectModel.childFreeRate) {
                freeRateProjectModel.childFreeRate.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
            }
        }

        return freeRateProjectModel;
    }

    /**
     * 导入项目后费率恢复
     * @param freeRateProjectModelMap
     */
    transSingleQfb(freeRateProjectModelMap) {
        freeRateProjectModelMap = ObjectUtils.convertObjectToMap(freeRateProjectModelMap);
        for (let [key, value] of freeRateProjectModelMap) {
            value.childFreeRate = ObjectUtils.convertObjectToMap(value.childFreeRate);
            for (let [key1, value1] of value.childFreeRate) {
                value.childFreeRate.set(key1, ObjectUtils.copyProp(value1, new FreeRateModel()))
            }

            freeRateProjectModelMap.set(key, ObjectUtils.copyProp(value, new FreeRateProjectModel()));
        }
        return freeRateProjectModelMap;
        // for (let freeRateProjectModel of freeRateProjectModelMap) {
        //     freeRateProjectModel = ObjectUtils.copyProp(freeRateProjectModel, new FreeRateProjectModel());
        //     freeRateProjectModel.childFreeRate = ObjectUtils.convertObjectToMap(freeRateProjectModel.childFreeRate);
        //     for (let [key, value] of freeRateProjectModel.childFreeRate) {
        //         freeRateProjectModel.childFreeRate.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
        //     }
        // }
        // return freeRateProjectModelMap;
    }

    transSingleQfb1(freeRateProjectModelMap) {
        freeRateProjectModelMap = ObjectUtils.convertObjectToMap(freeRateProjectModelMap);
        for (let [key, value] of freeRateProjectModelMap) {
            freeRateProjectModelMap.set(key, ObjectUtils.copyProp(value, new FreeRateProjectModel()));
        }
        return freeRateProjectModelMap;
        // for (let freeRateProjectModel of freeRateProjectModelMap) {
        //     freeRateProjectModel = ObjectUtils.copyProp(freeRateProjectModel, new FreeRateProjectModel());
        //     freeRateProjectModel.childFreeRate = ObjectUtils.convertObjectToMap(freeRateProjectModel.childFreeRate);
        //     for (let [key, value] of freeRateProjectModel.childFreeRate) {
        //         freeRateProjectModel.childFreeRate.set(key, ObjectUtils.copyProp(value, new FreeRateModel()))
        //     }
        // }
        // return freeRateProjectModelMap;
    }

    /**
     * 导入项目后单位工程费率恢复
     * @param freeRateUnitMap
     */
    transUnitQfb(freeRateUnitMap) {
        freeRateUnitMap = ObjectUtils.convertObjectToMap(freeRateUnitMap);
        for (let [key, value] of freeRateUnitMap) {
            let copyProp = ObjectUtils.copyProp(value, new FreeRateModel());
            if (ObjectUtils.isNotEmpty(value.manageFeeRateUpdate)) {
                copyProp.manageFeeRateUpdate = value.manageFeeRateUpdate;
            }
            if (ObjectUtils.isNotEmpty(value.profitRateUpdate)) {
                copyProp.profitRateUpdate = value.profitRateUpdate;
            }
            if (ObjectUtils.isNotEmpty(value.taxRateUpdate)) {
                copyProp.taxRateUpdate = value.taxRateUpdate;
            }
            if (ObjectUtils.isNotEmpty(value.gfRateUpdate)) {
                copyProp.gfRateUpdate = value.gfRateUpdate;
            }
            if (ObjectUtils.isNotEmpty(value.anwenRateUpdate)) {
                copyProp.anwenRateUpdate = value.anwenRateUpdate;
            }
            freeRateUnitMap.set(key, copyProp);
        }
        return freeRateUnitMap;
    }



}

GljFreeRateService.toString = () => '[class GljFreeRateService]';
module.exports = GljFreeRateService;
