<!--
 * @Descripttion: 人材机索引
 * @Author: liuxia
 * @Date: 2023-05-25 17:52:23
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-03-20 10:16:45
-->
<template>
  <div>
    <common-modal
      className="dialog-comm"
      v-model:modelValue="props.indexVisible"
      :title="dialogTitle"
      :mask="false"
      :lockView="false"
      :lockScroll="false"
      :destroy-on-close="true"
      width="auto"
      @cancel="close"
      @close="close"
      v-bind="props.modalProps || {}"
    >
      <div class="dialog-wrap">
        <div class="contentCenter">
          <div class="left">
            <p class="head">
              <a-radio-group v-model:value="value1" button-style="solid">
                <a-radio-button value="dingE">人材机</a-radio-button>
              </a-radio-group>
            </p>
            <div class="search">
              <IndexSearch
                v-model:value="materialName"
                type="renCJ"
                @search="onSearch"
              ></IndexSearch>
              <a-select
                v-model:value="selectValue"
                style="width: 95%; margin: 0 8px 10px"
                :options="selectOptions"
                placeholder="请选择"
                :field-names="{
                  label: 'libraryName',
                  value: 'libraryCode',
                }"
                @change="handleChange"
              ></a-select>
              <div class="tree" v-if="treeData.length > 0">
                <a-tree
                  v-model:expandedKeys="expandedKeys"
                  v-model:selectedKeys="selectedKeys"
                  :tree-data="treeData"
                  @select="selectChildren"
                  style="z-index: 10"
                  :field-names="{
                    title: 'materialName',
                    children: 'childrenList',
                    key: 'key',
                  }"
                >
                  <template #icon="{ key }">
                    <template v-if="key !== '0-0'"></template>
                  </template>
                </a-tree>
              </div>
            </div>
          </div>
          <div class="right">
            <p class="btns">
              <a-radio-group v-model:value="value2" button-style="solid">
                <a-radio-button value="list">人材机列表</a-radio-button>
              </a-radio-group>
              <span>
                <a-button
                  v-if="!isFrRcjSummary"
                  type="primary"
                  class="btnNo1"
                  @click="updateCurrentInfo(1)"
                  :disabled="optionDisabled"
                  >插入</a-button
                >
                <a-button
                  type="primary"
                  :disabled="optionDisabled"
                  @click="updateCurrentInfo(2)"
                  >替换</a-button
                >
              </span>
            </p>
            <div class="table">
              <vxe-table
                align="center"
                :column-config="{ resizable: true }"
                ref="vexTable"
                class="table-scrollbar"
                :data="tableData"
                height="auto"
                :row-config="{
                  isCurrent: true,
                  keyField: 'sequenceNbr',
                }"
                :scroll-y="{ enabled: true, gt: 0 }"
                @current-change="currentChangeEvent"
                @cell-dblclick="
                  cellData => {
                    useCellDBLClickEvent(
                      cellData,
                      vexTable,
                      'materialMachineIndex',
                      cellDBLClickEvent
                    );
                  }
                "
                show-overflow="title"
              >
                <vxe-column width="45" title="序号">
                  <template #default="{ row }">
                    <div class="multiple-select">
                      {{ row.seq }}
                    </div>
                  </template>
                </vxe-column>
                <vxe-column field="materialCode" width="70" title="材料编码">
                </vxe-column>
                <vxe-column field="materialName" align="left" title="材料名称">
                </vxe-column>
                <vxe-column
                  field="specification"
                  align="left"
                  width="120"
                  title="规格型号"
                >
                </vxe-column>
                <vxe-column field="unit" width="50" title="单位"> </vxe-column>
                <vxe-column
                  field="dePrice"
                  width="80"
                  align="right"
                  :title="
                    store.deStandardReleaseYear === '12'
                      ? '定额价'
                      : store.taxMade === 1
                      ? '不含税基期价'
                      : '含税基期价'
                  "
                >
                </vxe-column>
                <vxe-column
                  field="taxRemoval"
                  width="80"
                  align="right"
                  title="除税系数%"
                  v-if="store.deStandardReleaseYear !== '22'"
                >
                </vxe-column>
              </vxe-table>
            </div>
          </div>
        </div>
      </div>
    </common-modal>
    <common-modal
      title="单位换算系数"
      width="554px"
      v-model:modelValue="unitVisible"
      className="dialog-comm"
    >
      <div class="dialog-content">
        <div class="init-text">
          替换的资源和当前资源单位不同，是否继续替换？
        </div>
        <div class="init-text">
          单位换算系数&nbsp;<span>{{ currentMaterialInfo?.unit }}</span
          ><a-input
            v-model:value="conversionCoefficient"
            @keyup="
              conversionCoefficient = (conversionCoefficient.match(
                /\d{0,8}(\.\d{0,2}|100)?/
              ) || [''])[0]
            "
          /><span>{{ currentInfo?.unit }}</span>
        </div>
      </div>
      <div class="footer-btn-list">
        <a-button @click="unitClose">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import { reactive, ref, toRefs, watch, computed, nextTick } from 'vue';
import api from '../../../../api/projectDetail';
import loadPriceApi from '../../../../api/loadPrice';
import { projectDetailStore } from '../../../../store/projectDetail';
import { useCellDBLClickEvent } from '@/hooks/useCellClick';
import { getDeType, disposeDeTypeData } from '@/hooks/publicApiData';
import infoMode from '@/plugins/infoMode.js';
import { useVirtualList } from '@/hooks/useVirtualList';
import IndexSearch from '../inventoryAndQuotaIndex/IndexSearch.vue';

import { message } from 'ant-design-vue';
const value1 = ref('dingE');
const value2 = ref('list');
const props = defineProps([
  'indexVisible',
  'currentMaterialInfo',
  'indexLoading',
  'pageFr',
  'modalProps',
]);
/**
 * 是否来源人材机汇总页面
 */
const isFrRcjSummary = computed(() => {
  return props.pageFr === 'rcjSummary';
});
const emits = defineEmits([
  'currentQdDeInfo',
  'update:indexVisible',
  'addChildrenRcjData',
]);
const store = projectDetailStore();
let searchKey = ref();
let selectValue = ref();
let selectOptions = ref([]);
let expandedKeys = ref([]);
let queryForm = reactive({
  libraryCode: '',
  level1: '',
  level2: '',
  level3: '',
  level4: '',
  level5: '',
  level6: '',
  level7: '',
  materialName: '',
});

let { materialName } = { ...toRefs(queryForm) };

const selectedKeys = ref([]);
let currentTreeInfo = ref();
let treeData = ref([]);
let currentInfo = ref();
let vexTable = ref();
let scrollSwitch = ref(false);
let page = ref(1);
let limit = ref(300000);
const dialogTitle = ref('人材机索引');
let conversionCoefficient = ref('');
let unitVisible = ref(false);
let isSearch = ref(false); // 是否为点击搜索按钮查询数据
const optionDisabled = computed(() => {
  if (!currentInfo.value || props.indexLoading) return true;
  if (isFrRcjSummary.value) {
    // 人材机汇总页面所需拦截
    // 费用人材机置灰
    return (
      props.currentMaterialInfo?.isFyrcj == 0 ||
      currentInfo.value?.isFyrcj === 0
    );
  }
  return false;
});
watch(
  () => props.indexVisible,
  () => {
    if (props.indexVisible) {
      queryDeLibrary();
    }
  }
);

// const {
//   initVirtual,
//   getScroll,
//   renderedList,
//   init,
//   EnterType,
//   scrollToPosition,
//   resetIsAdd,
// } = useVirtualList();

/**
 * 虚拟滚动处理
 * @param {*} type
 * @param {*} posId
 */
const virtualListHandler = () => {
  // resetIsAdd();
  // initVirtual(vexTable.value);
  // const initList = init(tableData.value);
  setTimeout(() => {
    // initList();
    const posId = currentInfo.value?.sequenceNbr;
    console.log('currentInfo', currentInfo.value);
    if (posId && props.currentMaterialInfo?.standardId) {
      let currentObj = tableData.value.find(
        x =>
          x.materialName === currentInfo.value?.materialName &&
          x.materialCode === currentInfo.value?.materialCode
      );
      if (!currentObj) return;
      console.log('🚀反向定位 ~ nextTick ~ posId:', posId);
      // scrollToPosition(currentObj.sequenceNbr, tableData.value);
      vexTable.value.setCurrentRow(currentObj);
      vexTable.value.scrollToRow(currentObj);
    }
  }, 10);
};

const getRcjTreeByCode = () => {
  const params = {
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
    libraryCode: selectValue.value,
  };
  api.getRcjTreeByCode(params).then(res => {
    console.log('res');
    if (res.status === 200) {
      treeData.value = getInitData(res.result);
      console.log('人材机数据', getInitData(res.result));
      if (
        !props.currentMaterialInfo ||
        (!props.currentMaterialInfo.rcjDetailsDTOs &&
          Number(props.currentMaterialInfo.rcjmx) !== 0) ||
        !props.currentMaterialInfo.standardId
      ) {
        selectedKeys.value = [res.result[0].key];
        queryForm.libraryCode = selectValue.value;
        queryForm.level1 = res.result[0].materialName;
      } else {
        findRCJAndLevel(treeData.value, currentInfo.value);
      }
      queryBaseRcjLikeName();
    }
  });
};
const selectChildren = (selectedKeys, { node, event }) => {
  console.log('点击子节点', node); //选择左侧树对应右侧表格数据发生变化
  currentTreeInfo.value = node;
  initQuery();
  let pathList = node.dataRef.path.split('/');
  queryForm.libraryCode = selectValue.value;
  pathList.forEach((item, index) => {
    queryForm[`level${index + 1}`] = item;
  });
  console.log('===========', queryForm);
  isSearch.value = false;

  queryBaseRcjLikeName(() => {
    currentInfo.value = null;
    vexTable.value.scrollTo(0, 0);
  });
};

const handleChange = value => {
  queryForm.libraryCode = value;
  treeData.value = [];
  getRcjTreeByCode();
};
const tableData = ref();

const onSearch = () => {
  initQuery();
  queryForm.libraryCode = selectValue.value;
  isSearch.value = true;
  queryForm.materialName = materialName.value;
  queryAllBaseRcjLikeName();
};

const queryDeLibrary = () => {
  api.queryDeLibrary(store.currentTreeInfo.deStandardId).then(res => {
    if (res.status === 200) {
      selectOptions.value = res.result;
      selectValue.value = store.currentTreeInfo.libraryCode;
      // rcjmx 0父级1子级
      if (
        !props.currentMaterialInfo ||
        (!props.currentMaterialInfo.rcjDetailsDTOs &&
          Number(props.currentMaterialInfo.rcjmx) !== 0) ||
        !props.currentMaterialInfo.standardId
      ) {
        getRcjTreeByCode();
      } else {
        queryRcjById();
      }
    }
  });
};

const queryBaseRcjLikeName = (callback = () => {}) => {
  let apiData = {
    baseRcj: JSON.parse(JSON.stringify(queryForm)),
    page: page.value,
    limit: limit.value,
    taxMade: store.taxMade,
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
  };
  api.queryBaseRcjLikeName(apiData).then(res => {
    if (res.status === 200) {
      console.log(
        '111111111111人材机列表数据--queryBaseRcjLikeName',
        res.result,
        apiData
      );
      if (page.value === 1) {
        tableData.value = [];
      }
      tableData.value = tableData.value.concat(res.result.list);
      tableData.value = disposeDeTypeData(tableData.value, true, false);
      callback();
      virtualListHandler();
      if (props.currentMaterialInfo?.standardId) {
        // let currentObj = tableData.value.filter(
        //   x =>
        //     x.materialName === currentInfo.value?.materialName &&
        //     x.materialCode === currentInfo.value?.materialCode
        // )[0];
        // vexTable.value.setCurrentRow(currentObj);
      } else {
        let obj = tableData.value.filter(
          x => x?.sequenceNbr === currentInfo.value?.sequenceNbr
        )[0];
        if (!obj) {
          currentInfo.value = null;
          vexTable.value.clearCurrentRow();
        }
      }
      if (Math.ceil(res.result.total / limit.value) > page.value) {
        scrollSwitch.value = true;
      } else {
        scrollSwitch.value = false;
      }
    }
  });
};

const queryAllBaseRcjLikeName = () => {
  console.log('queryForm', queryForm);
  let apiData = {
    baseRcj: JSON.parse(JSON.stringify(queryForm)),
    page: page.value,
    limit: limit.value,
    taxMade: store.taxMade,
    constructId: store.currentTreeGroupInfo?.constructId,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
  };
  api.queryAllBaseRcjLikeName(apiData).then(res => {
    if (res.status === 200) {
      console.log(
        '111111111111人材机查询列表数据--queryAllBaseRcjLikeName',
        res.result,
        apiData
      );
      if (page.value === 1) {
        tableData.value = [];
      }
      tableData.value = tableData.value.concat(res.result.list);
      tableData.value = disposeDeTypeData(tableData.value, true, false);
      virtualListHandler();
      let obj = tableData.value.filter(
        x => x?.sequenceNbr === currentInfo.value?.sequenceNbr
      )[0];
      if (!obj) {
        currentInfo.value = null;
        vexTable.value.clearCurrentRow();
      }
      if (Math.ceil(res.result.total / limit.value) > page.value) {
        scrollSwitch.value = true;
      } else {
        scrollSwitch.value = false;
      }
    }
  });
};

const getInitData = tree => {
  return tree.map(item => {
    item.key = item.materialName + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.materialName;
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.materialName + Math.ceil(Math.random() * 10000 + 1),
            path:
              (item.path ? item.path : item.materialName) +
              '/' +
              n.materialName,
          }))
        )
      : null;
    return item;
  });
};

// 选中单条清单定额数据
const currentChangeEvent = ({ row }) => {
  console.log('选中行数据：', row);
  currentInfo.value = row;
};

const cellDBLClickEvent = ({ row }) => {
  if (isFrRcjSummary.value) return;
  currentInfo.value = row;
  if (currentInfo.value && !props.indexLoading) {
    updateCurrentInfo(1);
  }
};

// const getScroll = type => {
//   if (
//     Math.ceil(type.scrollTop + type.$event.target.clientHeight) >=
//     type.scrollHeight
//   ) {
//     if (scrollSwitch.value) {
//       page.value++;
//       if (isSearch.value) {
//         queryAllBaseRcjLikeName();
//       } else {
//         queryBaseRcjLikeName();
//       }
//     }
//   }
// };
/**
 * 是否配比材料
 */
const isProportionalMaterials = (info = store.subCurrentInfo) => {
  let { rcjFlag, type, kind } = info;
  if ([94, 95].includes(info.kind)) {
    // 以前修改人材机明细info为04得定额，如果当kind为新增得主材设备94，95时，当前应该取他得父级定额数据
    rcjFlag = info?.customParent?.rcjFlag;
    type = info?.customParent?.type;
  }
  return rcjFlag === 1 && ['砼', '商砼', '浆', '商浆', '配比'].includes(type);
};
/**
 * 是否材料类型
 * @param {*} kind
 */
const isMaterials = (kind = currentInfo.value.kind) => {
  return [2, 5, 6, 7, 8, 9, 10].includes(kind);
};
// 插入替换拦截
const addOrReplaceIntercept = () => {
  const currentMaterialInfo = props.currentMaterialInfo;
  if (
    currentMaterialInfo &&
    !currentMaterialInfo?.rcjDetailsDTOs &&
    currentInfo.value.levelMark !== 0
  ) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '明细数据不可再包含配比材料、机械台班',
      confirm: () => {
        infoMode.hide();
      },
    });
    return true;
  }
  if (
    currentMaterialInfo &&
    !currentMaterialInfo?.rcjDetailsDTOs &&
    (currentInfo.value.materialName === '其他材料' ||
      currentInfo.value.materialName === '其他机械' ||
      currentInfo.value.materialName === '其他材料费' ||
      currentInfo.value.materialName === '其他机械费') &&
    currentInfo.value.unit === '%'
  ) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '明细数据不能插入单位是%的其他材料费、其他机械费',
      confirm: () => {
        infoMode.hide();
      },
    });
    return true;
  }
  if (
    (isProportionalMaterials() && !isMaterials()) ||
    (store.subCurrentInfo.type === '机械费' && currentInfo.value.kind !== 3) ||
    (currentMaterialInfo &&
      !currentMaterialInfo?.rcjDetailsDTOs &&
      currentMaterialInfo?.kind !== currentInfo.value.kind)
  ) {
    console.log(currentMaterialInfo, currentInfo.value, store.subCurrentInfo);
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '明细数据仅可插入同主数据一致类型的数据',
      confirm: () => {
        infoMode.hide();
      },
    });
    return true;
  }
  return false;
};

/**
 * 人材机汇总替换拦截
 */
const replaceIntercept = () => {
  const { levelMark, rcjmx } = props.currentMaterialInfo;

  if (levelMark === 0 && currentInfo.value.levelMark !== 0) {
    // 普通材料不可替换为配比材料和机械台
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '无法将普通材料替换为配比材料、机械台班',
      confirm: () => {
        infoMode.hide();
      },
    });
    return true;
  }
  if (rcjmx === 1) {
    // 若某条材料为当前单位工程某人材机中的配比子级材料
    // rcjmx 0代表父级人材机
    //  1代表子集人材机
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '无法替换明细数据',
      confirm: () => {
        infoMode.hide();
      },
    });
    return true;
  }
  return false;
};
const updateCurrentInfo = async type => {
  // 不是人材机汇总页面中的拦截
  if (!isFrRcjSummary.value && addOrReplaceIntercept()) return;
  if (isFrRcjSummary.value && replaceIntercept()) return;
  if (type === 2) {
    if (currentInfo.value.unit !== props.currentMaterialInfo?.unit) {
      // 是否存在默认系数，如果存在，则默认计算
      try {
        console.log('loadPriceApi.queryUnitConversion=>res', {
          desc2: currentInfo.value.unit,
          desc1: props.currentMaterialInfo?.unit,
        });
        // 传参：desc1 之前单位 ;传参：desc2 之后单位
        let res = await loadPriceApi.queryUnitConversion({
          desc2: currentInfo.value.unit,
          desc1: props.currentMaterialInfo?.unit,
        });
        console.log('loadPriceApi.queryUnitConversion=>res', res);
        if (res.code === 200 && res.result) {
          conversionCoefficient.value = res.result;
        }
      } catch (e) {
        console.error('loadPriceApi.queryUnitConversion', e);
      } finally {
        unitVisible.value = true;
      }
      return;
    }
  }
  if (type === 1) {
    emits('addChildrenRcjData', currentInfo.value);
  } else {
    emits('currentInfoReplace', currentInfo.value);
  }
  // allInit();
};
const close = () => {
  emits('update:indexVisible', false);
  allInit();
};

const initQuery = () => {
  queryForm = Object.assign(queryForm, {
    level1: '',
    level2: '',
    level3: '',
    level4: '',
    level5: '',
    level6: '',
    level7: '',
  });
  page.value = 1;
};

const allInit = () => {
  initQuery();
  materialName.value = '';
  queryForm.materialName = '';
  treeData.value = [];
  tableData.value = [];
  expandedKeys.value = [];
  selectedKeys.value = [];
  currentTreeInfo.value = null;
  currentInfo.value = null;
  conversionCoefficient.value = '';
};

const unitClose = () => {
  unitVisible.value = false;
  conversionCoefficient.value = null;
};

const handleOk = () => {
  if (conversionCoefficient.value === '0') {
    return message.warning('转换系数不可为0');
  }
  currentInfo.value.conversionCoefficient = conversionCoefficient.value;
  console.log('66666666666', currentInfo.value);
  unitVisible.value = false;
  emits('currentInfoReplace', currentInfo.value);
  // allInit();
};

// 查询当前人材机数据用于反向定位
const queryRcjById = () => {
  api
    .queryRcjById({
      standardId:
        props.currentMaterialInfo.newStandardId ||
        props.currentMaterialInfo.standardId,
      libraryCode: props.currentMaterialInfo.libraryCode,
      constructId: store.currentTreeGroupInfo?.constructId,
      unitId: store.currentTreeInfo?.id,
      singleId: store.currentTreeGroupInfo?.singleId,
    })
    .then(res => {
      console.log('dddddddd12ddddddd', res);
      if (res.status === 200 && res.result) {
        selectValue.value = res.result.libraryCode;
        currentInfo.value = res.result;
        queryForm = {
          ...queryForm,
          libraryCode: res.result.libraryCode,
          level1: res.result.level1,
          level2: res.result.level2 || '',
          level3: res.result.level3 || '',
          level4: res.result.level4 || '',
          level5: res.result.level5 || '',
          level6: res.result.level6 || '',
          level7: res.result.level7 || '',
        };
      }
      getRcjTreeByCode();
    });
};

// 反向查找人材机指定数据并展开选中
const findRCJAndLevel = (tree, targetData, level = 1) => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.materialName === targetData[`level${level}`]) {
      expandedKeys.value.push(node.key);
      selectedKeys.value = [node.key];
    }
    if (node.childrenList) {
      const result = findRCJAndLevel(
        node.childrenList,
        currentInfo.value,
        level + 1
      );
      if (result) {
        console.log('反向定位数据', result);
        return result;
      }
    }
  }

  return null;
};
</script>

<style lang="scss" scoped>
// .custom-title {
//   display: flex;
//   align-items: center;
//   padding: 8px 20px 8px 0;
//   .title {
//     position: relative;
//     padding-left: 20px;
//     font-size: 16px;
//     line-height: 22px;
//     font-weight: 500;
//     flex: 1;
//     color: rgb(30, 144, 255);
//     &::after {
//       position: absolute;
//       top: 50%;
//       left: 0;
//       transform: translateY(-50%);
//       content: "";
//       display: block;
//       width: 2px;
//       height: 11px;
//       background: #1890ff;
//     }
//   }
//   img {
//     display: inline-block;
//     width: auto;
//     padding: 0 5px;
//     height: 16px;
//   }
// }

.dialog-wrap {
  width: 1000px;
}
.contentCenter {
  display: flex;
  justify-content: space-around;
  color: #b9b9b9;
  margin-top: 10px;
  height: calc(100% - 50px);
  .left {
    width: 35%;
    .search {
      width: 100%;
      // height: 350px;
      border: 1px solid #b9b9b9;
      .tree {
        width: 330px;
        height: 250px;
        // border: 1px solid #dcdfe6;
        margin-left: 10px;
        overflow: hidden;
        position: relative;
        z-index: 1;
        &:hover {
          overflow: auto;
        }
      }
    }
  }
  .right {
    width: 60%;
    .btns {
      display: flex;
      width: 100%;
      justify-content: space-between;
      .btnNo1 {
        margin: 0 15px;
      }
    }
    .table {
      height: 350px;
      :deep(
          .vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis
        ) {
        height: 22px !important;
      }
    }
  }
}
.dialog-content {
  padding: 46px;
  text-align: center;
}
.init-text {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
  .icon {
    margin-right: 5px;
  }
  span {
    color: #1e90ff;
    margin: 0 2px;
  }
  .ant-input {
    width: 105px;
    margin-left: 5px;
  }
}
.init-text:nth-last-of-type(1) {
  margin-top: 25px;
}
.footer-btn-list {
  padding-bottom: 20px;
}
</style>
