<!--
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-16 10:40:00
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-01-22 14:58:13
-->
<template>
  <div class="header">
    <div class="header-top">
      <div class="left-content">
        <div to="/" class="logo-link">
          <img :src="getUrl('newCsProject/logo.png')" alt="logo" class="logo" />
        </div>
        <span class="header-quick-save" :class="{ disabled: saveStatus }" @click="save">
          <icon-font class="icon-font" type="icon-kuaisubaocun" />
        </span>
        <redoUndo style="margin-left: 20px;"></redoUndo>
      </div>
      
      <div class="title">
        <span>{{ fileName || "建设工程计价管理软件" }}</span>
        <span v-if="!saveStatus"> *</span>
        <div class="sub-title">
          <div class="list">
            <img src="@/assets/img/sub-title-icon.png" alt="icon" class="icon" /><span
              >公测快速反馈：</span
            ><span
              class="link"
              @click="openExternal('https://www.yunsuanfang.com/feedback')"
              >https://www.yunsuanfang.com/feedback</span
            >
          </div>
          <div class="list">
            <img
              src="@/assets/img/sub-title-icon2.png"
              alt="icon"
              class="icon"
              style="width: 10px; height: 15px"
            /><span>手机快速反馈：鼠标移入扫一扫</span>
            <a-popover placement="bottom">
              <template #content>
                <img :src="qrCodeUrl" alt="qr-code" class="qr-code-img" />
              </template>
              <img src="@/assets/img/sub-qr-code.png" alt="qr-code" class="qr-icon" />
            </a-popover>
          </div>
        </div>
      </div>
      <!-- <a-menu v-model:selectedKeys="selectedKeys" mode="horizontal">
      <a-menu-item :key="item.key" v-for="item in menus">{{
        item.name
      }}</a-menu-item>
    </a-menu> -->
      <!-- <img src="@/assets/img/header-bg.png" class="header-bg" alt=""> -->
      <div class="right-menu">
        <user-info-show
          :loginType="userDetail?.loginType"
          :userInfo="userDetail?.userInfo"
          :infoList="userDetail?.changeInfoList"
          :infoVisible="false"
        ></user-info-show>
        <div class="operate">
          <div class="operate-icon" @click="toMin">
            <img :src="getUrl('newCsProject/operate-minimize.png')" />
          </div>
          <div class="operate-icon" v-show="!isMax" @click="toMaX">
            <img :src="getUrl('newCsProject/operate-maximize.png')" />
          </div>
          <div class="operate-icon" v-show="isMax" @click="toMaX">
            <img :src="getUrl('newCsProject/operate-reduction.png')" />
          </div>
          <div class="operate-icon" @click="toClose">
            <img :src="getUrl('newCsProject/operate-close.png')" />
          </div>
        </div>
      </div>
    </div>
    <div class="header-menu">
      <div
        class="header-menu-item"
        :class="{ selected: keyValue === item.key }"
        v-for="item in menus"
        :key="item.key"
      >
        <a-dropdown v-if="item.key === 'file'" :trigger="['click']">
          <div class="menu-label" @click="openChange(item)">
            {{ item.name }}
            <img v-if="item.dropdown" :src="getUrl('newCsProject/select.png')" alt="" />
          </div>
          <template #overlay>
            <a-menu @click="handleMenuClick">
              <a-menu-item
                v-for="child in item.children"
                :key="child.key"
                style="width: 120px"
                :disabled="['save'].includes(child.key) ? saveStatus : false"
              >
                <icon-font :type="child.icon" style="margin: 0 10px 0 0" /><a
                  href="javascript:;"
                  >{{ child.name }}</a
                >
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <div class="menu-label" @click="clickMenu(item)" v-else-if="item.isDev">
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
  <gljSetUpPopup
    ref="gljSetUpPopupRef"
    @closePopup="setUpVisible = false"
    v-if="setUpVisible"
  ></gljSetUpPopup>

  <new-glj-project-model
    v-model:visible="newGljProVisible"
    :showType="'新建工料机项目'"
    @onSuccess="
      () => {
        newGljProVisible = false;
      }
    "
  ></new-glj-project-model>
  <common-modal
    className="titleNoColor noHeaderHasclose"
    title=" "
    width="450"
    height="320"
    v-model:modelValue="updateValue"
    :mask="false"
  >
    <div class="updataInfo">
      <div>
        <img :src="getUrl('download.png')" alt="" />
        <p>存在新版本，可更新使用</p>
        <p><span>版本号</span>{{ version }}</p>
        <p><span>更新时间</span>{{ releaseDate }}</p>
      </div>
    </div>

    <a-button type="primary" @click="onSubmit" style="display: block; margin: auto"
      >立即更新</a-button
    >
  </common-modal>
  <common-modal
    className="titleNoColor project-nosave"
    v-model:modelValue="saveChenkModal"
    title=" "
    width="418"
    height="160"
    @close="cancelsaveCheck"
  >
    <div class="main-content">
      <p style="margin-bottom: 32px">
        <icon-font class="icon-font" :type="'icon-qiangtixing'"></icon-font>
        当前文件未保存，是否保存？
      </p>
      <div class="footer-btn">
        <a-button @click="noSave" style="margin: 0 30px 0px 0px">否</a-button>
        <a-button @click="confirmSave" type="primary">是</a-button>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  getCurrentInstance,
  watch,
  defineAsyncComponent,
} from "vue";
import { getUrl } from "@/utils/index";
import { ipcApiRoute, specialIpcRoute } from "@/api/main";
import feePro from "@/api/feePro";
import { checkisOnline } from "@/utils/publicInterface";
import csProject from "@gongLiaoJi/api/csProject";
import api from "@gongLiaoJi/api/projectDetail.js";
import { message } from "ant-design-vue";
import { projectDetailStore } from "@/store/projectDetail";
import { projectStore as projectStoreGlj } from "@gongLiaoJi/store/project";
import { useRouter, useRoute } from "vue-router";
import infoMode from "@/plugins/infoMode.js";
import { useCheckProjectBefore } from "@gongLiaoJi/hooks/useCheckProjectBefore";
import userInfoShow from "@/views/csProject/header/userInfoShow.vue";
import newGljProjectModel from "@/components/SelfModel/newGljProjectModel.vue";
import QRCode from "qrcode";
import redo from '../../hooks/redo';

let qrCodeUrl = ref("");
const getQRCode = async () => {
  const text = "https://h5.yunsuanfang.com/userFeedback"; // 这里是你想生成二维码的链接
  qrCodeUrl.value = await QRCode.toDataURL(text);
  console.log("dataUrl", qrCodeUrl.value);
};

const { shell } = require("electron");
// 假设你有一个链接地址
// 使用 shell 模块的 openExternal 方法打开链接

const openExternal = (link) => {
  shell.openExternal(link);
};
const gljSetUpPopup = defineAsyncComponent(() =>
  import("@/components/Header/gljSetUpPopup.vue")
);

const { showInfo, isProjectComplete } = useCheckProjectBefore();

let setUpVisible = ref(false);
const globalProperties = getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
const route = useRoute();
const isMax = ref(false);
const { ipcRenderer, webFrame } = require("electron");
const logoUrl = getUrl("logo.png");
const modalWidth = ref("1300px");
const router = useRouter();
const newGljProVisible = ref(false); //新建预算项目弹框
const store = projectDetailStore();
const projectStore = projectStoreGlj();
console.log("projectStore", projectStore);
let userDetail = ref(null);
let status = ref(0); // -1:异常，1：有可用更新，2：没有可用更新，3：下载中, 4：下载完成
let version = ref("1.0.0"); //版本
let releaseDate = ref("2023-09-13 10:00:00"); //更新时间
let updateValue = ref(false); //立即更新弹框
let keyValue = ref("customize"); //切换tab栏
let saveStatus = ref(true);
const saveChenkModal = ref(false);
const menus = ref([
  {
    name: "文件",
    key: "file",
    dropdown: true,
    children: [
      {
        name: "打开",
        key: "open",
        icon: "icon-dakai",
      },
      {
        name: "新建",
        key: "new",
        icon: "icon-xinjian",
      },
      {
        name: "另存为",
        icon: "icon-lingcunwei",
        key: "asideSave",
      },
      {
        name: "保存",
        icon: "icon-baocun",
        key: "save",
      },
      {
        name: "设置",
        icon: "icon-shezhi",
        key: "setup",
      },
      {
        name: "检查更新",
        icon: "icon-jianchagengxin",
        key: "checkUpdate",
      },
    ],
    isDev: true,
  },
  {
    name: "编制",
    key: "customize",
    isDev: true,
  },
  {
    name: "报表",
    key: "reportForm",
    isDev: true,
  },
]);
let timer = ref(); //获取保存信息循环
let saveTimer = ref(); // 获取自动保存信息循环

onMounted(() => {
  isMaxFun();
  getQRCode();
  window.addEventListener("resize", isMaxFun);
  isNeedSave();
  timer.value = setInterval(() => {
    isNeedSave();
  }, 3000);
  if (localStorage.getItem("loginUserInfo")) {
    store.SET_IS_LOGIN_USER_INFO(JSON.parse(localStorage.getItem("loginUserInfo")));
  } else {
    sendTosubWindow("getLoginInfo");
  }
  listenerMain();
  saveTimer.value = setInterval(() => {
    save();
  }, 1000 * 60 * 5);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", isMaxFun);
});
const save = async () => {
  // 关闭时保存当前用户的最后一次操作,下次打开时
  let gljSelData = JSON.parse(JSON.stringify(store.gljCheckTab));
  let obj = {
    constructId: store.currentTreeGroupInfo?.constructId,
    tableSetting: gljSelData,
    currentId: store.currentTreeInfo?.id,
  };
  await csProject.setTableSettingCache(obj);
  csProject.saveGljfFile(route.query.constructSequenceNbr).then((res) => {
    saveStatus.value = true;
    console.log(res);
    message.success("保存成功");
  });
};
const toMin = () => {
  ipcRenderer.send("window-min-child", {
    id: route.query.constructSequenceNbr,
  });
};
const toMaX = () => {
  ipcRenderer.send("window-max-child", {
    id: route.query.constructSequenceNbr,
  });
  isMaxFun();
};
const isMaxFun = () => {
  setTimeout(() => {
    let { innerWidth, innerHeight } = window;
    let { availWidth, availHeight } = screen;
    isMax.value = innerWidth === availWidth && innerHeight === availHeight;
    modalWidth.value = innerWidth < 1366 ? "453px" : "1300px";
  }, 100);
};
const toClose = async () => {
  await isNeedSave(); //关闭时候检测是否需要保存
  if (saveStatus.value) {
    deleteProjectCache();
  }
  await csProject
    .removeCheck({ constructId: route.query.constructSequenceNbr })
    .then((res) => {
      console.log("removeCheck", res);
    });

  await csProject
    .gljRemoveCheck({ constructId: route.query.constructSequenceNbr })
    .then((res) => {
      console.log("removeCheck", res);
    });
  // 关闭时保存当前用户的最后一次操作,下次打开时
  let gljSelData = JSON.parse(JSON.stringify(store.gljCheckTab));
  let obj = {
    constructId: store.currentTreeGroupInfo?.constructId,
    tableSetting: gljSelData,
    currentId: store.currentTreeInfo?.id,
  };
  await csProject.setTableSettingCache(obj);
  if (!saveStatus.value) {
    saveChenkModal.value = true;
  } else {
    // await api.importGljDeCancel({
    //   importConstructId: route.query.constructSequenceNbr,
    // });
    ipcRenderer.send("window-close-child", {
      id: route.query.constructSequenceNbr,
    });
  }
};
const cancelsaveCheck = () => {
  saveChenkModal.value = false;
};

const deleteProjectCache = () => {
  const importConstructId = route.query.constructSequenceNbr;
  csProject.deleteGljImportProject(importConstructId).then((res) => {
    console.log("删除导入项目缓存", res);
  });
};

const confirmSave = async () => {
  await saveGljfFile();
  deleteProjectCache();
  store.SET_FIRST_SET_CHECK_RANGE(true);
  ipcRenderer.send("window-close-child", {
    id: route.query.constructSequenceNbr,
  });
};

const noSave = () => {
  deleteProjectCache();
  ipcRenderer.send("window-close-child", {
    id: route.query.constructSequenceNbr,
  });
};

const saveGljfFile = async () => {
  await csProject.saveGljfFile(route.query.constructSequenceNbr).then((res) => {
    console.log(res);
    message.success("保存成功");
  });
};
const openChange = (item) => {
  isNeedSave();
  clickMenu(item);
};
// 概算未做
const isNeedSave = async () => {
  await csProject
    .diffProject({ constructId: route.query.constructSequenceNbr })
    .then((res) => {
      saveStatus.value = !res.result;
    });
};

let openLocalProStatus = ref(false);
const handleMenuClick = ({ key }) => {
  console.log("handleMenuClick", key);
  switch (key) {
    case "open":
      // 打开
      if (openLocalProStatus.value) {
        message.info("已打开系统弹窗");
        return;
      }
      openLocalProStatus.value = true;
      csProject
        .openGsLocalFile()
        .then((res) => {
          console.log("打开本地项目", res);
        })
        .finally((res) => {
          console.log("🚀 ~ feePro.openLocalFile ~ res:", res);
          openLocalProStatus.value = false;
        });
      break;
    case "new":
      // 新建-打开控制台新建概算项目弹框
      newGljProVisible.value = true;
      break;
    case "asideSave":
      // 另存为
      // 保存
      csProject.saveGljfFile(route.query.constructSequenceNbr, "saveAs").then((res) => {
        if (res.status !== 200) {
          if (res.message !== "未选中任何文件") {
            message.error(res.message);
          }
        } else {
          message.success(res.message);
        }
      });
      break;
    case "save":
      // 保存
      saveGljfFile();
      break;
    case "setup":
      setUpVisible.value = true;
      //设置
      break;
    case "checkUpdate":
      //检查更新
      // updateValue.value = true; //测试
      checkForUpdater();
      break;
  }
};

const getData = (time) => {
  let date = new Date(new Date(time).getTime() + 8 * 3600 * 1000);
  date = date.toJSON();
  date = date && date.substring(0, 19).replace("T", " ");
  return date;
};
//检查更新
const checkForUpdater = async () => {
  const isOnline = await checkisOnline(true);
  if (isOnline) {
    sendTosubWindow("checkUpdate");
    listenerMain(true);
  }
};
//更新下载
const onSubmit = () => {
  // showSchedule.value = true;
  feePro.downloadApp().then((res) => {
    console.log("点击下载", res);
    updateValue.value = false;
    sendTosubWindow("downloadApp");
    //关闭所有工作台页面---打开首页的进度条进行下载
  });
};
const sendTosubWindow = async (type) => {
  // 向主窗口发送下载请求-并关闭子窗口
  let mainId = await $ipc.invoke(ipcApiRoute.getWCid, "main");
  if (type === "checkUpdate") {
    //点击检查更新向父窗口发送检查更新消息
    $ipc.sendTo(mainId, specialIpcRoute.window2ToWindow1, "checkUpdate");
  } else if (type === "downloadApp") {
    //点击立即更新向主窗口发送消息关闭所有子窗口，主窗口展示进度条
    $ipc.sendTo(mainId, specialIpcRoute.window2ToWindow1, "downloadApp");
    feePro.closeAllChildWindow().then((res) => {});
  } else if (type === "getLoginInfo") {
    //向主窗口获取登录信息
    $ipc.sendTo(mainId, specialIpcRoute.window2ToWindow1, "getLoginInfo");
  }
};
const isJson = (str) => {
  //判断字符串是否可以使用JSON.parse规范转化
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};
const listenerMain = (bol = false) => {
  //监听主窗口消息
  $ipc.on(specialIpcRoute.window1ToWindow2, (event, arg) => {
    // bol && message.info(`接受主窗口发来的消息-------------${arg}`); //测试
    if (!isJson(arg)) return;
    let result = arg ? JSON.parse(arg) : null;
    console.log("检查更新", result);
    if (bol && result.status !== 1) {
      message.info("您当前使用的是最新版本");
      $ipc.removeAllListeners(specialIpcRoute.window1ToWindow2);
    } else if (bol && result.status === 1) {
      status.value = result.status;
      releaseDate.value = getData(result.releaseDate);
      version.value = result.version;
      updateValue.value = true;
      $ipc.removeAllListeners(specialIpcRoute.window1ToWindow2);
    }
  });
};
watch(
  () => store.loginUserInfo,
  () => {
    getUserInfo();
  }
);

const fileName = ref("");

watch(
  () => projectStore.projectPath,
  () => {
    if ((projectStore.projectPath ?? "") !== "") {
      let fileNameList = projectStore.projectPath.split("\\");
      if (fileNameList.length > 0) {
        fileName.value = fileNameList[fileNameList.length - 1];
      } else {
        fileName.value = "";
      }
    } else {
      fileName.value = "";
    }
  },
  {
    immediate: true,
  }
);

const getUserInfo = () => {
  if (store.loginUserInfo && !userDetail.value) {
    localStorage.setItem("loginUserInfo", JSON.stringify(store.loginUserInfo));
    userDetail.value = { ...store.loginUserInfo };
  }
};
const clickMenu = async ({ key, path }) => {
  if (keyValue.value === key) return; //key值未切换不走下面流程
  if (
    (keyValue.value === "file" && key === "customize") ||
    (keyValue.value === "customize" && key === "file")
  ) {
    keyValue.value = key; //文件-》编制  ，编制-》文件不需要切换路由
    return;
  }
  keyValue.value = key;
  // if (key === 'reportForm' && !showInfo()) return;
  let patha =
    key === "customize" || key === "reportForm"
      ? `/gljProjectDetail/${key}`
      : key === "file"
      ? "/gljProjectDetail/customize"
      : "/gljProjectDetail";
  console.log(path ?? patha);
  router.push({
    path: path ?? patha,
    query: {
      constructSequenceNbr: route.query.constructSequenceNbr,
    },
  });
  let gljSelData = JSON.parse(JSON.stringify(store.gljCheckTab));
  let obj = {
    constructId: store.currentTreeGroupInfo?.constructId,
    tableSetting: gljSelData,
    currentId: store.currentTreeInfo?.id,
  };
  await csProject.setTableSettingCache(obj);
  // 跳转清除存的树信息，不然有些地方缺少判断，直接报错
  store.currentTreeInfo = null;
  store.currentTreeGroupInfo = null;
};

onBeforeUnmount(() => {
  clearInterval(timer.value);
  timer.value = null;
  clearInterval(saveTimer.value);
  saveTimer.value = null;
});
</script>
<style lang="scss" scoped>
.header {
  width: 100vw;
  height: 186px;
  position: relative;
  height: 56px;
  background: #2867c7;
  user-select: none;
  &-top {
    height: 32px;
    display: flex;
    position: relative;
    z-index: 99;
    .left-content {
      display: flex;
      align-items: center;
      .versionInfo {
        color: #ffffff;
        margin-left: 5px;
      }
    }
    .logo-link {
      height: 100%;
      -webkit-app-region: drag; // 设置可拖动
      img {
        width: 139px;
        height: 19px;
        // transform: scale(0.90);
        vertical-align: top;
        margin-top: 8px;
        margin-left: 18px;
      }
    }
  }
  &-menu {
    height: 24px;
    width: 100%;
    line-height: 24px;
    font-size: 12px;
    color: white;
    display: flex;
    padding: 0 10px;
    &-item:hover {
      background: rgba(30, 76, 149, 0.39);
    }
    .selected {
      background: #1e4c95;
      border-radius: 4px 4px 0px 0px;
    }
    &-item {
      height: 24px;
      padding: 0 16px;
      cursor: pointer;
      .menu-label {
        height: auto;
        img {
          width: 13px;
          position: relative;
          top: -2px;
        }
      }
    }
  }

  .title {
    -webkit-app-region: drag; // 设置可拖动
    margin-left: 59px;
    width: calc(100vw - 460px);
    text-align: center;
    line-height: 32px;
    color: white;
    font-size: 12px;
    text-indent: 10px;
    img {
      position: relative;
      top: 0px;
    }
  }
  .sub-title {
    position: absolute;
    top: 33px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    .list {
      padding: 0 10px;
      font-size: 0;
      color: #a0ffff;
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
    }
    span {
      font-size: 12px;
      color: #a0ffff;
      line-height: 17px;
      text-indent: 0;
      white-space: nowrap;
    }
    .link {
      cursor: pointer;
      text-decoration-line: underline;
    }
    .icon {
      display: block;
      width: 15px;
      height: 17px;
      margin-right: 3px;
    }
    .qr-icon {
      display: block;
      width: 15px;
      height: 15px;
      margin-left: 3px;
    }
  }
}
.qr-code-img {
  display: block;
  width: 77px;
  height: 77px;
  margin: -12px -16px;
}
.updataInfo {
  div {
    width: 210px;
    margin: -30px auto 0;
    p {
      font-size: 14px;
      line-height: 17px;
      color: #2a2a2a;
      span {
        width: 80px;
        display: inline-block;
      }
    }
  }
}
.logo-link {
  position: relative;
  z-index: 2;
  width: 166px;
}
.right-menu {
  padding-right: 12px;
  text-align: right;
  display: flex;
}
.user-info {
  cursor: pointer;
  line-height: 32px;
  .avatar {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #dfdfdf;
    vertical-align: super;
  }
  span {
    display: inline-block;
    width: 62px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-right: 3px;
    color: #fff;
  }
}
.operate {
  width: 120px;
  display: flex;
  height: 18px;
  align-items: center;
  margin-left: 20px;
  flex-direction: row;
  justify-content: space-between;
  /* padding-top: 5px; */
  line-height: 32px;
  opacity: 1;
  transition: opacity linear 0.2s;
  &-icon {
    width: 20px;
    height: 20px;
  }
  &-icon:hover {
    cursor: pointer;
    transition: opacity linear 0.2s;
    opacity: 0.6;
  }
}
.project-nosave {
  .main-content {
    padding: 5px 18px;
  }
  .footer-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.header-quick-save {
  width: 14px;
  cursor: pointer;
  justify-content: center;
  display: flex;
  position: absolute;
  left: 166px;
  top: 10px;
  height: 20px;
  &.disabled {
    cursor: default;
    .icon-font {
      opacity: 0.5;
    }
  }
}
</style>
