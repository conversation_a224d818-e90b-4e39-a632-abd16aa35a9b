const {Service} = require('../../../core');
const {GsBaseFreeRate} = require("../models/GsBaseFreeRate");
const FreeRateType  = require('../enums/FreeRateType');
const {ResponseData} = require("../utils/ResponseData");
const ProjectDomain = require("../domains/ProjectDomain");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const WildcardMap = require('../core/container/WildcardMap');
const {FreeRateModel} = require("../models/FreeRateModel");
const GsjRateKindEnum = require('../enums/GsjRateKindEnum');
const {ArrayUtil} = require("../../../common/ArrayUtil");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {Snowflake} = require("../utils/Snowflake");
const {NumberUtil} = require("../utils/NumberUtil");

/**
 * 费率 service
 * @class
 */
class GljBaseFreeRateService extends Service{

    constructor(ctx) {
        super(ctx);
        this.gljBaseFreeRateDao = this.app.db.gongLiaoJiProject.manager.getRepository(GsBaseFreeRate);
    }

    /**
     * 获取所有费率
     * @returns {Promise<GsBaseFreeRate[]|Error>}
     */
    async getByFreeRateAll() {
        return await this.gljBaseFreeRateDao.find({});
    }

    /**
     * 费率信息查询
     * @param libraryCode 定额册码
     * @param freeRateType 费率类型
     * @returns {Promise<*>}
     */
    async getFreeRateInfo(args) {
        let {libraryCode, freeRateType, constructId, type, singleId, unitId} = args
        let param = {};
        param.libraryCode = libraryCode;
        param.freeRateType = freeRateType;
        //查询计税方式
        let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        param.taxCalculationMethod = taxCalculationMethod;
        let freeRateModel;
        // 将结果转换为数组
        let result = [];
        let freeRate;
        switch (freeRateType) {
            case FreeRateType.MANAGE_FEE_RATE: // 企业管理费
                // freeRateModel=this.queryFreeRateModel(type,constructId,singleId,unitId);
                // param.projectType=freeRateModel.projectType;
                // param.taxCalculationMethod=taxCalculationMethod;

                result = await this.service.gongLiaoJiProject.gljInitUnitProjectService.setManageFeeRateProfitRateAll(param);
                break;
            case FreeRateType.PROFIT_RATE: // 利润
                // freeRateModel=this.queryFreeRateModel(type,constructId,singleId,unitId);
                // param.libraryCode=libraryCode;
                // param.projectType=freeRateModel.projectType;
                // param.taxCalculationMethod=taxCalculationMethod;

                result = await this.service.gongLiaoJiProject.gljInitUnitProjectService.setManageFeeRateProfitRateAll(param);
                break;
            case FreeRateType.TAX_RATE: // 税金
                freeRateModel = this.queryFreeRateModel(type, constructId, singleId, unitId);
                let sjList = await this.service.gongLiaoJiProject.gljBaseGsjRateService.queryByKindAndMethodAndRegionAll(GsjRateKindEnum.SJ.code, taxCalculationMethod, true);
                let index = 1

                // for(const sj of sjList){
                //     freeRate ={
                //         dispNo:index,
                //         name:sj.taxPayingRegion,
                //         freeRate:NumberUtil.numberScale2(sj.rate),
                //         description: ""
                //     };
                //     index++;
                //     result.push(freeRate);
                // }
                freeRate = {
                    dispNo: index,
                    name: "税金",
                    // freeRate: NumberUtil.numberScale2(sjList[0].rate),
                    freeRate: sjList[0].rate,
                    description: ""
                };
                result.push(freeRate);
                break;
            case FreeRateType.GF_RATE: // 规费
                // freeRate ={
                //     dispNo:1,
                //     name:"管理费",
                //     freeRate:manageFeeRateData.rate,
                //     description: ""
                // };
                // result.push(freeRate);
                break;
            case FreeRateType.ANWEN_RATE: // 安全生产、文明施工费（%）
                freeRateModel = this.queryFreeRateModel(type, constructId, singleId, unitId);
                // let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseAnwenRateService.queryByLibraryCodeGlj(libraryCode, freeRateModel.projectLocation, freeRateModel.roadSurfaceNum, freeRateModel.floorSpace, freeRateModel.municipalConstructionCost);
                // let rate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.anwenRateYbjs : gsBaseFreeRate.anwenRateJyjs;
                // freeRate = {
                //     dispNo:1,
                //     name:"安全生产、文明施工费",
                //     freeRate:NumberUtil.numberScale2(rate),
                //     description: ""
                // };
                // result.push(freeRate);

                let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseAnwenRateService.queryAnwenByLibraryCodeGlj(libraryCode);
                // 定义 roadSurfaceNum 的排序映射
                const roadSurfaceNumOrder = {
                    'no_way': 1,
                    'one_side': 2,
                    'two_side': 3,
                    'three_side': 4,
                    'four_side': 5
                };

                // 多级排序
                gsBaseFreeRate.sort((a, b) => {
                    if (a.projectLocation < b.projectLocation) return -1;
                    if (a.projectLocation > b.projectLocation) return 1;

                    if (a.municipalConstructionCost > b.municipalConstructionCost) return -1;
                    if (a.municipalConstructionCost < b.municipalConstructionCost) return 1;

                    // 使用映射进行 roadSurfaceNum 的排序
                    const orderA = roadSurfaceNumOrder[a.roadSurfaceNum] || 6; // 默认值为 6
                    const orderB = roadSurfaceNumOrder[b.roadSurfaceNum] || 6; // 默认值为 6
                    if (orderA < orderB) return -1;
                    if (orderA > orderB) return 1;

                    if (a.floorSpace > b.floorSpace) return -1;
                    if (a.floorSpace < b.floorSpace) return 1;

                    return 0;
                });

                // 定义映射表
                const locationMap = {
                    'city_proper': '市区',
                    'county_town': '县城',
                    'press': '镇',
                    'project_location_other': '非市区、县城、镇'
                };

                const constructionCostMap = {
                    'more_than_50_million': '5000万以上',
                    'less_than_50_million': '5000万以下'
                };

                const roadSurfaceNumMap = {
                    'no_way': '不临路',
                    'one_side': '一面',
                    'two_side': '二面',
                    'three_side': '三面',
                    'four_side': '四面'
                };

                let dispNo = 1
                for (let i = 0; i < gsBaseFreeRate.length; i++) {
                    let awf = gsBaseFreeRate[i];
                    // 转换 projectLocation  位置
                    if (locationMap.hasOwnProperty(awf.projectLocation)) {
                        awf.projectLocation = locationMap[awf.projectLocation];
                    }
                    // 转换 municipalConstructionCost  造价
                    if (constructionCostMap.hasOwnProperty(awf.municipalConstructionCost)) {
                        awf.municipalConstructionCost = constructionCostMap[awf.municipalConstructionCost];
                    }
                    // 转换 roadSurfaceNum  临街
                    if (roadSurfaceNumMap.hasOwnProperty(awf.roadSurfaceNum)) {
                        awf.roadSurfaceNum = roadSurfaceNumMap[awf.roadSurfaceNum];
                    }

                    if (i % 2 === 0) {
                        freeRate = {
                            dispNo: dispNo,
                            projectLocation: awf.projectLocation,
                            municipalConstructionCost: awf.municipalConstructionCost,
                            roadSurfaceNum: awf.roadSurfaceNum,
                            floorSpace2: awf.floorSpace,
                            // freeRate2: NumberUtil.numberScale2(taxCalculationMethod == 1 ? awf.anwenRateYbjs : awf.anwenRateJyjs),
                            freeRate2: taxCalculationMethod == 1 ? awf.anwenRateYbjs : awf.anwenRateJyjs,
                            libraryCode: awf.libraryCode,
                            description: ""
                        };
                        dispNo++;
                        result.push(freeRate);
                    } else {
                        result[dispNo - 2].floorSpace1 = gsBaseFreeRate[dispNo - 1].floorSpace;
                        // result[dispNo - 2].freeRate1 = NumberUtil.numberScale2(taxCalculationMethod == 1 ? gsBaseFreeRate[dispNo - 1].anwenRateYbjs : gsBaseFreeRate[dispNo - 1].anwenRateJyjs);
                        result[dispNo - 2].freeRate1 = taxCalculationMethod == 1 ? gsBaseFreeRate[dispNo - 1].anwenRateYbjs : gsBaseFreeRate[dispNo - 1].anwenRateJyjs;
                    }
                }
                break;
            default:
        }
        return result;
    }


    /**
     * 更新取费文件费率为标准值   可提供给前端使用修改单项或者工程项目费率说明时页面计算
     * @param args
     */
    async getBaseFreeRateData(args) {
        let {libraryCode,constructId,type,singleId,unitId,freeFileOld,qfCode} = args;

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        let freeFile= ConvertUtil.deepCopy(freeFileOld);
        let freeRate = new FreeRateModel();
        let param={};
        //查询计税方式
        let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let freeRateModel;
        // 将结果转换为数组
        let manageFeeRateData;
        freeRateModel=this.queryFreeRateModel(type,constructId,singleId,unitId);
        param.libraryCode=libraryCode;
        param.qfCode=qfCode;
        param.projectType=freeRateModel.projectType;
        param.taxCalculationMethod=taxCalculationMethod;
        //管理费
        manageFeeRateData=await this.service.gongLiaoJiProject.gljInitUnitProjectService.setManageFeeRateProfitRateByQfCode(param,freeRate);
        freeFile.manageFeeRate=manageFeeRateData.manageFeeRate;
        freeFile.manageFeeRateUpdate=false;

        //利润
        manageFeeRateData=await this.service.gongLiaoJiProject.gljInitUnitProjectService.setManageFeeRateProfitRateByQfCode(param,freeRate);
        freeFile.profitRate=manageFeeRateData.profitRate;
        freeFile.profitRateUpdate=false;

        //税金
        manageFeeRateData= await this.service.gongLiaoJiProject.gljBaseGsjRateService.queryByKindAndMethodAndRegion(GsjRateKindEnum.SJ.code, taxCalculationMethod, freeRateModel.projectLocation, true);
        freeFile.taxRate=manageFeeRateData.rate;
        freeFile.taxRateUpdate=false;

        //安全生产、文明施工费
        let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseAnwenRateService.queryByLibraryCodeGlj(libraryCode,freeRateModel.projectLocation,freeRateModel.roadSurfaceNum,freeRateModel.floorSpace,freeRateModel.municipalConstructionCost);
        let rate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.anwenRateYbjs : gsBaseFreeRate.anwenRateJyjs;
        freeFile.anwenRate=parseFloat(rate);
        freeFile.anwenRateUpdate=false;

        return freeFile;
    }


    async getBaseFreeRateDataV1(args) {
        let {libraryCode,constructId,type,singleId,unitId,freeFile} = args;

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        let freeRate = new FreeRateModel();
        let param={};
        //查询计税方式
        let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
        let freeRateModel;
        // 将结果转换为数组
        let manageFeeRateData;
        freeRateModel=this.queryFreeRateModel(type,constructId,singleId,unitId);
        param.libraryCode=libraryCode;
        param.projectType=freeRateModel.projectType;
        param.taxCalculationMethod=taxCalculationMethod;
        param.qfCode=freeFile.qfCode;
        //管理费
        manageFeeRateData=await this.service.gongLiaoJiProject.gljInitUnitProjectService.setManageFeeRateProfitRateByQfCode(param,freeRate);
        freeFile.manageFeeRate=manageFeeRateData.manageFeeRate;

        //利润
        manageFeeRateData=await this.service.gongLiaoJiProject.gljInitUnitProjectService.setManageFeeRateProfitRateByQfCode(param,freeRate);
        freeFile.profitRate=manageFeeRateData.profitRate;

        //税金
        manageFeeRateData= await this.service.gongLiaoJiProject.gljBaseGsjRateService.queryByKindAndMethodAndRegion(GsjRateKindEnum.SJ.code, taxCalculationMethod, freeRateModel.projectLocation, true);
        freeFile.taxRate=manageFeeRateData.rate;

        //安全生产、文明施工费
        let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseAnwenRateService.queryByLibraryCodeGlj(libraryCode,freeRateModel.projectLocation,freeRateModel.roadSurfaceNum,freeRateModel.floorSpace,freeRateModel.municipalConstructionCost);
        let rate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.anwenRateYbjs : gsBaseFreeRate.anwenRateJyjs;
        freeFile.anwenRate=rate;

        return freeFile;
    }







    /**
     * 添加定额时新增取费文件
     * @returns {Promise<void>}
     */
    async addFreeFileByDe(de,constructId,unitId){
        //非标准定额不需要取费文件
        if(ObjectUtils.isEmpty(de.standardId) || de.costMajorName=="随主工程"){
            return;
        }

        let unitQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
        let unitProject = await this.service.gongLiaoJiProject.gljProjectCommonService.getUnit(constructId, unitId);
        let freeKey = WildcardMap.generateKey(unitId,de.qfCode);
        let freeRate = unitQfbMap.get(freeKey);
        //如果有对应的取费文件 就不增加
        if(ObjectUtils.isNotEmpty(freeRate)){
            return;
        }

        let freeRateModel = new FreeRateModel();
        freeRateModel.constructId=constructId;
        freeRateModel.unitId=unitId;
        freeRateModel.qfCode=de.qfCode;
        freeRateModel.libraryCode=de.libraryCode;
        freeRateModel.sequenceNbr=Snowflake.nextId();

        let args={};
        args.libraryCode=de.libraryCode;
        args.qfCode=de.qfCode;
        args.type=3;
        args.singleId=unitProject.parentId;
        args.freeFileOld=freeRateModel;
        args.constructId=constructId;
        args.unitId=unitId;
        //恢复默认费率
        let newVar = await this.getBaseFreeRateData(args);
        let baseFeeFile =await this.service.gongLiaoJiProject.baseFeeFileService.getBaseFeeFile(newVar.qfCode);
        newVar.freeProfession=baseFeeFile.qfName;
        newVar.diffFreeRate=[];
        //更新取费表数据
        ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.UNIT_QFB, unitQfbMap.set(freeKey, ObjectUtils.cloneDeep(newVar)));
        //同步单项工程
        let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
        let freeKeySingle = WildcardMap.generateKey(unitProject.parentId, FunctionTypeConstants.SINGLE_QFB);
        let freeRateSingleModel = singleQfbMap.get(freeKeySingle);
        if(ObjectUtils.isEmpty(freeRateSingleModel.childFreeRate.get(freeRateModel.qfCode))){
            freeRateSingleModel.childFreeRate.set(freeRateModel.qfCode,  ObjectUtils.cloneDeep(newVar));
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));
            //多层级单项数据同步
            this.dealLevelSingleFee(constructId, unitProject.parentId, ObjectUtils.cloneDeep(newVar));
            //同步工程项目和单项工程数据
            let freeRateProjectModel = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_QFB);
            freeRateProjectModel.childFreeRate.set(freeRateModel.qfCode,  ObjectUtils.cloneDeep(newVar));
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.PROJECT_QFB, freeRateProjectModel);
        }

    }



    async dealLevelSingleFee(constructId, singleId,freeRateModel ) {
        let singleProject = ProjectDomain.getDomain(constructId).getProjectById(singleId);
        if (ObjectUtils.isNotEmpty(singleProject) && singleProject.type !== ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            // 同步至单项取费率
            let singleQfbMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_QFB);
            let freeKeySingle = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_QFB);
            let freeRateSingleModel = ObjectUtils.isNotEmpty(singleQfbMap.get(freeKeySingle))?singleQfbMap.get(freeKeySingle):{"childFreeRate":{}};
            //分别查询管理费利润、税金、安文费
            freeRateSingleModel.childFreeRate.set(freeRateModel.qfCode, freeRateModel);
            ProjectDomain.getDomain(constructId).functionDataMap.set(FunctionTypeConstants.SINGLE_QFB, singleQfbMap.set(freeKeySingle, freeRateSingleModel));

            if(ObjectUtils.isNotEmpty(singleProject.parentId)){
                await this.dealLevelSingleFee(constructId, singleProject.parentId, freeRateModel);
            }
        }
    }



    /**
     *  前端修改费率说明重新计算费率
     * @param args
     * @returns {Promise<*>}
     */
    async getFreeRateDataBefor(args) {
        let {libraryCode,constructId,freeFile,freeRateModel} = args;

        let precision1 = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let precision = precision1.FREE_RATE;

        let freeRateDescribe={};
        for(const frm of freeRateModel){
            if(frm.field=="projectType"){
                freeRateDescribe.projectType=frm.default;
            }
            if(frm.field=="projectLocation"){
                freeRateDescribe.projectLocation=frm.default;
            }
            if(frm.field=="roadSurfaceNum"){
                freeRateDescribe.roadSurfaceNum=frm.default;
            }
            if(frm.field=="floorSpace"){
                freeRateDescribe.floorSpace=frm.default;
            }
            if(frm.field=="municipalConstructionCost"){
                freeRateDescribe.municipalConstructionCost=frm.default;
            }
        }
        for(const ff of  freeFile){
            //修改费率说明数据结构
            let freeRate = new FreeRateModel();
            let param={};
            //查询计税方式
            let taxCalculationMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;
            // 将结果转换为数组
            let manageFeeRateData;
            param.libraryCode=ff.libraryCode;
            param.qfCode=ff.qfCode;
            libraryCode=ff.libraryCode;
            param.projectType=freeRateDescribe.projectType;
            param.taxCalculationMethod=taxCalculationMethod;
            //管理费
            manageFeeRateData=await this.service.gongLiaoJiProject.gljInitUnitProjectService.setManageFeeRateProfitRateByQfCode(param,freeRate);
            ff.manageFeeRate=manageFeeRateData.manageFeeRate;

            //利润
            manageFeeRateData=await this.service.gongLiaoJiProject.gljInitUnitProjectService.setManageFeeRateProfitRateByQfCode(param,freeRate);
            ff.profitRate=manageFeeRateData.profitRate;

             //税金
             manageFeeRateData= await this.service.gongLiaoJiProject.gljBaseGsjRateService.queryByKindAndMethodAndRegion(GsjRateKindEnum.SJ.code, taxCalculationMethod, freeRateDescribe.projectLocation, true);
             ff.taxRate=manageFeeRateData.rate;

            //安全生产、文明施工费
            let gsBaseFreeRate = await this.service.gongLiaoJiProject.gljBaseAnwenRateService.queryByLibraryCodeGlj(libraryCode,freeRateDescribe.projectLocation,freeRateDescribe.roadSurfaceNum,freeRateDescribe.floorSpace,freeRateDescribe.municipalConstructionCost);
            let rate = param.taxCalculationMethod == 1 ? gsBaseFreeRate.anwenRateYbjs : gsBaseFreeRate.anwenRateJyjs;
            ff.anwenRate=rate;
        }
        return freeFile;
    }



    queryFreeRateModel(type,constructId,singleId,unitId){
        let qfbMap;
        let freeKey ;
        let freeRateModel;
        switch (Number(type)) {
            case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
                freeRateModel= ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.PROJECT_FLSM);
                break;
            case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
                qfbMap= ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.SINGLE_FLSM);
                freeKey = WildcardMap.generateKey(singleId, FunctionTypeConstants.SINGLE_FLSM);
                freeRateModel = qfbMap.get(freeKey);
                break;
            case ProjectTypeConstants.PROJECT_TYPE_UNIT:
                qfbMap= ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_FLSM);
                freeKey = WildcardMap.generateKey(unitId);
                freeRateModel = qfbMap.get(freeKey);
                break;
            default:
         }
         return freeRateModel;
    }


    /**
     * 查询满足取费code数据，并进行libarayCode分组
     * @param args
     * @returns {Promise<void>}
     */
    async getFreeRateByLibaryCodeGroup(args){
        let baseFeeFileListProjects =  await this.service.gongLiaoJiProject.baseFeeFileService.getBaseFeeFileProjectByQfCode(args);
        let groupLibaryCodeMap = ArrayUtil.group(baseFeeFileListProjects, "libraryCode");
        return groupLibaryCodeMap;
    }


    // /**
    //  * 费率信息查询
    //  * @param libraryCode 定额库编码
    //  * @param projectType 所属工程专业
    //  * @param payTaxesAreas 纳税地区
    //  * @returns {Promise<ResponseData>}
    //  */
    // async searchFreeRateInfo(args) {
    //     let {libraryCode, projectType, payTaxesAreas} = args;
    //     let gljBaseFreeRate = await this.gljBaseFreeRateDao.findOne({
    //         where: {
    //             libraryCode: libraryCode,
    //             projectType: projectType,
    //             payTaxesAreas: payTaxesAreas,
    //         }
    //     });
    //     return gljBaseFreeRate;
    // }

}

GljBaseFreeRateService.toString = () => '[class GljBaseFreeRateService]';
module.exports = GljBaseFreeRateService;
