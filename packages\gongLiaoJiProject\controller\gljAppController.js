const {Controller} = require('../../../core');
const AppContext = require('../core/container/APPContext');
const {<PERSON><PERSON><PERSON>Window, dialog} = require('electron');
const {ResponseData} = require('../../../electron/utils/ResponseData');
const FileOperator = require('../core/tools/fileOperator/FileOperator');
const FileOperatorType = require('../constants/FileOperatorType');
const {ObjectUtils} = require("../utils/ObjectUtils");
const CommonConstants = require("../constants/CommonConstants");
const YGSOperator = require('../core/tools/fileOperator/YGLJOperator');
const {UserHistoryUtil} = require('../utils/UserHistoryUtil');
const {ProjectFileUtils} = require("../../../common/ProjectFileUtils");
const {Snowflake} = require("../utils/Snowflake");
const {ConvertUtil} = require("../../PreliminaryEstimate/utils/ConvertUtils");


class GljAppController extends Controller {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 清除内存里所有项目缓存
     */
    clearMM() {
        let contextMap = AppContext.clearContexts()
        return ResponseData.success("OK");
    }

    /**
     *
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async dumpAppContext(args) {
        let {constructId, sections} = args;
        let sectionsArray = [];
        if (ObjectUtils.isNotEmpty(sections)) {
            sectionsArray = sections.split(",");
        }

        let contextMap = AppContext.getAllContexts();
        let operate = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
        let contentArray = [];
        if (ObjectUtils.isEmpty(constructId)) {
            contextMap = Object.fromEntries(contextMap);

            for (const [key, value] of Object.entries(contextMap)) {
                contentArray.push(Object.fromEntries(operate.prepareContent(value, sectionsArray)));
            }
        } else {
            let projectDomain = contextMap.get(constructId);
            contentArray.push(Object.fromEntries(operate.prepareContent(projectDomain, sectionsArray)));
        }
        return ResponseData.success(contentArray);
    }

    /**
     *
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async importAndOpen(args) {
        if (ObjectUtils.isEmpty(args)) {
            args = {};
        }
        args.changeConstructId = true;
        let result = await this.import(args)
        if (result.code == 500) {
            return result;
        }
        let constructId = result.result;
        await this.service.gongLiaoJiProject.gljAppService.newEstimateProject(constructId)

        let projectRoot = AppContext.getContext(constructId).getRoot();
        //历史打开记录更新
        projectRoot.constructName = projectRoot.name;
        projectRoot.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
        ProjectFileUtils.writeUserHistoryListFile(projectRoot);
        // UserHistoryUtil.writeUserHistoryListFile({
        //   sequenceNbr: projectRoot.sequenceNbr,
        //   constructName: projectRoot.name,
        //   path: projectRoot.path,
        //   openTime:null
        // });
        return ResponseData.success(projectRoot.sequenceNbr);
    }

    /**
     *
     * @returns {Promise<*|undefined>}
     */
    async openProject(args) {
        let {constructId} = args;

        let projectDomain = AppContext.getContext(constructId);
        let projectRoot = projectDomain.getRoot();
        let defaultStoragePath = await this.service.gongLiaoJiProject.gljAppService.getSetStoragePath(projectRoot.name);

        const options = {
            title: '保存文件',
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [CommonConstants.GAISUAN_FILE_SUFFIX]} // 可选的文件类型
            ]
        };
        let result = dialog.showSaveDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return ResponseData.fail('未选中任何文件');
        }
        //获取选中的路径
        let filePath = result;

        //打开后就保存，保证filepath的存在
        if (!projectRoot.path) {
            projectRoot.path = filePath;
            projectRoot.constructName = projectRoot.name;
            projectRoot.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
            ProjectFileUtils.writeUserHistoryListFile(projectRoot);   //最近打开记录
            let operate = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
            await operate.saveByFullPath(projectDomain, filePath);
        }

        //无需返回
        await this.service.gongLiaoJiProject.gljAppService.newEstimateProject(constructId);

        return ResponseData.success(filePath);
    }

    /**
     *
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async save(args) {
        let {constructId, type} = args;
        let projectDomain = AppContext.getContext(constructId);
        let projectRoot = projectDomain.getRoot();
        let path = projectRoot.path;
        let operate = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
        let savedPath = null;
        if (type === 'saveAs') {
            let defaultStoragePath = await this.service.gongLiaoJiProject.gljAppService.getSetStoragePath(projectRoot.name);
            const options = {
                title: '保存文件',
                defaultPath: defaultStoragePath, // 默认保存路径
                filters: [
                    {name: '云算房', extensions: [CommonConstants.GAISUAN_FILE_SUFFIX]} // 可选的文件类型
                ]
            };
            let result = dialog.showSaveDialogSync(null, options);
            if (ObjectUtils.isEmpty(result)) {
                console.log("未选中任何文件");
                return ResponseData.fail('未选中任何文件');
            }
            //获取选中的路径
            path = result;

            let projectRootCopy = ConvertUtil.deepCopy(projectRoot);
            projectRootCopy.path = path;
            let constructName = projectRootCopy.constructName;
            let newConstructId = Snowflake.nextId();

            //如果保存地址等于当前文件地址，不替换id
            if (path === projectRoot.path) {
                newConstructId = projectRoot.sequenceNbr;
            }

            let index = projectRootCopy.path.lastIndexOf("\\");
            if (index !== -1) {
                constructName = projectRootCopy.path.substring(index + "\\".length); // 获取从分隔符之后的所有内容
                constructName = constructName.replace(CommonConstants.GAISUAN__FILE_DOT_SUFFIX, "");
                //更新历史记录信息
                projectRootCopy.constructName = constructName;
                projectRootCopy.sequenceNbr = newConstructId;
                projectRootCopy.biddingType = CommonConstants.GLJ_FILE_BIDDINGTYPE;
                ProjectFileUtils.writeUserHistoryListFile(projectRootCopy);
            }

            savedPath = await operate.saveAsByFullPath(projectDomain, path, newConstructId, constructName);
        } else {
            let operate = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
            savedPath = await operate.saveByFullPath(projectDomain, path);
        }

        return ResponseData.success(savedPath);
    }

    /**
     * 切换计税方式
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async switchProjectTax(args, clearRedo="清除历史记录- -切换计税方式,节约内存") {
        let {constructId, taxCalculationMethod} = args;
        let projectDomain = AppContext.getContext(constructId);
        let projectRoot = projectDomain.getRoot();
        let path = projectRoot.path;
        let fileName = projectRoot.name
        let jyjs = "(简易计税)"
        let ybjs = "(一般计税)"
        if (taxCalculationMethod === 0) {
            fileName = fileName + jyjs;
            // fileName = fileName.replaceAll(ybjs, "");
        }else if (taxCalculationMethod === 1) {
            fileName = fileName + ybjs;
            // fileName = fileName.replaceAll(jyjs, "");
        }
        let defaultStoragePath = await this.service.gongLiaoJiProject.gljAppService.getSetStoragePath(fileName);
        const options = {
            title: '保存文件',
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [CommonConstants.GAISUAN_FILE_SUFFIX]} // 可选的文件类型
            ]
        };
        let result = dialog.showSaveDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return ResponseData.fail('未选中任何文件');
        }
        if (path === result) {
            return ResponseData.fail("文件" + result + "已经打开，不能覆盖！");
        }

        let operate = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
        let savedPath = await operate.saveByTax(projectDomain, result, taxCalculationMethod)
        return ResponseData.success(savedPath);
    }

    /**
     * 仅支持概算窗口左侧树上方导入工程的功能，请不要乱用，注意changeConstructId的使用
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async import(args) {
        let changeConstructId = args.changeConstructId ? false : true;
        let defaultStoragePath = await this.service.gongLiaoJiProject.gljAppService.getSetStoragePath(null);

        const options = {
            properties: ['openFile'],
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [CommonConstants.GAISUAN_FILE_SUFFIX]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return ResponseData.fail('未选中任何文件');
        }
        //获取选中的路径
        let filePath = result[0];
        let ygsOperator = FileOperator.getOperator(FileOperatorType.File_TYPE_YGS);
        let fileProjectDomain = await ygsOperator.openFile(filePath, changeConstructId);
        if (fileProjectDomain) {
            fileProjectDomain.getRoot().path = filePath;
            return ResponseData.success(fileProjectDomain.constructId);
        } else {
            return ResponseData.fail('导入失败');
        }
    }

    /**
     *
     * @param arg
     * @returns {Promise<ResponseData>}
     */
    async getWinIdBySequenceNbr(arg) {
        let {sequenceNbr} = arg
        let formId = AppContext.getWindowMap().get(sequenceNbr);
        let browserWindow = BrowserWindow.fromId(formId);
        return ResponseData.success(browserWindow.webContents.id)
    }

}

GljAppController.toString = () => '[class gljAppController]';
module.exports = GljAppController;