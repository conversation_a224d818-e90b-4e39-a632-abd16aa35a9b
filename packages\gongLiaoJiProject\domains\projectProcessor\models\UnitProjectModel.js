const TreeProjectModel = require('./TreeProjectModel');

class UnitProjectModel extends TreeProjectModel {
    upCode  ; // '单位工程编号',
    upName  ;// '单位工程名称',
    uptotal  ; // '合计金额（元）',
    average  ; // '建筑面积',
    fbfxhj  ; // '分部分项合计（元）',
    fbfxrgf  ; // '分部分项人工费合计（元）',
    fbfxclf  ; // '分部分项材料费合计（元）',
    fbfxjxf  ; // '分部分项机械费合计（元）',
    fbfxlr  ; // '分部分项利润合计（元）',
    fbfxglf  ; // '分部分项管理费合计（元）',
    csxhj  ; // '措施项目合计（元）',
    csxrgf  ; // '措施项目人工费合计（元）',
    csxclf  ; // '措施项目材料费合计（元）',
    csxjxf  ; // '措施项目机械费合计（元）',
    csxglf  ; // '措施项目管理费合计（元）',
    csxlr  ; // '措施项目利润合计（元）',
    djcsxhj  ; // '单价措施项目合计（元）',
    djcsxrgf  ; // '单价措施项目人工费合计（元）',
    djcsxclf  ; // '单价措施项目材料费合计（元）',
    djcsxjxf  ; // '单价措施项目机械费合计（元）',
    djcsxglf  ; // '单价措施项目管理费合计（元）',
    djcsxlr  ; // '单价措施项目利润合计（元）',
    zjcsxhj  ; // '其他总价措施项目合计（元）',
    zjcsxrgf  ; // '其他总价措施项目人工费合计（元）',
    zjcsxclf  ; // '其他总价措施项目材料费合计（元）',
    zjcsxjxf  ; // '其他总价措施项目机械费合计（元）',
    zjcsxglf  ; // '其他总价措施项目管理费合计（元）',
    zjcsxlr  ; // '其他总价措施项目利润合计（元）',
    qtxmhj  ; // '其他项目清单计价合计（元）',
    qtxmrgf  ; // '其他项目清单计价人工费合计（元）',
    qtxmclf  ; // '其他项目清单计价材料费合计（元）',
    qtxmjxf  ; // '其他项目清单计价机械费合计（元）',
    qtxmglf  ; // '其他项目清单计价管理费和利润合计（元）',
    gfee  ; // '规费(元)',
    safeFee  ; // '安全文明施工费(元)',
    safeFl  ; // '安全文明施工费费率',
    sbf  ; // '设备费(元)',
    sqgczj  ; // '税前工程造价(元)',
    jxse  ; // '单位工程进项税额（元）',
    xxse  ; // '销项税额（元）',
    zzsynse  ; // '增值税应纳税额（元）',
    fjse  ; // '附加税费（元）',
    sj  ; // '税金（元）',
    xxsefl  ; // '销项税额费率',
    fjsffl  ; // '附加税费费率',
    fyZb  ; // '单位工程费用占比',
    spId  ; // '单项id',
    status  ; // '状态',
    createDate  ; // '创建时间',
    updateDate  ; // '更新时间',
    delFlag  ; // '是否删除',
    tenantId  ; // '租户ID',
    sortNo  ; // '排序字段',
    constructId  ; // '工程id',
    biddingType  ; // '招投标类型 0招标 1投标（招标Bidding Documents 投标Bidding Proposal）',
    importUrl  ; // '导入文件url',
    reportStorageUrls  ; // '报表文件存储url',
    reportUrl  ; // '导出报表url 文本格式',
    mainDeLibrary  ; // 主定额册编码
    secondInstallationProjectName  ; // 二级取费专业，只有安装工程有

    rgfId ; // '人工费政策文件id',

    fbfxzcf  ; //分部分项主材费合计（元）
    fbfxzgj  ; //分部分项暂估价合计（元）
    djcsxzcf  ; //单价措施项目主材费合计（元）
    zjcsxzcf  ; //其他总价措施项目主材费合计（元）
    qtxmzlje  ; //其他项目清单计价暂列金额合计
    qtxmzygczgj  ; //其他项目清单计价专业工程暂估价合计
    qtxmzcbfwf  ; //其他项目清单计价总承包服务费合计
    qtxmjrg  ; //其他项目清单计价计日工合计
    sbfsj  ; //设备费及其税金(不含甲供)
    sbfsjjg  ; //设备费及其税金(含甲供)
    gczjsbsj  ; //工程总造价含设备及其税金(不含甲供)
    gczjsbsjjg  ; //程总造价含设备及其税金(含甲供)

    // ------------------------------------造价分析字段
    gczj; //工程总造价(不含设备费及其税金)
    unitcost; //单方造价(元/m、元/㎡)
    budgetzjf; // 预算书-直接费  合计
    qzrgf;   // 其中-人工费
    qzclf;   // 其中-材料费
    qzjxf;   // 其中-机械费
    qzsbf;   // 其中-设备费
    qzzcf;   // 其中-主材费

    qtcsf;  // 其他措施费
    qtcsrgf;  // 其中-其他措施费-人工费
    qtcsclf;  // 其中-其他措施费-材料费
    qtcsjxf;  // 其中-其他措施费-机械费

    lxgcbgf;  // 零星工程包干费
    lxgcbgrgf;  // 其中-零星工程包干费-人工费
    lxgcbgclf;  // 其中-零星工程包干费-材料费
    lxgcbgsbf;  // 其中-零星工程包干费-机械费
    qyglf;  // 企业管理费
    gf;  // 规费
    lr;  // 利润
    dlf; // 独立费
    aqwmsgf; // 安全生产、文明施工费
    sj;  // 税金
    // ----------------------------------------------------------

    spyxcqz  ; // 索赔与现场签证
    jdrgf  ;   // 甲定人工费
    jdclf  ;   // 甲定材料费
    jdjxf  ;   // 甲定机械费
    jdzcf  ;   // 甲定主材费
    jdsbf  ;   // 甲定设备费
    yzgjgcl  ; // 预制构件工程量（10m3）
    gcsdf  ;   // 工程水电费
    jsjc  ;  // 结算价差合计
    jsjgclf  ;  // 结算甲供材料费
    jsjcrgf  ;  // 结算人工价差
    jsjcclf  ;  // 结算材料价差
    jsjcjxf  ;  // 结算机械价差
    jsjczgj  ;  // 结算暂估价差
    jsjcsbf  ;  // 结算设备费价差
    jsjcjgclf  ; // 结算甲供材料费价差
    jsjcjgzcf  ; // 结算甲供主材费价差
    jsjcjgsbf  ;   // 结算甲供设备费价差
    jchjjgfhsj  ;  // 价差合计(计规费和税金)
    jcgfhj  ;   // 价差规费合计(计规费和税金)
    jcaqwmsgfhj  ;   // 价差安全生产、文明施工费合计（计安、文施工费和税金)
    jcjxse  ;    // 价差进项税额
    jcclfjxse  ;   // 价差材料费进项税额
    jcjxfjxse  ;  // 价差机械费进项税额
    jcsbfjxse  ;   // 价差设备费进项税额
    jcaqwmsgf  ;   // 价差安全生产、文明施工费进项税额
    feeBuild ; //单价构成
    feeFiles; //取费文件
    itemBillProjects;//分部分项
    measureProjectTables;//措施项目
    djMeasureProjectTableArray;// （临时存放单价措施子分部和清单）
    zjMeasureProjectTableArray;// （临时存放总价措施和清单）
    awfMeasureProjectTableArray;// （临时存放安文费清单）
    otherProjects;//其他项目
    otherProjectDayWorks; //其他项目 - 计日工
    otherProjectProvisionals;//其他项目 - 暂列金
    otherProjectServiceCosts;//其他项目 - 总承包服务费
    otherProjectClZgjs;//其他项目 - 材料暂估价
    otherProjectSbZgjs;//其他项目 - 设备暂估价
    otherProjectZygcZgjs;//其他项目 - 专业工程暂估价
    otherProjectZyclSbs;//其他项目 - 主要材料设备
    otherProjectJgclSbs;//其他项目 - 甲供材料设备

    gfees;//规费
    safeFees;//安文费
    jcGfees;  //价差规费
    jcSafeFees;  //价差安文费
    constructProjectRcjs;//人材机汇总表 (存放了当前单位工程所有人材机明细数据)
    rcjDetailList;//人材机配比明细
    unitJBXX ;// 基本信息
    unitGCTZ ;// 工程特征
    organizationInstructions;// 编制说明
    unitCostCodePrices;//费用代码
    unitCostSummarys;//费用汇总
    unitInputTaxAmounts; //进项税额明细
    projectTaxCalculation ;// 计税方式

    cgCostMathCache  ;// 超高记取参数缓存
    cyCostMathCache  ;// 垂运记取参数缓存
    azCostMathCache  ;// 安装记取参数缓存

    freeRate; //费率
    isSingleMajorFlag;  // 是否进行单专业汇总  true 单专业  false 多专业汇总
    qfMajorTypeMoneyMap;  // 不同取费专业的工程造价
}
UnitProjectModel.toString = () => 'UnitProjectModel';
module.exports = UnitProjectModel;