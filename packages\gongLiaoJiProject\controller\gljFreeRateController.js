const { Controller } = require('../../../core');
const {ResponseData} = require("../utils/ResponseData");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");

/**
 * 费率 Controller
 */
class GljFreeRateController extends Controller {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 恢复费率
     * @param args
     */
    async recoverUnitFreeRate(args, redo="恢复默认- -费率") {
        await this.service.gongLiaoJiProject.gljFreeRateService.recoverUnitFreeRate(args);
        return ResponseData.success(true);
    }

    /**
     * 获取费率说明
     * @returns {Promise<void>}
     */
    async feeDescriptionData(args) {
        let result = [];
        switch (Number(args.type)) {
            case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
                // 获取工程项目取费表
                result = await this.service.gongLiaoJiProject.gljFreeRateService.getProjectQfbDes(args);
                break;
            case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
                // 获取单项工程取费表
                result = await this.service.gongLiaoJiProject.gljFreeRateService.getProjectQfbDesSingle(args);
                break;
            case ProjectTypeConstants.PROJECT_TYPE_UNIT:
                result = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbDes(args);
                break;
            default:
        }
        return ResponseData.success(result);
    }

    /**
     * 获取费率信息目录数据
     * @returns {Promise<void>}
     */
    async feeCatalogueData(args) {
        let result = await this.service.gongLiaoJiProject.gljFreeRateService.feeCatalogueData(args);
        return ResponseData.success(result);
    }

    /**
     * 获取工程项目费率表
     * @param args
     */
    async getProjectQfbList(args) {
        let result = [];
        switch (Number(args.type)) {
            case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
                // 获取工程项目取费表
                result = await this.service.gongLiaoJiProject.gljFreeRateService.getProjectQfbList(args);
                break;
            case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
                // 获取工程项目取费表
                result = await this.service.gongLiaoJiProject.gljFreeRateService.getProjectQfbListSingle(args);
                break;
            case ProjectTypeConstants.PROJECT_TYPE_UNIT:
                result = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbList(args);
                break;
            default:
        }
        // for (let item of result) {
        //     if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZGC) {
        //         item.freeProfession = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_JZGC_NAME
        //     }
        //     if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_ZSGC) {
        //         item.freeProfession = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_ZSGC_NAME
        //     }
        //     if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZGC) {
        //         item.freeProfession = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_AZGC_NAME
        //     }
        //     if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_SZGC) {
        //         item.freeProfession = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_SZGC_NAME
        //     }
        //     if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXJZ) {
        //         item.freeProfession = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXJZ_NAME
        //     }
        //     if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXAZ) {
        //         item.freeProfession = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_FXAZ_NAME
        //     }
        //     if (item.libraryCode === UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_YLLH) {
        //         item.freeProfession = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_YLLH_NAME
        //     }
        //     if (ObjectUtils.isEmpty(item.diffFreeRate)) {
        //         item.diffFreeRate = []
        //     }
        // }
        return ResponseData.success(result);
    }

    /**
     * 单位工程修改费率总览数据
     * @param freeRateModel
     */
    async updateUnitFreeRate(freeRateModel, redo="编辑- -费率总览") {
        let result = [];
        switch (Number(freeRateModel.type)) {
            // case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
            //     result = await this.service.gongLiaoJiProject.gljFreeRateService.updateProjectFreeRate(freeRateModel);
            //     break;
            // case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
            //     result = await this.service.gongLiaoJiProject.gljFreeRateService.updateProjectFreeRateSingle(freeRateModel);
            //     break;
            case ProjectTypeConstants.PROJECT_TYPE_UNIT:
                await this.service.gongLiaoJiProject.gljFreeRateService.updateUnitFreeRate(freeRateModel);
                let free = ProjectDomain.getDomain(freeRateModel.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
                result = ObjectUtils.getMapWithKeysStartingWith2(free, freeRateModel.unitId);
                break;
            default:
        }
        return ResponseData.success(result);
    }

    /**
     * 单位工程修改费率说明数据
     * @param freeRateModel
     */
    async updateUnitFreeRateDescribe(args, redo="编辑- -费率说明") {
        let responseData = await this.service.gongLiaoJiProject.gljFreeRateService.updateUnitFreeRateDescribe(args);
        return ResponseData.success(responseData);
    }




    /**
     * 修改工程项目费率(工程类别和纳税地区)
     * @param args
     */
    async changeProjectTypeAndAreas(args, redo="编辑- -费率地区及工程类别") {
        let result = [];
        switch (Number(args.type)) {
            case ProjectTypeConstants.PROJECT_TYPE_PROJECT:
                // 获取工程项目取费表
                result = await this.service.gongLiaoJiProject.gljFreeRateService.changeProjectTypeAndAreas(args);
                break;
            case ProjectTypeConstants.PROJECT_TYPE_SINGLE:
                // 获取工程项目取费表
                result = await this.service.gongLiaoJiProject.gljFreeRateService.changeProjectTypeAndAreasSingle(args);
                break;
            case ProjectTypeConstants.PROJECT_TYPE_UNIT:
                await this.service.gongLiaoJiProject.gljFreeRateService.changeUnitTypeAndAreas(args);
                let free = ProjectDomain.getDomain(args.constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QFB);
                result = ObjectUtils.getMapWithKeysStartingWith2(free, args.unitId);
                break;
            default:
        }

        return ResponseData.success(result);
    }

    /**
     * 统一应用
     * @param args
     * @param constructId 工程项目id
     * @param singleId 工程项目id
     * @param type 单位工程id   1 工程项目  2 单项工程
     * @param freeRateDescribe 手动修改后的费率说明数据
     * @param freeFileList 费率总览中的多条取费文件数据
     *
     */
    async unifiedApplication(args, redo="统一应用- -取费") {
        let{freeRateDescribe,freeFileList,type}=args
        if (ObjectUtils.isEmpty(args.singleId)) {
            await this.service.gongLiaoJiProject.gljFreeRateService.unifiedApplication(args);
        } else {
            await this.service.gongLiaoJiProject.gljFreeRateService.unifiedApplicationSingle(args);
        }
        return ResponseData.success(true);
    }

}
GljFreeRateController.toString = () => 'GljFreeRateController';
module.exports = GljFreeRateController;
