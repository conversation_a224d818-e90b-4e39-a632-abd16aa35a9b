<!--
 * @Author: wangru
 * @Date: 2023-05-23 15:38:48
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-07 17:42:07
 * @LastEditors: kong<PERSON><PERSON>ang
 * @LastEditTime: 2025-01-16 11:28:20
-->
<template>
  <!-- <fee-header class="head"></fee-header> -->
  <div class="table-content">
    <vxe-table
      align="center"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{ isHover: true, isCurrent: true }"
      :data="tableData"
      height="auto"
      ref="qtxmTable"
      @edit-actived="editActivedEvent"
      @edit-closed="editClosedEvent"
      :menu-config="menuConfigOptions"
      @menu-click="contextMenuClickEvent"
      keep-source
      show-overflow="tooltip"
      @cell-click="useCellClickEvent"
      class="table-edit-common"
      :cell-class-name="selectedClassName"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod({ rowIndex, row }) {
          if (rowIndex === 0 || ['CLZGJ', 'SBZGJ'].includes(row.type)) {
            //第一行不可编辑
            return false;
          }
          return true;
        },
      }"
    >
      <vxe-column
        field="dispNo"
        :width="columnWidth(60)"
        title="序号"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{ row.dispNo }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.dispNo"
            type="text"
            @blur="clear()"
            @keyup="row.dispNo = sortAndlength(row.dispNo, 10)"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="extraName"
        :width="columnWidth(180)"
        title="名称"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{ row.extraName }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.extraName"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="unit"
        :width="columnWidth(100)"
        title="单位"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{ row.unit }}</span>
        </template>
        <template #edit="{ row }">
          <vxeTableEditSelect
            :filedValue="row.unit"
            :list="projectStore.unitListString"
            @update:filedValue="
              newValue => {
                saveCustomInput(newValue, row, 'unit', $rowIndex);
              }
            "
          ></vxeTableEditSelect>
        </template>
      </vxe-column>
      <!--      <vxe-column-->
      <!--        field="amount"-->
      <!--        width="60"-->
      <!--        title="数量"-->
      <!--        :edit-render="{ autofocus: '.vxe-input&#45;&#45;inner' }"-->
      <!--      >-->
      <!--        <template #default="{ row }">-->
      <!--          <span v-if="['ZGJ'].includes(row.type)"></span>-->
      <!--          <span v-else>{{ row.amount }}</span>-->
      <!--        </template>-->
      <!--        <template #edit="{ row }">-->
      <!--          <span v-if="!row.isEdit">{{ row.amount }}</span>-->
      <!--          <span v-else-if="['ZGJ'].includes(row.type)"></span>-->
      <!--          <vxe-input-->
      <!--            v-else-->
      <!--            :clearable="false"-->
      <!--            v-model.trim="row.amount"-->
      <!--            type="text"-->
      <!--            :maxlength="10"-->
      <!--            @blur="-->
      <!--              row.amount = pureNumber(row.amount, 6);-->
      <!--              clear();-->
      <!--            "-->
      <!--          ></vxe-input>-->
      <!--        </template>-->
      <!--      </vxe-column>-->
      <vxe-column
        field="calculationBase"
        :min-width="columnWidth(150)"
        title="计算公式(计算基数)"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          {{ row.calculationBase }}
        </template>
        <template #edit="{ row }">
          <vxeTableEditTable
            tableType="QTXM"
            @showTable="showTable"
            :filedValue="row.calculationBase"
            :placement="'bottom'"
            @update:filedValue="
              newValue => {
                saveCustomInput(newValue, row, 'calculationBase', $rowIndex);
              }
            "
          ></vxeTableEditTable>
        </template>
      </vxe-column>
      <vxe-column
        field="instructions"
        width="150"
        :width="columnWidth(150)"
        title="基数说明"
        show-overflow="ellipsis"
      >
      </vxe-column>
      <vxe-column
        field="rate"
        :min-width="columnWidth(100)"
        title="费率(%)"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <!-- wh让吧判断去掉 -->
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-model.trim="row.rate"
            type="text"
            :maxlength="10"
            @blur="(row.rate = pureNumber(row.rate, 2)), clear()"
          ></vxe-input>
        </template>
      </vxe-column>

      <!--      <vxe-column-->
      <!--        field="taxRemoval"-->
      <!--        min-width="100"-->
      <!--        title="除税系数(%)"-->
      <!--        v-if="-->
      <!--          projectStore.deStandardReleaseYear !== '22' && Number(projectStore.taxMade) !== 0-->
      <!--        "-->
      <!--        :edit-render="{ autofocus: '.vxe-input&#45;&#45;inner' }"-->
      <!--      >-->
      <!--        <template #edit="{ row }">-->
      <!--          <vxe-input-->
      <!--            v-if="!['ZCBFWF', 'ZYGCZGJ', 'ZCBFWF', 'JRG'].includes(row.type)"-->
      <!--            :clearable="false"-->
      <!--            v-model.trim="row.taxRemoval"-->
      <!--            type="text"-->
      <!--            :maxlength="10"-->
      <!--            @blur="(row.taxRemoval = pureNumber(row.taxRemoval, 2)), clear()"-->
      <!--          ></vxe-input>-->
      <!--          <span v-else>-->
      <!--            {{ row.taxRemoval }}-->
      <!--          </span>-->
      <!--        </template>-->
      <!--      </vxe-column>-->
      <vxe-column
        field="type"
        :width="columnWidth(100)"
        title="费用类别"
        show-overflow="ellipsis"
        :edit-render="{}"
      >
        <template #default="{ row, index }">
          {{ row.type }}
        </template>
        <template #edit="{ row, index }">
          <vxe-select
            v-model="row.type"
            transfer
          >
            <vxe-option
              v-for="(item, index) in typeList"
              :key="index"
              :value="item.value"
              :label="item.name"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column
        field="total"
        :width="columnWidth(100)"
        title="合价"
      >
        <template #default="{ row }">
          <span>{{ row.total }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="jxTotal"
        :width="columnWidth(100)"
        title="进项合计"
        v-if="
          projectStore.deStandardReleaseYear !== '22' &&
          Number(projectStore.taxMade) !== 0
        "
      >
      </vxe-column>
      <vxe-column
        field="csTotal"
        :width="columnWidth(180)"
        title="除税合计"
        v-if="
          projectStore.deStandardReleaseYear !== '22' &&
          Number(projectStore.taxMade) !== 0
        "
      ></vxe-column>
      <vxe-column
        field="description"
        :width="columnWidth(180)"
        title="备注"
        :edit-render="{}"
      >
        <template #default="{ row }">
          <span>{{ row.description }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            :maxlength="2000"
            v-model.trim="row.description"
            type="text"
            @blur="clear()"
          ></vxe-input>
        </template>
      </vxe-column>
      <vxe-column
        field="aqwmsgf"
        width="150"
        :width="columnWidth(120)"
        title="安全生产、文明施工费"
        show-overflow="ellipsis"
      >
      </vxe-column>
      <vxe-column
        field="markSafa"
        :min-width="columnWidth(170)"
        title="计取安全生产、文明施工费"
        :cell-render="{}"
        fixed="right"
      >
        <template #default="{ row, rowIndex }">
          <vxe-checkbox
            v-model="row.markSafa"
            name="awf"
            @change="CheckChange('markSafa', row,'计取安全生产、文明施工费')"
            v-if="
              row.type !== '材料暂估价' &&
              row.type !== '设备暂估价' &&
              row.type !== '专业工程暂估价' &&
              rowIndex > 0
            "
          ></vxe-checkbox>
        </template>
      </vxe-column>
      <vxe-column
        field="putOntotalFlag"
        :min-width="columnWidth(100)"
        title="计入合价"
        :cell-render="{}"
        fixed="right"
      >
        <template #default="{ row, rowIndex }">
          <vxe-checkbox
            v-model="row.putOntotalFlag"
            name="taotalFlag"
            @change="CheckChange('putOntotalFlag', row,'计入合价')"
            v-if="rowIndex > 0"
          ></vxe-checkbox>
        </template>
      </vxe-column>

      <vxe-column
        field="markSj"
        :min-width="columnWidth(150)"
        title="计取税金"
        :cell-render="{}"
        fixed="right"
      >
        <template #default="{ row, rowIndex }">
          <vxe-checkbox
            v-model="row.markSj"
            name="sj"
            @change="CheckChange('markSj', row,'计取税金')"
            v-if="
              row.type !== '材料暂估价' &&
              row.type !== '设备暂估价' &&
              row.type !== '专业工程暂估价' &&
              rowIndex > 0
            "
          ></vxe-checkbox>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>
<script setup>
import FeeHeader from './FeeHeader.vue';
import {
  onMounted,
  onActivated,
  onUpdated,
  ref,
  watch,
  getCurrentInstance,
  reactive,
} from 'vue';
import { sortAndlength, pureNumber } from '@/utils/index';
import { columnWidth } from '@/hooks/useSystemConfig';
import { projectDetailStore } from '../../../../store/projectDetail';
import csProject from '../../../../api/csProject';
import qtxmCommon from './qtxmCommon';
import { insetBus } from '@/hooks/insetBus';
import { message } from 'ant-design-vue';
import { useCellClick } from '@/hooks/useCellClick';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
const { rateFormat } = useDecimalPoint();
import redo from '@/hooks/redo';

const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const projectStore = projectDetailStore();
let loading = ref(false);
let tableData = ref([]);
const activeKey = ref(1);
let qtxmTable = ref();
let operateType = ref(0); // 操作类型
const typeList = reactive([
  {
    name: '普通费用',
    value: 'PTFY',
  },
  {
    name: '暂列金额',
    value: 'ZLJE',
  },
  {
    name: '暂估价',
    value: 'ZGJ',
  },
  {
    name: '材料暂估价',
    value: 'CLZGJ',
  },
  {
    name: '设备暂估价',
    value: 'SBZGJ',
  },
  {
    name: '专业工程暂估价',
    value: 'ZYGCZGJ',
  },
  {
    name: '总承包服务费',
    value: 'ZCBFWF',
  },
  {
    name: '计日工',
    value: 'JRG',
  },
  {
    name: '索赔与现场签证',
    value: 'SPYQZ',
  },
]);
const clear = () => {
  //清除编辑状态
  const $table = qtxmTable.value;
  $table.clearEdit();
};

// 定位方法
const posRow = sequenceNbr => {
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  // currentInfo.value = { sequenceNbr };
  getOtherProjectList();
};
const limitNum = value => {
  if (typeof value !== 'string') return value;
  return value.replace(/[^(-?\d+)\.?(\d*)$]/g, '');
};
let oldValue = ref(null);
const editActivedEvent = ({ row, column }) => {
  oldValue.value = row[column.field]; // 保存修改前的数据
}
const editClosedEvent = ({ row, column }) => {
  const $table = qtxmTable.value;
  const field = column.field;
  let value = row[field];
  redo.addnoMatchedRedoList({
    sequenceNbr: row.sequenceNbr,
    columnTitle: column.title,
    oldValue: oldValue.value,
    newValue: row[field],
  });
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field === 'amount' && Number(row.originalAmount) === Number(row.amount)) {
    row.amount = row.originalAmount;
    return;
  }

  if (field === 'unit' && row.unit && row.unit.length > 2000) {
    row.unit = value.slice(0, 2000);
  }
  if (field === 'amount' && row.amount && row.amount.length > 20) {
    row.amount = value.slice(0, 20);
  }
  if (field === 'calculationBase' && row.calCopy === row.calculationBase) {
    return;
  }
  if ((field === 'rate' && Number(value) > 1000) || Number(value) < 0) {
    message.warn(`费率可输入数值范围：0-1000`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate' && row.rate != '') {
    row.rate = limitNum(row.rate); //数字
    row.rate = rateFormat(row.rate);
  }
  // if ($table.isUpdateByRow(row, field)) {
  upDate(row, column.field);
  // }
};

const otherProjectLineDataColl = () => {
  let isCurrentRow = qtxmTable.value.getCurrentRecord();
  let apiData = {
    operateType: operateType.value,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId, //单项ID
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    targetSequenceNbr: isCurrentRow?.sequenceNbr,
  };
  console.log('apiData', apiData);
  csProject.otherProjectLineDataColl(apiData).then(res => {
    console.log('接口结果', res);
    if (res.status === 200 && res.result) {
      getOtherProjectList();
    }
  });
};

const upDate = (row, field) => {
  let otherProject = {};
  otherProject[field] = row[field];
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeInfo?.parentId, //单项ID
    unitId: projectStore.currentTreeInfo?.id, //单位ID
    sequenceNbr: row.sequenceNbr,
    otherProject: otherProject,
  };
  console.log('其他项目修改传参', apiData);
  csProject.updateOtherProject(apiData).then(res => {
    console.log('其他项目修改结果', res);
    if (res.status === 200) {
      getOtherProjectList();
      message.success('修改成功');
    } else if (res.status === 500) {
      qtxmTable.value.revertData(row, field);
      message.error(res.message);
    }
  });
};
const getOtherProjectList = () => {
  loading.value = true;

  let apiData = qtxmCommon.requestParams();
  csProject.getOtherProjectList(apiData).then(res => {
    if (res.status === 200) {
      loading.value = false;
      tableData.value = res.result;
      tableData.value &&
        tableData.value.map(item => {
          item.markSafa = item.markSafa === 1 ? true : false;
          item.markSj = item.markSj === 1 ? true : false;
          // item.putOntotalFlag = item.putOntotalFlag === 1 ? true : false;
          item.calCopy = item.calculationBase;
          item.originalAmount = item.amount;
          if (
            item.type?.trim() === '材料暂估价' ||
            item.type?.trim() === '设备暂估价'
          ) {
            item.isEdit = false;
          } else {
            item.isEdit = true;
          }
        });
      console.log('********getOtherProjectList', res.result);
    }
  });
};

watch(
  () => projectStore.asideMenuCurrentInfo,
  () => {
    console.log('*****************其他项目');
    if (projectStore.asideMenuCurrentInfo?.sequenceNbr === 'qtxm00') {
      getOtherProjectList();
    }
  }
);
onMounted(() => {
  getOtherProjectList();
});

const changeEdit = val => {
  console.log('val', val);
};
const CheckChange = (column, row,columnTitle) => {
  console.log('复选框CheckChange', row);
  if (column !== 'putOntotalFlag') {
    row[column] = row[column] ? 1 : 0;
  }
  redo.addnoMatchedRedoList({
    sequenceNbr: row.sequenceNbr,
    columnTitle,
    checkType: row[column]
  });
  upDate(row, column);
};
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
};

const menuConfigOptions = {
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 'insert',
          name: '插入',
        },
        {
          code: 'delete',
          name: '删除',
          className: 'redFont',
        },
      ],
    ],
  },
};

const contextMenuClickEvent = ({ menu, row }) => {
  qtxmTable.value.setCurrentRow(row);
  switch (menu.code) {
    case 'insert':
      // 删除
      operateType.value = 1;
      break;
    case 'delete':
      // 删除
      operateType.value = 2;
      break;
  }
  otherProjectLineDataColl();
};

onActivated(() => {
  insetBus(bus, projectStore.componentId, 'qtxmStatistics', async data => {
    if (data.name === 'insert') operateType.value = 1;
    otherProjectLineDataColl();
    // if (data.name === 'delete') operateType.value = 2;
    // otherProjectLineDataColl();
  });
});

defineExpose({
  getTableData: getOtherProjectList,
  posRow,
});

projectStore.otherProItemGetList = getOtherProjectList;
</script>
<style lang="scss" scoped>
// @import './otherProject.scss';
.table-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
