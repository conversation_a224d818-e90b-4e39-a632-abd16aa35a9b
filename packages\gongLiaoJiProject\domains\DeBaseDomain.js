const BaseDomain = require('./core/BaseDomain');
const BranchProjectDisplayConstant = require("../constants/BranchProjectDisplayConstant");
const CommonConstants = require("../constants/CommonConstants");
const DeTypeConstants = require('../constants/DeTypeConstants');
const StandardDeModel = require('./deProcessor/models/StandardDeModel');
const ResourceKindConstants = require('../constants/ResourceKindConstants');
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const DeCommonConstants = require("../constants/DeCommonConstants");
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const DomainConstants = require('../constants/DomainConstants');
const CostDeMatchConstants = require('../constants/CostDeMatchConstants')
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const ResourceModel = require('./deProcessor/models/ResourceModel');
const ResourceConstants = require('../constants/ResourceConstants');
const { Snowflake } = require('../utils/Snowflake');
const RcjTypeEnum = require('../../../electron/enum/RcjTypeEnum');
const {NumberUtil} = require("../utils/NumberUtil");
const UnitUtils = require('../core/tools/UnitUtils');
const {ObjectUtils} = require("../utils/ObjectUtils");
const EE = require('../../../core/ee');
const {DeFlattener} = require("./calculators/de/DeFlattener");
const {DeCalculator} = require("./calculators/de/DeCalculator");
const {QDCalculator} = require("./calculators/de/QDCalculator");
const {FBCalculator} = require("./calculators/de/FBCalculator");
const {ResourceCalculator} = require("./calculators/resource/ResourceCalculator");
const WildcardMap = require('../core/container/WildcardMap');
const PropertyUtil = require('./utils/PropertyUtil');
const {ConvertUtil} = require("../utils/ConvertUtils");
const DeUtils = require("./utils/DeUtils");
const DeQualityUtils = require('./utils/DeQualityUtils');
const _ = require("lodash");

/**
 * 定额基类
 *
 */
class DeBaseDomain extends BaseDomain {

    allDeMap; //who are  you
    static FIELD_NAME_ROW_ID = "deRowId";

    constructor(ctx,type) {
      super();
      this.ctx = {};
      this.ctx.resourceMap = ctx.resourceMap;
      this.ctx.allDeMap = ctx.allDeMap;//兼容旧写法
      this.ctx.treeProject = ctx.treeProject;
      this.allDeMap = ctx[type];
    }

    /**
     * 防卫方法！！！，不要使用此方法的ctx, 此类为组合类
     * @param unitId
     */
    getRoot(unitId){
        let roots = this.allDeMap.getAllNodes().filter(item => item.type === DeTypeConstants.DE_TYPE_DEFAULT && item.unitId === unitId);
        if(roots&&roots.length === 1){
            return roots[0];
        }else{//有多个root时，
            let virtualRoot = new StandardDeModel();
            virtualRoot.unitId = unitId
            virtualRoot.type = DeTypeConstants.DE_TYPE_DEFAULT;
            virtualRoot.sequenceNbr = 0;
            virtualRoot.parentId = -1;
            virtualRoot.children = roots;
            virtualRoot.deName = DeTypeConstants.DE_TYPE_DEFAULT_LABEL;
            return virtualRoot;
        }
    }

    getDe(predicate){
        return this.allDeMap.getAllNodes().find(predicate);
    }

    getDeById(deId){
        return this.allDeMap.getNodeById(deId);
    }

    getDeTree(predicate){
        return PropertyUtil.shallowCopyAndFilterProperties(this.allDeMap.getAllNodes().filter(predicate), DeBaseDomain.avoidProperty).map(DeBaseDomain.filter4DeTree);
    }
    /**
     * 深度获取某个节点下的所有子节点 孙节点一直到最末级
     * @param constructId
     * @param deRowId
     * @param unitId
     * @param types
     * @returns {*}
     */
    getDeTreeDepth(constructId,unitId,deRowId,types,posId)
    {

        let de =  ObjectUtils.isEmpty(deRowId) ? this.getRoot(unitId) : this.getDeById(deRowId);
        let deList = [];
        this._getDeDepth(de,deList,types,posId);
        return PropertyUtil.shallowCopyAndFilterProperties(deList,DeBaseDomain.avoidProperty).map(DeBaseDomain.filter4DeTree);
    }

    /**
     * 内部方法 外部勿用
     * @param de
     * @param deList
     * @param types
     */
    _getDeDepth(de,deList,types,posId)
    {
        if(ObjectUtils.isNotEmpty(de)) {
            if(ObjectUtils.isEmpty(types) || types.includes(de.type))
            {
                deList.push(de);
                //保证数据显示主材或设备
                if(de.isExistedZcSb === CommonConstants.COMMON_YES &&(de.type === DeTypeConstants.DE_TYPE_DE
                || de.type === DeTypeConstants.DE_TYPE_USER_DE
                || (de.type === DeTypeConstants.DE_TYPE_DELIST&&de.displaySign === BranchProjectDisplayConstant.open))){
                    this._addZcSb2De(de,deList);
                }
            }

            let deItem;
            if(de.sequenceNbr === 0){
                deItem = de;
            }else{
                deItem = this.getDeById(de.sequenceNbr);
            }
            if (deItem.children.length)
            {
                if (posId){
                    this._getAncestorIds(deItem, posId).map(de=>{
                        de.displaySign = BranchProjectDisplayConstant.open
                    })
                }
                if (de.displaySign === BranchProjectDisplayConstant.open) {
                    let childrens = deItem.children;
                    //排序
                    childrens.sort((a,b)=>a.index-b.index);
                    for (let subDe of childrens) {
                        this._getDeDepth(subDe, deList, types);
                    }
                }
            }
        }
    }

    /**
     * 深度获取某个节点下的所有子节点 孙节点一直到最末级
     * @param constructId
     * @param deRowId
     * @param unitId
     * @param types
     * @returns {*}
     */
    getDeAllTreeDepth(constructId,unitId,deRowId,types)
    {
        let de =  ObjectUtils.isEmpty(deRowId) ? this.getRoot(unitId) : this.getDeById(deRowId);
        let deList = [];
        this._getDeAllDepth(de,deList,types);
        return PropertyUtil.shallowCopyAndFilterProperties(deList,DeBaseDomain.avoidProperty).map(DeBaseDomain.filter4DeTree);
    }

    /**
     * 内部方法 外部勿用
     * @param de
     * @param deList
     * @param types
     */
    _getDeAllDepth(de,deList,types)
    {
        if(ObjectUtils.isNotEmpty(de)) {
            if(ObjectUtils.isEmpty(types) || types.includes(de.type))
            {
                deList.push(de);
                //保证数据显示主材或设备
                if(de.isExistedZcSb === CommonConstants.COMMON_YES &&(de.type === DeTypeConstants.DE_TYPE_DE
                || de.type === DeTypeConstants.DE_TYPE_USER_DE
                || de.type === DeTypeConstants.DE_TYPE_DELIST)){
                    this._addZcSb2De(de,deList);
                }
            }
            let deItem;
            if(de.sequenceNbr === 0){
                deItem = de;
            }else{
                deItem = this.getDeById(de.sequenceNbr);
            }
            if (deItem.children.length)
            {
                for (let subDe of deItem.children) {
                this._getDeAllDepth(subDe, deList, types);
                }
            }
        }
    }

    /**
     * parentRow.type === DeTypeConstants.DE_TYPE_FB || parentRow.type === DeTypeConstants.DE_TYPE_ZFB
     *           || parentRow.type === DeTypeConstants.DE_TYPE_DEFAULT
     * 找到当前定额上层的分部或者子分部,直到返回默认的顶级Row
     * @returns {*}
     * @param deRow
     * @param parentList
     * @param types
     */
    findParents(deRow,parentList,types)
    {
        if(ObjectUtils.isNotEmpty(deRow.parentId) ) {
            if(deRow.type === DeTypeConstants.DE_TYPE_DEFAULT)
            {
                parentList.push(deRow);
                return;
            }

            let parentRow = this.getDeById(deRow.parentId);

            if (ObjectUtils.isNotEmpty(parentRow) && types.includes(parentRow.type))
            {
                parentList.push(parentRow);
                this.findParents(parentRow,parentList,types);
            }
        }
    }

  async extendQuantity(constructId, unitId, deRowId) {
    let deRow = this.getDeById(deRowId);
    if(ObjectUtils.isNotEmpty(deRow)) {
      let quantityExpression = deRow.quantityExpression
      let originalQuantity = ObjectUtils.isEmpty(quantityExpression)? deRow.originalQuantity: quantityExpression;
      if(ObjectUtils.isNotEmpty(originalQuantity)){
        await this.updateQuantity(constructId, unitId, deRowId, originalQuantity, false, true, false);
      }

    }
  }
    /**
     * 递归查询子节点 并可通过类型过滤 返回找到的row 数组.
     * @param deRow
     * @param childRowList
     * @param types
     */
    findChilds(deRow,childRowList,types)
    {
        if(ObjectUtils.isNotEmpty(deRow) && ObjectUtils.isNotEmpty(deRow.children))
        {
            for (let child of deRow.children)
            {
                if(types.includes(child.type))
                {
                    childRowList.push(child);
                    this.findChilds(child,childRowList,types);
                }
            }
        }
    }

    async updateDe(deModel, updateResouceKind=false,isFront = false){
        let {service} = EE.app;
        let de = this.getDeById(deModel.sequenceNbr);
        let changeMaterialCode = false;
        if(updateResouceKind
          && (de.type === DeTypeConstants.DE_TYPE_RESOURCE || de.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)
          && (de.deName !== deModel.deName || de.specification !== de.specification)){
            changeMaterialCode = true;
        }
        if (ObjectUtils.isNotEmpty(de)) {
          deModel.index = de.index;//不可更改index值;
          deModel.children = de.children;//不可更改children值;
          de.updateValue(deModel);
        } else {
          throw Error('没有找到工程:' + deModel.sequenceNbr);
        }
        if(changeMaterialCode){
          let rcjs = this.ctx.resourceMap.findByValueProperty(DeBaseDomain.FIELD_NAME_ROW_ID,de.sequenceNbr);
            let rcjDetail = rcjs.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES).value;
            rcjDetail.materialName = de.deName;
            await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId:de.constructId, singleId:null, unitId:de.unitId
              , deId:de.sequenceNbr
              , rcjDetailId:rcjDetail.sequenceNbr
              , constructRcj:{
                materialName:rcjDetail.materialName
            }});
        }
        if(updateResouceKind){
          let rcjs = this.ctx.resourceMap.findByValueProperty(DeBaseDomain.FIELD_NAME_ROW_ID,de.sequenceNbr);
          if(ObjectUtils.isNotEmpty(rcjs)){
            let rcjDetailMap = rcjs.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES);
            if(ObjectUtils.isNotEmpty(rcjDetailMap)){
              let  rcjDetail = rcjDetailMap.value;
              // rcjDetail.kind = de.deResourceKind;
              // rcjDetail.specification = de.specification;
              await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId:de.constructId, singleId:null, unitId:de.unitId
                , deId:de.sequenceNbr
                , rcjDetailId:rcjDetail.sequenceNbr
                , constructRcj:{
                  kind: de.deResourceKind,
                  specification:  de.specification
                }});

            }
          }
        }
        let notifyResource = false;
        if(updateResouceKind
            && (de.type === DeTypeConstants.DE_TYPE_RESOURCE || de.type === DeTypeConstants.DE_TYPE_USER_RESOURCE)
            && de.deResourceKind !== deModel.deResourceKind){
          notifyResource = true;
        }
        if(notifyResource){
          let rcjs = this.ctx.resourceMap.findByValueProperty(DeBaseDomain.FIELD_NAME_ROW_ID,de.sequenceNbr);
          rcjs[0].value.type = this._getRcjTypeEnumDescByCode(deModel.deResourceKind);
          rcjs[0].value.kind = deModel.deResourceKind;

          this.notify({constructId: de.constructId, unitId: de.unitId, deRowId: de.sequenceNbr}, false);

          if(isFront){
            //自动计算=各种记取+费用汇总通知
            await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
              unitId: de.unitId,
              singleId: null,
              constructId:de.constructId
            });
          }else{
            service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
              constructId: de.constructId,
              unitId: de.unitId,
              qfMajorType: de.qfCode
            });
          }
        }else{
          de.updateDate =  Date.now();
          if(isFront){
            //自动计算=各种记取+费用汇总通知
            await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
              unitId: de.unitId,
              singleId: null,
              constructId:de.constructId
            });
          }
        }
    }
    /**
     * 更新取费装专业
     * @param constructId
     * @param unitId
     * @param deRowId
     * @param costFileCode
     * @param costMajorName
     * @returns {Promise<void>}
     */
    async updateChargingDiscipline(constructId, unitId, deRowId, costFileCode,costMajorName) {

      let {service} = EE.app;
      // let costFileCodeSet = new Set();
      // costFileCodeSet.add(costFileCode);
      let deRow = this.allDeMap.getNodeById(deRowId);
      if(ObjectUtils.isNotEmpty(deRow)) {
        let childRowList = [deRow];
        this.findChilds(deRow, childRowList, [DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_USER_DE,DeTypeConstants.DE_TYPE_RESOURCE]);
        //查一个默认的qfcode
        for (let child of childRowList) {
          // costFileCodeSet.add(child.qfCode);
          child.costFileCode = costFileCode;
          child.costMajorName = costMajorName;
          child.qfCode = costFileCode;
          await this.updateDe(child);
        }
        //同步取费文件
        await service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorAddFeeRate(constructId, unitId, deRow.libraryCode,costFileCode);
        // if(costFileCodeSet.size > 1){
          try {

          //自动计算=各种记取+费用汇总通知
          await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
            unitId: unitId,
            singleId: null,
            constructId:constructId
          });
            // for(let item of costFileCodeSet) {
              // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
              //   constructId: constructId,
              //   unitId: unitId,
              //   qfMajorType: costFileCode
              // });
            // }
              // 汇总预算书、措施项目、独立费、费用汇总，对应专业的取费表专业数据
              await service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorDelFeeRate(constructId, unitId, deRow.libraryCode,costFileCode);
          } catch (error) {
            console.error("捕获到异常:", error);
          }
        // }
        return deRow;
      }
      else{
        throw Error("未找到定额行.");
      }

    }

    /**
     * 删除定额
     * @param deRowId
     */
    async removeDeRow(deRowId, isCountCost = true) {
        try {
        let {service} = EE.app;

        let deRow = this.getDeById(deRowId);
        if(ObjectUtils.isEmpty(deRow))
        {
            return;
        }
        let deRowBack = ConvertUtil.deepCopy(deRow);

      //用户定额删除后记录在缓存中为了未来使用
      if(deRow.type === DeTypeConstants.DE_TYPE_USER_DE){
        this.add2UserDeBase(deRow.constructId,deRowBack);
      }
        this._removeRowRelatedDatas(deRow);
        this.allDeMap.removeNode(deRowId);

        if(deRow.type !== DeTypeConstants.DE_TYPE_DEFAULT){
            //此时核查父级, 应该是这里啊
            DeTypeCheckUtil.updateParentDeType(deRow,this.ctx);

            //记录，删除定额不用重新计算工程量？20240712其他是否又
            await this.notify(this.getDeById(deRow.parentId),false);

            //计算父级是否展开/折叠
            await this.calDeDisplaySign(deRow.parentId);

            if(isCountCost){
              //联动计算装饰超高人材机数量
              // await service.gongLiaoJiProject.gljDeService.calculateZSFee(deRow.constructId, deRow.unitId, true);
              //联动计算安装费
              // await service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAloneDelete(deRow.constructId, deRow.unitId, deRow, 'delete');
              await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
                constructId: deRow.constructId,
                unitId: deRow.unitId,
                singleId: null
              });

              // 同步更新费用代码
              // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
              //   constructId: deRow.constructId,
              //   unitId: deRow.unitId,
              //   qfMajorType: deRow.qfCode
              // });
            }
        }

        // 删除定额后，其他相关操作
        await service.gongLiaoJiProject.gljInitDeService.remove(deRowBack);
        await service.gongLiaoJiProject.gljRcjCollectService.dropShareCost(deRow);
        // 同步更新取费文件
        await service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorDelFeeRate(deRow.constructId, deRow.unitId, deRow.libraryCode,deRow.costFileCode);
        } catch (error) {
        console.error("捕获到异常:", error);
        }
    }


    add2UserDeBase(constructId, userDe) {
        let userDeBase = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_DE);
        if (ObjectUtils.isEmpty(userDeBase)) {
          userDeBase = [];
          this.functionDataMap.set(FunctionTypeConstants.PROJECT_USER_DE, userDeBase);
        }else{
          //五要素一致,
          let realKey = `${userDe.deCode}-${userDe.deName}-${userDe.displayType}-${userDe.unit}-${userDe.price}-${userDe.baseJournalPrice}`;
          userDeBase = userDeBase.filter(item=>{
            let key = `${item.deCode}-${item.deName}-${item.displayType}-${item.unit}-${item.price}-${item.baseJournalPrice}`;
            if(key !== realKey){
              return true;
            }
            return false;
          });
        }
        let rcjLists = this.ctx.resourceMap.findByValueProperty(DeBaseDomain.FIELD_NAME_ROW_ID,userDe.sequenceNbr);
        let rcjListCopy = [];
        if(ObjectUtils.isNotEmpty(rcjLists)){
          for(let rcjList of rcjLists){
            rcjListCopy.push(ConvertUtil.deepCopy(rcjList.value));
          }
        }
        delete userDe.parent;//删除父类
        delete userDe.parentId;//删除父类
        userDe.rcjList = rcjListCopy;
        userDe.sequenceNbr = Snowflake.nextId(); //id 重复，selectde时判断逻辑混淆，所有重新生成id
        userDeBase.push(userDe);
        userDeBase.sort((a,b)=>b.updateDate-a.updateDate);
        this.functionDataMap.set(FunctionTypeConstants.PROJECT_USER_DE,userDeBase);
      }
    /**
    * 删除相关数据
    * @param deRowModel
    */
    _removeRowRelatedDatas(deRowModel)
    {
        if(ObjectUtils.isEmpty(deRowModel))
        {
        return;
        }
        if (ObjectUtils.isNotEmpty(deRowModel)) {
        let childs = [];
        this._findDeRows(deRowModel, childs);
        this._removeRowRelatedDataById(childs);
        }
        this.ctx.resourceMap.removeByValueProperty(DeBaseDomain.FIELD_NAME_ROW_ID,deRowModel.sequenceNbr);
    }
    /**
     * 通过定额行ID删除下面所有对应的定额 人材机
     * @param deRow
     * @param relatedRowIds
     */
    _findDeRows(deRow,relatedRowIds)
    {
        for (let subDeRow of deRow.children)
        {
        relatedRowIds.push(subDeRow.sequenceNbr);
        if(subDeRow.type === DeTypeConstants.DE_TYPE_DELIST || subDeRow.type === DeTypeConstants.DE_TYPE_FB
            || subDeRow.type === DeTypeConstants.DE_TYPE_ZFB)
        {
            this._findDeRows(subDeRow,relatedRowIds);
        }

        }
    }
    /**
     * 删除相关rcj数据及定额
     * @param {*} relatedRowIds
     */
   _removeRowRelatedDataById(relatedRowIds)
   {
     for (let rowId of relatedRowIds)
     {
       this.ctx.resourceMap.removeByValueProperty(DeBaseDomain.FIELD_NAME_ROW_ID,rowId);
       this.allDeMap.removeNode(rowId);
     }
   }

    async calDeDisplaySign(deParentId) {
        let { service } = EE.app;
        if (ObjectUtils.isNotEmpty(deParentId)) {
        let parentDeRow = this.getDeById(deParentId);
        let sign = true;
        if (ObjectUtils.isNotEmpty(parentDeRow) && ObjectUtils.isNotEmpty(parentDeRow.children)) {
            sign = false;
        }
        if (parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST || parentDeRow.type === DeTypeConstants.DE_TYPE_DE || parentDeRow.type === DeTypeConstants.DE_TYPE_USER_DE) {
            let rcjLists = await service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(parentDeRow.constructId, parentDeRow.unitId, deParentId, parentDeRow.type);
            if (ObjectUtils.isNotEmpty(rcjLists)) {
            let filter = rcjLists.filter(p => p.kind == ResourceKindConstants.INT_TYPE_ZC || p.kind == ResourceKindConstants.INT_TYPE_SB);
            if (ObjectUtils.isNotEmpty(filter)) {
                sign = false;
            }
            }
        }

        if (sign && parentDeRow.displaySign === BranchProjectDisplayConstant.open) {
            parentDeRow.displaySign = BranchProjectDisplayConstant.noSign;
        }
        }
    }
    async notifyAll(constructId,unitId){

      let allNodes = this.allDeMap.getAllNodes();
      let deArrs = allNodes.filter(item=>item.unitId === unitId&&![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB,DeTypeConstants.DE_TYPE_DELIST].includes(item.type));
      for(let deRow of deArrs){
        let dc = DeCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId:deRow.sequenceNbr},this.ctx);
        await dc.analyze();
      }
      let qdIdList = allNodes.filter(item=>item.unitId === unitId&&[DeTypeConstants.DE_TYPE_DELIST].includes(item.type));
      for(let qdRow of qdIdList){
        let qc = QDCalculator.getInstance({constructId: constructId, unitId: unitId,deRowId:qdRow.sequenceNbr}, this.ctx);
        await qc.analyze();
      }
      let rootRows = allNodes.filter(item=>item.unitId === unitId&&DeTypeConstants.DE_TYPE_DEFAULT == item.type);
      for(let rootRow of rootRows){
        let fc = FBCalculator.getInstance({constructId: constructId, unitId: unitId, deRowId: rootRow.sequenceNbr},this.ctx);
        await fc.analyze();
      }
    }

    /**
     * 基于定额重新铺设工程量 人材机 定额的单价合价
     * @param deRow
     * @param reCalculateQuantity
     * @returns {Promise<void>}
     */
    async notify(deRow,reCalculateQuantity = true,filterTempRemoveRow=true,priceCodes=[]) {
        if(ObjectUtils.isEmpty(deRow)){
          return;
        }
        //第一步 重新计算该清单下所有子清单和定额的 工程量
        deRow = this.getDeById(deRow.deRowId);
        let parent = this.getDeById(deRow.parentId);
        if(ObjectUtils.isEmpty(deRow)){
        throw Error("无法找到定额 : " + deRow.sequenceNbr + ".");
        }
        if (reCalculateQuantity) {
            if(ObjectUtils.isEmpty(priceCodes)){ //获取编码
             priceCodes = await this.getQuantityExpressionCodes(deRow.constructId,deRow.unitId,this.functionDataMap);
            }
            let df = DeFlattener.getInstance(deRow, this.ctx,filterTempRemoveRow,priceCodes);
            await df.analyze();
            //数组顺序为从下到上，人材机计算可以分开计算，但是定额计算需要从下到上
            //重新计算当前定额或者清单下所有人材机
            for(let relatedDeDeRow of df.relatedDeRows){
                //只有定额下有人才机明细需要重新计算 而清单不用
                if(ObjectUtils.isNotEmpty(relatedDeDeRow) && (relatedDeDeRow.type != DeTypeConstants.DE_TYPE_ZFB
                && relatedDeDeRow.type != DeTypeConstants.DE_TYPE_FB
                && relatedDeDeRow.type != DeTypeConstants.DE_TYPE_DEFAULT )) {
                    //1.重新计算人材机
                    let rc = ResourceCalculator.getInstance({constructId: relatedDeDeRow.constructId, unitId: relatedDeDeRow.unitId, deRowId:relatedDeDeRow.sequenceNbr}, this.ctx);
                    await rc.analyze();
                    //人材机修改notify于定额notify此时分开，互补影响
                    // await this.resourceDomain.notify({constructId: relatedDeDeRow.constructId, unitId: relatedDeDeRow.unitId, deRowId}, this.ctx);
                }
            }
            let orderedDeRow = [];
            this._executeDeCalculator(deRow,orderedDeRow);
            for(let i=0;i<orderedDeRow.length;i++){
                if(orderedDeRow[i].type ===  DeTypeConstants.DE_TYPE_DELIST){
                let qc = QDCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId,deRowId:orderedDeRow[i].sequenceNbr}, this.ctx);
                await qc.analyze();
                }else{
                let dc = DeCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId:orderedDeRow[i].sequenceNbr},this.ctx);
                await dc.analyze();
                }
            }

        }
        else
        {
            let rc = ResourceCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId:deRow.sequenceNbr}, this.ctx);
            await rc.analyze();
            if(deRow.type === DeTypeConstants.DE_TYPE_DELIST){
                let qc = QDCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId,deRowId:deRow.sequenceNbr}, this.ctx);
                await qc.analyze();
            }else{
                let dc = DeCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId:deRow.sequenceNbr},this.ctx);
                await dc.analyze();
            }
        }

        if(ObjectUtils.isNotEmpty(parent) && parent.type === DeTypeConstants.DE_TYPE_DELIST)
        {
            let qdAllParentIds = DeUtils.findAllQdParendIds(parent.parentId,this.ctx,'all');
            let qdIdList =[];
            qdIdList.push(parent.sequenceNbr);
            qdIdList = qdIdList.concat(qdAllParentIds);
            for(let qdId of qdIdList){
                let qc = QDCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId,deRowId:qdId}, this.ctx);
                await qc.analyze();
            }
        }
        let parentId = deRow.parentId;
        if(deRow.type === DeTypeConstants.DE_TYPE_DEFAULT){
            parentId = deRow.sequenceNbr;
        }
        let fc = FBCalculator.getInstance({constructId: deRow.constructId, unitId: deRow.unitId, deRowId: parentId},this.ctx);
        await fc.analyze();
    }

    _executeDeCalculator(deRow,orderedDeRow){
        for(let child of deRow?.children){
          this._executeDeCalculator(child,orderedDeRow);
        }
        orderedDeRow.push(deRow);
    }
    /**
     * 初始化人材机补充
     * @param {*} newResource
     * @param {*} materialCode
     * @param {*} materialName
     * @param {*} price
     * @param {*} resQty
     * @param {*} deRow
     */
    initResourceByUserDe(newResource, materialCode, materialName, price, resQty = 1,deRow) {
        newResource.materialCode = materialCode;
        newResource.materialName = materialName;
        newResource.baseJournalPrice = price;
        newResource.baseJournalTaxPrice = price;
        newResource.marketPrice = price;
        newResource.marketTaxPrice = price;
        newResource.taxRate = ObjectUtils.isEmpty(deRow.taxRate)?null:deRow.taxRate;
        newResource.isDataTaxRate = ObjectUtils.isEmpty(deRow.taxRate)? 2:1;
        newResource.resQty = resQty;
        newResource.quantity = 0;
        newResource.unit = DeCommonConstants.COMMON_UNIT;
        newResource.totalNumber = 0;
        newResource.deRowId = deRow.sequenceNbr;
        newResource.deId = deRow.sequenceNbr;
        newResource.originalQty= resQty;// 创建新的人材机  原始消耗量为restqty
        newResource.levelMark =RcjCommonConstants.LEVELMARK_ZERO;
        newResource.specification =null;
        newResource.markSum =1;

        if (deRow.type === DeTypeConstants.DE_TYPE_USER_DE) {
            //补充定额下人材机：人工费税率置空不能编辑，其他类型置0
            if (newResource.kind == "1") {
                newResource.taxRate = null;
                newResource.isDataTaxRate = 2;
            } else {
                newResource.taxRate = 0;
                newResource.isDataTaxRate = 1;
            }
        }
        //调整rcj
      if ( ['RGFTZ', 'CLFTZ', 'JXFTZ'].includes(materialCode) ) {
        if (newResource.kind == 1) {
          newResource.taxRate = null;
          newResource.isDataTaxRate = 2;
        } else {
          newResource.taxRate = 0;
          newResource.isDataTaxRate = 1;
        }
      }

    }


  /**
   * 增加主材或设备到定额数据中
   * @param {*} de
   * @param {*} deList
   */
  _addZcSb2De(de,deList){

    let rootProject = this.ctx.treeProject.root;
    let isPricingTax = rootProject.projectTaxCalculation.taxCalculationMethod == '0';//1? 一般计税: 简易计税

    let subResources = this.ctx.resourceMap.findByValueProperty(DeBaseDomain.FIELD_NAME_ROW_ID,de.sequenceNbr);
    for(let rcj of subResources){
      if(rcj.value.kind == ResourceKindConstants.INT_TYPE_SB ){
        let deRow =  this._convertResource2De(de,rcj.value,isPricingTax);
        deRow.SSum = deRow.price;
        deRow.SDSum = deRow.baseJournalPrice;
        deRow.sTotalSum = deRow.totalNumber;
        deRow.sdTotalSum = deRow.baseJournalTotalNumber;
        deRow.adjustmentCoefficient = null;
        deList.push(deRow);
      }
      if(rcj.value.kind == ResourceKindConstants.INT_TYPE_ZC){
        let deRow =  this._convertResource2De(de,rcj.value,isPricingTax);
        deRow.ZSum = deRow.price;
        deRow.ZDSum = deRow.baseJournalPrice;
        deRow.zTotalSum = deRow.totalNumber;
        deRow.zdTotalSum = deRow.baseJournalTotalNumber;
        deRow.adjustmentCoefficient = null;
        deList.push(deRow);
      }
    };
  }

 /**
   * 通过code 查询定额 包括当前工程下的用户定额
   * @param constructId
   * @param unitId
   * @param deCode
   * @param deRowId
   * @returns {T}
   */
 async checkAndQueryDe(constructId, unitId, deCode,functionDataMap) {

    let {service} = EE.app;
    let unitDes = this.allDeMap.filter(item=>item.deCode && this._fixCode(item.deCode) === this._fixCode(deCode) && item.unitId === unitId
      && (item.type === DeTypeConstants.DE_TYPE_DELIST ||item.type === DeTypeConstants.DE_TYPE_DE || item.type == DeTypeConstants.DE_TYPE_RESOURCE
        || item.type === DeTypeConstants.DE_TYPE_USER_DE || item.type === DeTypeConstants.DE_TYPE_USER_RESOURCE||item.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE
        || item.type === DeTypeConstants.DE_TYPE_ZHUANSHI_FEE));
    unitDes = unitDes.filter(item=>!(item.type === DeTypeConstants.DE_TYPE_DELIST && item.isCsxmDe));//措施项过滤掉
    //用户缓存定额数据
    let userDeBases = functionDataMap.get(FunctionTypeConstants.PROJECT_USER_DE);
    if(ObjectUtils.isNotEmpty(userDeBases)) {

      let resultUserDes = userDeBases.filter(item => this._fixCode(item.deCode) === this._fixCode(deCode)  && item.unitId === unitId);
      if(ObjectUtils.isNotEmpty(unitDes)){
        unitDes = unitDes.concat(resultUserDes);
      }else{
        unitDes = resultUserDes;
      }
    }
    let rootProject = this.ctx.treeProject.root;
    let isPricingTax = rootProject.projectTaxCalculation.taxCalculationMethod == '0';//1? 一般计税: 简易计税
    let isPricingMethod = rootProject.pricingMethod == 0;//
    //人材机定额缓存
    let unitAllMemory = await service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructId, unitId);
    if(ObjectUtils.isNotEmpty(unitAllMemory)){
      let results = unitAllMemory.filter(item=>this._fixCode(item.materialCode) === deCode);
      let userDeBaseRcjs = functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
      if(ObjectUtils.isNotEmpty(userDeBaseRcjs)) {
        let resultUserDes = userDeBaseRcjs.filter(item => this._fixCode(item.materialCode) === this._fixCode(deCode)  && item.unitId === unitId);
        if(ObjectUtils.isNotEmpty(results)){
          results = results.concat(resultUserDes);
        }else{
          results = resultUserDes;
        }
      }
      if(ObjectUtils.isNotEmpty(results)){
        let rcjs = [];
        for(let item of results){
          let convertDeRow = this._convertResource2De({constructId,unitId,sequenceNbr:item.sequenceNbr,costFileCode:"",costMajorName:""},item,isPricingTax);
          convertDeRow.deResourceKind = item.kind;
          if(convertDeRow.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG){
            convertDeRow.type = DeTypeConstants.DE_TYPE_USER_RESOURCE;
          }else{
            convertDeRow.type = DeTypeConstants.DE_TYPE_RESOURCE;
          }

          let rcjItemArr = rcjs.filter(item=>item.sequenceNbr === convertDeRow.sequenceNbr || item.sequenceNbr.indexOf(convertDeRow.sequenceNbr)>-1);
          if(rcjItemArr&&rcjItemArr.length>0){
            convertDeRow.sequenceNbr += ('__'+ rcjItemArr.length)
          }
          if(ObjectUtils.isEmpty(convertDeRow.displayType)){
            convertDeRow.displayType = "";
          }
          rcjs.push(convertDeRow)
        }

        if(ObjectUtils.isNotEmpty(unitDes)){
          unitDes = unitDes.concat(rcjs);
        }else{
          unitDes = rcjs;
        }
      }
    }
    let result = await service.gongLiaoJiProject.gljBaseDeService.getDeAndRcjByDeCode(constructId, unitId, deCode);
    if(ObjectUtils.isEmpty(result)){
      let rcjItem = await service.gongLiaoJiProject.gljBaseRcjService.getRcjByCode(constructId, unitId, deCode);
      let resouceKind = null;
      if(ObjectUtils.isNotEmpty(rcjItem)){
        let baseRCJ = await service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:rcjItem[0].sequenceNbr});
        // baseRCJ.marketPrice = baseRCJ.price;
        resouceKind = baseRCJ.kind;
        let convertDeRow = this._convertResource2De({constructId,unitId,sequenceNbr:baseRCJ.sequenceNbr,costFileCode:"",costMajorName:""},baseRCJ,isPricingTax,true);
        result = convertDeRow;
      }
      if(ObjectUtils.isNotEmpty(result)){
        result.type = DeTypeConstants.DE_TYPE_RESOURCE;
        result.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;
        result.deResourceKind = resouceKind;
      }
    }else{

      for(let resultChild of result){
        if(resultChild.isExistDe == CommonConstants.COMMON_YES )
        {
          resultChild.type = DeTypeConstants.DE_TYPE_DELIST;
          resultChild.displayType = DeTypeConstants.DE_TYPE_DELIST_LABEL;
        }
        else {
          resultChild.type = DeTypeConstants.DE_TYPE_DE;
          resultChild.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;
        }
      }
    }
    let dbArr = [];
    if(ObjectUtils.isNotEmpty(result)){
      if(result instanceof Array){
        dbArr.push(...result);
      } else{
        dbArr.push(result);
      }
    }

    //无奈，
    unitDes.forEach(element => {
      if(ObjectUtils.isNotEmpty(element.deName)){
        element.deName = element.deName.trim();
      }
      if(ObjectUtils.isEmpty(element.displayType) && ObjectUtils.isNotEmpty(element.type) && element.type == DeTypeConstants.DE_TYPE_RESOURCE){
        element.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;
      }
    });
    if(ObjectUtils.isNotEmpty(unitDes)){
      unitDes.sort((a,b)=>b.updateDate-a.updateDate);
      unitDes = this._mergeDuplicates(unitDes,isPricingMethod);
    }
    if(ObjectUtils.isNotEmpty(unitDes) && ObjectUtils.isNotEmpty(dbArr)){
      let mapKey = new Map();
      for(let dbItem of dbArr){
        let dbUnit = ObjectUtils.isEmpty(dbItem.unit)?'':dbItem.unit;
        let dbDisplayType = ObjectUtils.isEmpty(dbItem.displayType)?'':dbItem.displayType;
        let key = `${dbItem.deCode}-${dbItem.deName}-${dbDisplayType}-${dbUnit}-${dbItem.baseJournalPrice}`;
        //匹配定额本身及子级
        if(isPricingTax){
          key = `${dbItem.deCode}-${dbItem.deName}-${dbDisplayType}-${dbUnit}-${dbItem.baseJournalTaxPrice}`;
        }
        let childMapKey = new Map();
        if(ObjectUtils.isNotEmpty(dbItem.subDeList)){
          for(let child of dbItem.subDeList){
            child.type = DeTypeConstants.DE_TYPE_DE;

            let childKey = `${child.deCode}-${child.deName}-${child.displayType}-${child.unit}-${child.price}`;
            if(isPricingMethod){
              childKey = `${child.deCode}-${child.deName}-${child.displayType}-${child.unit}-${child.baseJournalPrice}`;
            }
            childMapKey.set(child.deCode,childKey);
          }
        }
        mapKey.set(key,childMapKey);
      }
      unitDes = unitDes.filter(item=>{
        let itemKey =  `${item.deCode}-${item.deName}-${item.displayType}-${item.unit}-${item.price}`;
        if(isPricingMethod){
          itemKey =  `${item.deCode}-${item.deName}-${item.displayType}-${item.unit}-${item.baseJournalPrice}`;
        }
        if(!mapKey.has(itemKey)){
          return true;
        }
        if(ObjectUtils.isNotEmpty(item.children)){
          let childMapKey = mapKey.get(itemKey);
          if(item.children.length != childMapKey.size){
            return true;
          }
          let childMatch = true;
          for(let child of item.children){
            let childKey = `${child.deCode}-${child.deName}-${child.displayType}-${child.unit}-${child.price}`;
            if(isPricingMethod){
              childKey = `${child.deCode}-${child.deName}-${child.displayType}-${child.unit}-${child.baseJournalPrice}`;
            }
            let childDbkey = childMapKey.get(child.deCode);
            if(childKey !== childDbkey){
              childMatch = false;
            }
          }
          return !childMatch;
        }
        return false;
      });
    }
    if(ObjectUtils.isNotEmpty(result)){
       dbArr = PropertyUtil.shallowCopyAndFilterProperties(dbArr,BaseDomain.avoidProperty).map(DeBaseDomain.filter4DeTree)
    }
    if(ObjectUtils.isNotEmpty(unitDes)){
       unitDes = PropertyUtil.shallowCopyAndFilterProperties(unitDes,BaseDomain.avoidProperty).map(DeBaseDomain.filter4DeTree)
    }
    return  {local:unitDes,db:dbArr};
   }

    /**
   * 人材机转为定额
   * @param {*} parentDeRow
   * @param {*} resource
   * @returns
   */
  _convertResource2De(parentDeRow,resource,isTax,isBaseRCJ=false){

    let deRow = new StandardDeModel(parentDeRow.constructId,parentDeRow.unitId,resource.sequenceNbr,parentDeRow.sequenceNbr, DeTypeConstants.SUB_DE_TYPE_DE);
    deRow.kind = DeTypeConstants.SUB_DE_TYPE_DE;
    deRow.deResourceKind = parseInt(resource.kind);//增加补充人材机类型
    deRow.isDeResource = CommonConstants.COMMON_YES;
    PropertyUtil.copyProperties(resource ,deRow, [...BaseDomain.avoidProperty,"sequenceNbr","parentId","deResourceKind","kind","type","isDeResource"]);
    if(isBaseRCJ){
      deRow.price = ObjectUtils.isEmpty(resource.marketPrice)||resource.marketPrice == 0?resource.baseJournalPrice:resource.marketPrice;
    }else{
      deRow.price = resource.marketPrice;
    }
    deRow.baseJournalPrice = resource.baseJournalPrice;
    deRow.totalNumber = resource.total;
    deRow.baseJournalTotalNumber = resource.baseJournalTotal;
    if(isTax){
      deRow.price = resource.marketTaxPrice;
      if(isBaseRCJ){
        deRow.price = ObjectUtils.isEmpty(resource.marketTaxPrice)||resource.marketTaxPrice == 0?resource.baseJournalTaxPrice:resource.marketTaxPrice;
      }
      deRow.baseJournalPrice = resource.baseJournalTaxPrice;
      deRow.totalNumber = resource.totalTax;
      deRow.baseJournalTotalNumber = resource.baseJournalTotalTax;
    }
    deRow.quantity = resource.totalNumber;//保留4位
    deRow.originalQuantity = resource.totalNumber;
    deRow.deName = resource.materialName;
    deRow.deCode = resource.materialCode;
    deRow.costFileCode = parentDeRow.costFileCode;
    deRow.costMajorName = parentDeRow.costMajorName;
    deRow.standardId = resource.rcjId;
    deRow.isNumLock = resource.isNumLock;//特殊处理

    return deRow;
  }

  async _initQfAndMeasureType(unitId, deRow,service,initMeasureType = true){

    if(initMeasureType){
      //施工组织措施类别
      let unitProject = this.ctx.treeProject.getNodeById(unitId);
      let baseFeeFileRelation = await service.gongLiaoJiProject.baseFeeFileService.getFeeFileRelationByQfCode(unitProject.qfMajorType);
      if(ObjectUtils.isNotEmpty(baseFeeFileRelation)){
        deRow.measureType = baseFeeFileRelation.cslbName;
      }
    }
    //取费专业初始化
    await service.gongLiaoJiProject.gljInitDeService.initDeFree(deRow);
  }
  _fixCode(code){
      if(ObjectUtils.isEmpty(code)){
        return code;
      }
      if(code.indexOf('#')>-1){
        code = code.split('#')[0];
      }
      code = code.toUpperCase();
      return code;
  }
  _mergeDuplicates(items,isPricingMethod){
    let mapKey = new Map();
    for(let item of items){
      let itemKeys = []
      if(ObjectUtils.isEmpty(item.displayType)){
        item.displayType='';
      }
      this._add5KeyWithChild(item,itemKeys,isPricingMethod);
      mapKey.set(item.sequenceNbr,itemKeys);
    }
    let filtered = [];
    for(let i=0; i< items.length; i++){
      let itemKey = mapKey.get(items[i].sequenceNbr);
      for(let j=i+1;j<items.length; j++){
        let value =  mapKey.get(items[j].sequenceNbr);
        if(_.isEqual(value,itemKey)){
          filtered.push(items[j].sequenceNbr);
        }
      }
    }
    return items.filter(item=>filtered.indexOf(item.sequenceNbr) === -1);
  }

  _add5KeyWithChild(item,itemKeys,isPricingMethod){
    let key = `${item.deCode}-${item.deName}-${item.displayType}-${item.unit}-${item.price}`;
    if(isPricingMethod){
      key = `${item.deCode}-${item.deName}-${item.displayType}-${item.unit}-${item.baseJournalPrice}`;
    }
    itemKeys.push(key);
    if(ObjectUtils.isNotEmpty(item.children)){
      let childs = item.children;
      childs.sort((a,b) => a.index-b.index);
      for(let child of childs){
        this._add5KeyWithChild(child,itemKeys,isPricingMethod);
      }
    }
  }

  /**
   * 追加定额下的人材机
   * @param deRcjRelationList
   * @param deModel
   * @param rcjList
   * @param isBaseRCJ
   */
  async attachDeRCJ(resourceDomain,deRcjRelationList, deModel,rcjList,isBaseRCJ, setResQty1 = false) {
    let {service} = EE.app;
    let childCodes = [];
    let initDeRcjNameList = [];
    let  taxMethod = this.ctx.treeProject.root.projectTaxCalculation.taxCalculationMethod;
    for (let rcj of deRcjRelationList) {
      //constructId,unitId,sequenceNbr,parentId,type
      let resource = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(), deModel.sequenceNbr, rcj.kind);
      PropertyUtil.copyProperties(rcj, resource, ['sequenceNbr']);
      resource.rcjId = rcj.rcjId;
      resource.deRowId = deModel.sequenceNbr;
      resource.parentId = deModel.sequenceNbr;
      resource.deId = deModel.sequenceNbr;
      resource.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
      resource.specification= rcj.specification;
      resource.producer = null;
      resource.manufactor = null;
      resource.brand = null;
      if(setResQty1){
        resource.resQty = 1;//添加人材机定额时定额人材机的消耗量应该为1
      }
      resource.deliveryLocation = null;
      resource.qualityGrade= null;
      resource.remark= null;
      resource.markSum = RcjCommonConstants.MARKSUM_JX;
      resource.totalNumber = RcjCommonConstants.TOTALNUMBER_DEFAULT;
      resource.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
      resource.libraryCode = rcj.libraryCode;
      resource.originalQty = rcj.resQty;
      resource.sourcePrice = RcjCommonConstants.SOURCEPRICE;
      resource.isNumLock = RcjCommonConstants.ISNUMLOCK;
      resource.numLockNum = 0;
      resource.rcjDetailEdit = RcjCommonConstants.RCJDETAILEDIT;

      if(ObjectUtils.isNotEmpty(isBaseRCJ) && ObjectUtils.isEmpty( resource.dePrice ))
      {
        resource.dePrice = rcj.price;
        resource.marketPrice = rcj.price;
        resource.levelMark = rcj.levelMark;
        resource.unit = rcj.unit;
      }
      //工料机定额人材处理
      if(rcj.isDeResource === 1){
        resource.marketPrice = rcj.baseJournalPrice ;
        resource.marketTaxPrice = rcj.baseJournalTaxPrice ;
        resource.isDataTaxRate = rcj.isDataTaxRate ;
        resource.taxRate = rcj.taxRate ;
        service.gongLiaoJiProject.gljRcjService.rcjCalculateOriginalData(resource,rcj);
        resource.isFyrcj = rcj.isFyrcj ;
      }
      if(ObjectUtils.isNotEmpty(rcjList))
      {
        let baseRcj = rcjList.find(item => item.sequenceNbr === rcj.rcjId);
        resource.dePrice = baseRcj.price;
        resource.marketPrice = baseRcj.price;
        resource.price=baseRcj.price;
        resource.unit = baseRcj.unit;
        resource.kindSc = baseRcj.kindSc;
        resource.transferFactor = baseRcj.transferFactor;
        resource.scCount = NumberUtil.multiply(resource.totalNumber, resource.transferFactor);
        if (ObjectUtils.isNotEmpty(baseRcj)) {
          resource.specification= baseRcj.specification;
          resource.levelMark = baseRcj.levelMark;
          rcj.levelMark =  resource.levelMark ;
        }
        //工料机含税价格
        resource.baseJournalPrice = baseRcj.baseJournalPrice ;
        resource.baseJournalTaxPrice = baseRcj.baseJournalTaxPrice ;
        resource.marketPrice = baseRcj.baseJournalPrice ;
        resource.marketTaxPrice = baseRcj.baseJournalTaxPrice ;
        resource.isDataTaxRate = baseRcj.isDataTaxRate ;
        resource.taxRate = baseRcj.taxRate ;
        service.gongLiaoJiProject.gljRcjService.rcjCalculateOriginalData(resource,baseRcj);
        resource.isFyrcj = baseRcj.isFyrcj ;
      }
      for (let key in RcjTypeEnum) {
        if (RcjTypeEnum[key].code == resource.kind) {
          resource.type =  RcjTypeEnum[key].desc;
        }
      }
      if (!ObjectUtils.isEmpty(resource.levelMark) && resource.levelMark === ResourceConstants.LEVEL_MARK_PB_CL
          || resource.levelMark === ResourceConstants.LEVEL_MARK_PB_JX) {
        await this.attachPBs(rcj, deModel.constructId, deModel.unitId, deModel.sequenceNbr, resource.sequenceNbr,resourceDomain);
        let subSourcePrice = '';
        for(let index = 0; index < rcj.pbs.length; index++) {
          let item=rcj.pbs[index];
          item.marketPrice=item.dePrice;
          item.ifDonorMaterial = RcjCommonConstants.DEFAULT_IFDONORMATERIAL;
          item.ifLockStandardPrice = RcjCommonConstants.IFLOCKPRICE_DEFAULT;
          item.markSum =RcjCommonConstants.MARKSUM_JX;
          item.originalQty = item.resQty;
          item.unitId = resource.unitId;
          item.constructId = resource.constructId;
          for (let key in RcjTypeEnum) {
            if (RcjTypeEnum[key].code == item.kind) {
              item.type =  RcjTypeEnum[key].desc;
            }
          }
          service.gongLiaoJiProject.gljRcjService.processingMarketPrice(item);
          if(ObjectUtils.isNotEmpty(item.sourcePrice)){
            subSourcePrice =  item.sourcePrice;
          }
          childCodes.push({code:item.materialCode,sequenceNbr:item.sequenceNbr,resQty:item.originalQty});
        }
        resource.sourcePrice = subSourcePrice
        resource.pbs = rcj.pbs;
      }

      // 市场价同步
      service.gongLiaoJiProject.gljRcjService.processingMarketPrice(resource);
      service.gongLiaoJiProject.gljRcjService.processingDonorMaterial(resource);
      //处理主材和设备,挂一个主材及设备定额
      let constructRcjArray= service.gongLiaoJiProject.gljRcjService.getAllRcj(resource);
      if(resource.kind == ResourceKindConstants.INT_TYPE_ZC || resource.kind == ResourceKindConstants.INT_TYPE_SB){
        //
        deModel.isExistedZcSb = CommonConstants.COMMON_YES;
        await service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCodeMemory(resource, true, constructRcjArray);
      }
      service.gongLiaoJiProject.gljRcjService.calculateTax(resource,taxMethod);
      let   rcjFind =constructRcjArray.find( item=> item.materialCode === resource.materialCode);
      if(ObjectUtils.isNotEmpty(rcjFind) && ObjectUtils.isNotEmpty(rcjFind.rcjId) ){
            resource.rcjId  =  rcjFind.rcjId ;
        }
      await service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCodeMemory(resource, true, constructRcjArray);

      resourceDomain.createResource(deModel.unitId, deModel.sequenceNbr, resource);
      if(isBaseRCJ){
        deModel.ifLockStandardPrice = resource.ifLockStandardPrice;
        deModel.markSum = resource.markSum;
      }
      let rcjNameObj = {};
      rcjNameObj.sequenceNbr = resource.sequenceNbr;
      rcjNameObj.initMaterialName = resource.materialName;
      rcjNameObj.code = resource.materialCode;
      rcjNameObj.kind = resource.kind;
      initDeRcjNameList.push(rcjNameObj);
      let itemChildCode = resource.materialCode;
      childCodes.push({code:itemChildCode,sequenceNbr:resource.sequenceNbr,resQty:resource.originalQty});
        if(rcj.isDeResource === 1){
            deModel.deCode=resource.materialCode;
        }
    }

    deModel.initChildCodes = childCodes;
    deModel.initDeRcjNameList = initDeRcjNameList;

  }
  /**
   *
   * @param rcj
   * @param constructId
   * @param unitId
   * @param deId
   * @param parentId
   * @returns {Promise<void>}
   */
  async attachPBs(rcj, constructId, unitId, deId, parentId,resourceDomain) {
    await resourceDomain.createResourcePBs(unitId, deId, parentId, rcj);
  }

  /**
   * 处理人材机调整数据
   * @param {*} resourceDomain
   * @param {*} constructId
   * @param {*} unitId
   * @param {*} deRow
   * @param {*} price
   * @param {*} resQtyR
   * @param {*} resQtyC
   * @param {*} resQtyJ
   * @param {*} originalQtyMap
   */
  _updatePriceCommon(resourceDomain,constructId, unitId,deRow,price,resQtyR,resQtyC,resQtyJ,originalQtyMap){
    let {service} = EE.app;
    let differenceResourceR = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_R);
    differenceResourceR.unit = "元";
    let differenceResourceC = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_C);
    differenceResourceC.unit = "元";
    let differenceResourceJ = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_J);
    differenceResourceJ.unit = "元";

    differenceResourceR.type = RcjTypeEnum['TYPE1'].desc;
    differenceResourceC.type = RcjTypeEnum['TYPE2'].desc;
    differenceResourceJ.type = RcjTypeEnum['TYPE3'].desc;
    differenceResourceR.kind = RcjTypeEnum['TYPE1'].code;
    differenceResourceC.kind = RcjTypeEnum['TYPE2'].code;
    differenceResourceJ.kind = RcjTypeEnum['TYPE3'].code;
    this.initResourceByUserDe(differenceResourceR, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ_LABEL, 1,resQtyR,deRow);
    this.initResourceByUserDe(differenceResourceC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ_LABEL, 1,resQtyC,deRow);
    this.initResourceByUserDe(differenceResourceJ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ_LABEL, 1,resQtyJ,deRow);
    if(resQtyR != 0){
      let huansuanRec = ObjectUtils.cloneDeep(differenceResourceR);
      let tzItem = originalQtyMap?.get(differenceResourceR.materialCode)
      if(ObjectUtils.isNotEmpty(tzItem)){
        if(tzItem.isConversion && ObjectUtils.isNotEmpty(tzItem.isConversion)){
          differenceResourceR.isConversion = tzItem.isConversion;
        }
        differenceResourceR.originalQty = tzItem.originalQty;
        huansuanRec.originalQty = tzItem.originalQty;
        huansuanRec.resQty = NumberUtil.subtract(differenceResourceR.resQty,tzItem.originalQty);
      }
      //如果旧的调整锁定，恢复其人材机，仅记录换算信息
      if(ObjectUtils.isNotEmpty(tzItem) && tzItem.isNumLock){
        resourceDomain.createResource(unitId, deRow.sequenceNbr, tzItem);
      }else{
        resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceR);
      }
      service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, huansuanRec, 'addMerge', null, null);
    }else{
      //删除
      let tzItem = originalQtyMap?.get(differenceResourceR.materialCode)
      if(ObjectUtils.isNotEmpty(tzItem)){
        service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, tzItem, 'del', null, null);
      }
    }
    if(resQtyC != 0){
      let huansuanRec = ObjectUtils.cloneDeep(differenceResourceC);
      let tzItem = originalQtyMap?.get(differenceResourceC.materialCode)
      if(ObjectUtils.isNotEmpty(tzItem)){
        if(tzItem.isConversion && ObjectUtils.isNotEmpty(tzItem.isConversion)){
          differenceResourceC.isConversion = tzItem.isConversion;
        }
        differenceResourceC.originalQty = tzItem.originalQty;
        huansuanRec.originalQty = tzItem.originalQty;
        huansuanRec.resQty = NumberUtil.subtract(differenceResourceC.resQty,tzItem.originalQty);
      }
      //如果旧的调整锁定，恢复其人材机，仅记录换算信息
      if(ObjectUtils.isNotEmpty(tzItem) && tzItem.isNumLock){
        resourceDomain.createResource(unitId, deRow.sequenceNbr, tzItem);
      }else{
        resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceC);
      }
      service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, huansuanRec, 'addMerge', null, null);
    }else{
      //删除
      let tzItem = originalQtyMap?.get(differenceResourceC.materialCode)
      if(ObjectUtils.isNotEmpty(tzItem)){
        service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, tzItem, 'del', null, null);
      }
    }
    if(resQtyJ != 0){
      let huansuanRec = ObjectUtils.cloneDeep(differenceResourceJ);
      let tzItem = originalQtyMap?.get(differenceResourceJ.materialCode)
      if(ObjectUtils.isNotEmpty(tzItem)){
        if(tzItem.isConversion && ObjectUtils.isNotEmpty(tzItem.isConversion)){
          differenceResourceJ.isConversion = tzItem.isConversion;
        }
        differenceResourceJ.originalQty = tzItem.originalQty;
        huansuanRec.originalQty = tzItem.originalQty;
        huansuanRec.resQty = NumberUtil.subtract(differenceResourceJ.resQty,tzItem.originalQty);
      }
      //如果旧的调整锁定，恢复其人材机，仅记录换算信息
      if(ObjectUtils.isNotEmpty(tzItem) && tzItem.isNumLock){
        resourceDomain.createResource(unitId, deRow.sequenceNbr, tzItem);
      }else{
        resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceJ);
      }
      service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, huansuanRec, 'addMerge', null, null);
    }else{
      //删除
      let tzItem = originalQtyMap?.get(differenceResourceJ.materialCode)
      if(ObjectUtils.isNotEmpty(tzItem)){
        service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, tzItem, 'del', null, null);
      }
    }
    // if(resQtyR === 0 && resQtyC === 0 && resQtyJ === 0){

    //   service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, null, 'addMerge', null, null);
    // }

    deRow.price = price;
    deRow.resourceTZ = CommonConstants.COMMON_YES;//有过单价 调整的标识
  }

  async getQuantityExpressionCodes(constructId,unitId,functionDataMap){
    let {service} = EE.app;
    let unitProject =  this.ctx.treeProject.getNodeById(unitId);
    //1.0 变量表校验quantity
    let codeArgs =  {
        constructId:constructId,
        type:"变量表",
        unitId:unitId,
        qfMajorType:unitProject.qfMajorType
    }
    let priceCodes = await service.gongLiaoJiProject.gljUnitCostCodePriceService.costCodePrice(codeArgs);
    //记取逻辑
    let costMath = functionDataMap.get(FunctionTypeConstants.UNIT_COST_MATCH_VALUE);
    if(ObjectUtils.isNotEmpty(costMath)){
      let costMathUnit = costMath.get(unitId);
      if(ObjectUtils.isEmpty(priceCodes)){
        priceCodes = [];
      }
      if(ObjectUtils.isNotEmpty(costMathUnit)){

        let keys = Object.keys(costMathUnit);
        for(let key of keys){
          priceCodes.push({code:key,price:costMathUnit[key]});
        }
      }
    }
    return priceCodes;
  }

  _containsAnyItem(quantityExpression,keys){
    let containKey = false;
    for(let key of keys){
      if(quantityExpression.includes(key)){
        containKey = true;
      }
    }
    return containKey;
    // return keys.some(item=>{quantityExpression.includes(item)});
  }

  /**
   * 费用定额统一修改工程量接口
   * @param {*} constructId
   * @param {*} unitId
   * @param {*} functionDataMap
   */
  async notifyQuantityCZYS(constructId,unitId,functionDataMap){

    let costMath = functionDataMap.get(FunctionTypeConstants.UNIT_COST_MATCH_VALUE);
    if(ObjectUtils.isEmpty(costMath)){
      return ;
    }
    let costMathUnit = costMath.get(unitId);
    if(ObjectUtils.isEmpty(costMathUnit)){
      return;
    }
    let keys = Object.keys(costMathUnit);
    let nodes = this.allDeMap.getAllNodes().filter(item=>{
      if(item.type=== DeTypeConstants.DE_TYPE_DE){
        let quantity = item.quantityExpression;
        if(ObjectUtils.isEmpty(quantity)){
          quantity = item.originalQuantity;
        }
        if(ObjectUtils.isEmpty(quantity)){
          quantity = '';
        }
        if (typeof quantity == 'number') {
          quantity = quantity + '';
        }
        return this._containsAnyItem(quantity,keys);
      }
      return false;
    });
    if(ObjectUtils.isNotEmpty(nodes)){
      let priceCodes = await this.getQuantityExpressionCodes(constructId, unitId, functionDataMap);

      for(let de of nodes){
        de.originalQuantity = de.quantityExpression;//头疼了，，重新赋值
        this.notifyQuantity({deRowId:de.sequenceNbr},true,false,priceCodes);
      }
    }
  }

  async notifyQuantity(deRowBak,reCalculateQuantity = true,filterTempRemoveRow=true,priceCodes=[]) {

    let { service } = EE.app;
    let deRow = this.getDeById(deRowBak.deRowId);
    if(ObjectUtils.isEmpty(deRow)){
      return;
    }
    let parentDeRow = this.getDeById(deRow.parentId);
    let parentDeFlag = false;
    // 工料机没有定额父级
    // if(parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST){
    //   parentDeFlag = true;
    // }

    let curResultQuantity = 0;
    let  unitNbr = UnitUtils.removeCharter(deRow.unit);
    if(ObjectUtils.isEmpty(priceCodes)){
        priceCodes = [];
    }
    //增加定额费用代码
    await service.gongLiaoJiProject.gljDeService.addPriceCodes(deRow.constructId, deRow.unitId, deRow.sequenceNbr, priceCodes);

    //这里无需trycatch，内部玩耍无错误，有问题请核查来源
    let resultQuantity = DeQualityUtils.evalQualityWithCodes(deRow.quantityExpression,priceCodes);
    if(ObjectUtils.isNotEmpty(unitNbr))
    {
      curResultQuantity = NumberUtil.numberScale(NumberUtil.divide(resultQuantity,unitNbr),5);
    }else{
      curResultQuantity= resultQuantity;
    }
    //父级与自己计算是否相同
    if(parentDeFlag && parentDeRow.quantity == 0){
      deRow.quantity = 0;
    }
    if(parentDeFlag && parentDeRow.quantity > 0){
      let calResultQuantity = NumberUtil.numberScale(NumberUtil.divide(parentDeRow.quantity,deRow.resQty),5);
      if(calResultQuantity != curResultQuantity){
        deRow.resQty  = NumberUtil.numberScale(NumberUtil.divide(curResultQuantity,parentDeRow.quantity),5);
      }
      deRow.quantity  = curResultQuantity;
      deRow.originalQuantity = deRow.quantityExpression;
    }else{
      deRow.quantity  = curResultQuantity;
      deRow.originalQuantity = deRow.quantityExpression;
    }
    //检查工程量人材机数量的影响
    let rcjDeKey = WildcardMap.generateKey(deRow.unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjlist =  this.ctx.resourceMap.getValues(rcjDeKey);
    if(ObjectUtils.isNotEmpty(rcjlist)){
      //如果人材机存在数量锁定，先更新消耗量，
      let rcjDetails = rcjlist.filter(item=>item.isNumLock);
      for(let rcjDetail of rcjDetails){
        await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId, singleId:null, unitId
          , deId:deRow.sequenceNbr
          , rcjDetailId:rcjDetail.sequenceNbr
          , constructRcj:{
            resQty : NumberUtil.divide(rcjDetail.totalNumber,deRow.quantity)
          }});
      }

    }
    await this.notify(deRow,reCalculateQuantity,filterTempRemoveRow,priceCodes);
  }
    /**
     * 过滤属性
     * @param deItem
     * @returns {{constructId, libraryName, classlevel04, classlevel05, isExistDe, projectType, standardId, type, total, price, unitId, deCode, classlevel01, classlevel02, classlevel03, quantity, index, libraryCode, parentId, sortNo, displayType, unit, deName, resQty, sequenceNbr}}
     */
    static filter4DeTree(deItem) {
      const {
        constructId,
        unitId,
        sequenceNbr,
        deRowId,
        type,
        parentId,
        displayType,
        index,
        libraryCode,
        libraryName,
        deCode,
        deName,
        classlevel01,
        classlevel02,
        unit,
        resQty,
        quantity,
        originalQuantity,
        price,
        baseJournalPrice,
        totalNumber,
        baseJournalTotalNumber,
        projectType,
        sortNo,
        isExistDe,
        standardId,
        isDeResource,
        rcjDetailEdit,
        quantityExpression,
        costMajorName,
        costFileCode,
        deResourceKind,
        isTempRemove,
        changeQuantity,
        levelMark,
        annotations,
        isShowAnnotations,
        calculateMethod,
        displaySign,
        displayStatu,
        importYgsDeDRGCFB,
        importYgsDeYYGCFB,
        remark,
        rTotalSum,
        cTotalSum,
        jTotalSum,
        sTotalSum,
        zTotalSum,
        RSum,
        CSum,
        JSum,
        SSum,
        ZSum,
        rdTotalSum,
        cdTotalSum,
        jdTotalSum,
        sdTotalSum,
        zdTotalSum,
        RDSum,
        CDSum,
        JDSum,
        ZDSum,
        SDSum,
        specification,
        isFirstTempRemove,
        isNumLock,
        color,
        classiflevel1,
        ifLockStandardPrice,
        markSum,
        initDeRcjNameList,
        optionMenu,
        pricingMethod,
        calculateBase,
        baseDescription,
        rate,
        adjustmentCoefficient,
        measureType,
        isDataTaxRate,
        itemCategory,
        isFyrcj,
        isCostDe,
        awfType,
        ifProvisionalEstimate,
        unitChanged
      } = deItem; // 解构赋值，只保留需要的属性
      return {
        constructId,
        unitId,
        sequenceNbr,
        deRowId,
        type,
        parentId,
        displayType,
        index,
        libraryCode,
        libraryName,
        deCode,
        deName,
        classlevel01,
        classlevel02,
        unit,
        resQty,
        quantity,
        originalQuantity,
        price,
        baseJournalPrice,
        totalNumber,
        baseJournalTotalNumber,
        projectType,
        sortNo,
        isExistDe,
        standardId,
        isDeResource,
        rcjDetailEdit,
        quantityExpression,
        costMajorName,
        costFileCode,
        deResourceKind,
        isTempRemove,
        changeQuantity,
        levelMark,
        annotations,
        isShowAnnotations,
        calculateMethod,
        displaySign,
        displayStatu,
        importYgsDeDRGCFB,
        importYgsDeYYGCFB,
        remark,
        rTotalSum,
        cTotalSum,
        jTotalSum,
        sTotalSum,
        zTotalSum,
        RSum,
        CSum,
        JSum,
        SSum,
        ZSum,
        rdTotalSum,
        cdTotalSum,
        jdTotalSum,
        sdTotalSum,
        zdTotalSum,
        RDSum,
        CDSum,
        JDSum,
        ZDSum,
        SDSum,
        specification,
        isFirstTempRemove,
        isNumLock,
        color,
        classiflevel1,
        ifLockStandardPrice,
        markSum,
        initDeRcjNameList,
        optionMenu,
        pricingMethod,
        calculateBase,
        baseDescription,
        rate,
        adjustmentCoefficient,
        measureType,
        isDataTaxRate,
        itemCategory,
        isFyrcj,
        isCostDe,
        awfType,
        ifProvisionalEstimate,
        unitChanged
      }; // 返回一个只包含所需属性的新对象
    }

    static getClassName() {
      return DomainConstants.CODE_DE_BASE_DOMAIN;
    }
}

DeBaseDomain.toString = () => 'DeBaseDomain';
module.exports = DeBaseDomain;