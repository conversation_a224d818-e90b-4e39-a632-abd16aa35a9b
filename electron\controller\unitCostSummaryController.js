const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
class UnitCostSummaryController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }
    // /**
    //  * 获取默认费用汇总
    //  * @returns {Promise<ResponseData>}
    //  */
    // getDefaultUnitCostSummary(projectTaxCalculation){
    //     const res = this.service.unitCostSummaryService.getDefaultUnitCostSummary(projectTaxCalculation);
    //     return ResponseData.success(res);
    // }

    /**
     * 插入费用汇总
     */
    async addCostSummary(args){
        const res = await this.service.unitCostSummaryService.addCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 删除费用汇总
     */
    async deleteCostSummary(args){
        const res = await this.service.unitCostSummaryService.deleteCostSummary(args);
        return ResponseData.success(res);
    }



    /**
     * 获取费用汇总
     * @param args
     * @returns {ResponseData}
     */
    getUnitCostSummary(args){
        const res = this.service.unitCostSummaryService.getUnitCostSummary(args);
        return ResponseData.success(res);
    }


    /**
     * 保存或者修改费用汇总
     * @param args
     */
     saveCostSummary(args,redo="费用汇总 {columnTitle} 由【{oldValue}】修改为【{newValue}】",checkRedo="费用汇总 {checkType} {columnTitle}"){
        return   this.service.unitCostSummaryService.saveCostSummary(args);

    }
    // /**
    //  * 获取计算后费用汇总
    //  * @param args
    //  * @returns {ResponseData}
    //  */
    // countUnitCostSummary(args){
    //     const res = this.service.unitCostSummaryService.countUnitCostSummary(args);
    //     return ResponseData.success(res);
    // }

    /**
     * 更新费用汇总
     * @param args
     * @returns {ResponseData}
     */
    updateUnitCostSummary(args){
        const res = this.service.unitCostSummaryService.updateUnitCostSummary(args);
        return ResponseData.success(res);
    }


    /**
     * 根据计税方式获取费用汇总模板列表
     * @param args
     * @returns {ResponseData}
     */
    async getCostSummaryTemplate(args){
        const res =await this.service.unitCostSummaryService.getCostSummaryTemplate(args);
        return ResponseData.success(res);
    }

    /**
     * 选择费用汇总模板
     * @param args
     */
    async selectCostSummaryTemplate(args){
        const res = await this.service.unitCostSummaryService.selectCostSummaryTemplate(args);
        return ResponseData.success(res);
    }

    /**
     * 根据模板名称获取数据
     */
    getTemplateData(args){
        const res = this.service.unitCostSummaryService.getTemplateData(args);
        return ResponseData.success(res);
    }

    /**
     * 上移下移
     */
    moveUpDown(args){
        const res = this.service.unitCostSummaryService.moveUpDown(args);
        return ResponseData.success(res);
    }

    /**
     * 保存模板
     * @param args
     */
    saveTemplate(args){
        const res = this.service.unitCostSummaryService.saveTemplate(args);
        return ResponseData.success(res);
    }


    /**
     * 批量替换
     */
    batchReplacement(args){
        const res = this.service.unitCostSummaryService.batchReplacement(args);
        return ResponseData.success(res);

    }
}

UnitCostSummaryController.toString = () => '[class UnitCostSummaryController]';
module.exports = UnitCostSummaryController;