const {Service} = require("../../core");
const {ProjectOverview} = require("../model/ProjectOverview")
const { getConnection ,getRepository,getManager  } =require('typeorm');
const {Snowflake} = require("../utils/Snowflake");
const projectLevelConstant = require('../enum/ProjectLevelConstant');
const {PricingFileFindUtils} = require('../utils/PricingFileFindUtils')
const {PricingFileWriteUtils} = require('../utils/PricingFileWriteUtils')
const {TreeList} = require("../model/TreeList");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const gcjbxx = require("../jsonData/gcjbxx.json");
const {NumberUtil} = require("../utils/NumberUtil");
const Fixs = require("../fixs");
class ProjectOverviewService extends Service{

    constructor(ctx) {
        super(ctx);
        this._jsonToXmlService = this.service.jsonToXmlService;
    }
    getFydm(){
        console.log('费用代码',fydm)
        return fydm;
    }

    /**
     * 获取项目概况的下级目录
     * @param arg
     * @return {any[]}
     */
    getProjectOverviewCollectionData(arg){
        let array = new Array();
        let obj1 ={
            "11":"基本工程信息"
        };
        let obj2 ={
            "12":"编制说明"
        };
        array.push(obj1);
        array.push(obj2);
        if(arg.levelType=="3"){
            let obj3 ={
                "13":"工程特征"
            };
            array.push(obj3);
        }
        let treeList = new TreeList();
        treeList.itemList = array;
        return treeList;
    }




     getProjectOverviewList(arg){
        let project = null;
        if(arg.levelType === projectLevelConstant.construct){
            //工程项目  只有基本信息
            project = PricingFileFindUtils.getConstructProjectJBXX(arg.constructId);
        }else {
            project = PricingFileFindUtils.getUnitProjectJBXXOrXMTZ(arg.constructId,arg.singleId,arg.unitId,arg.type)
        }

        if (arg.type === 0 && arg.levelType === projectLevelConstant.construct){
            const groups = project.reduce((acc, item) => {
                const key = item.groupCode;
                if (!acc[key]) {
                    acc[key] = [];
                }
                acc[key].push(item);
                return acc;
            }, {});

            let array = new Array();
            //此处是为了不让修改内存数据
            let deepCopy = ConvertUtil.deepCopy(groups);
            for (const group in deepCopy) {
                let group1 = deepCopy[group];
                let group1Element1 = group1[0];
                group1.shift();
                group1Element1.childrenList = group1;
                array.push(group1Element1);
            }
            return array;
        }
        return project;

    }

    /**
     * 前端传递的数据拉平
     * @param arr
     * @param result
     * @return {[]}
     */
    dataHandler(arr) {
        let projectOverviewList = arr;
        let result = new Array();
        for (let i = 0; i < projectOverviewList.length; i++) {
            const item = projectOverviewList[i];
            let childrenList = item.childrenList;
            result.push(item);
            item.childrenList = null;
            if (!ObjectUtils.isEmpty(childrenList)){
                result = result.concat(childrenList);
            }
        }
        return result;
    }

    _reorder(array){
        if(ObjectUtils.isEmpty(array)){
            return;
        }

        array.forEach((item, index) => item.dispNo = index + 1)
    }

    async saveList(arg) {
        if (arg.type === 0){
            if (arg.levelType === projectLevelConstant.construct){
                //项目
                //基本信息
                let jbxx = this.dataHandler(arg.projectOverviewList);
                let projectObj = PricingFileFindUtils.getProjectObjById(arg.constructId);
                this._reorder(jbxx)
                projectObj.constructProjectJBXX = jbxx;
                let cNameObj = projectObj.constructProjectJBXX.find(k =>k.name === "工程名称");
                projectObj.constructName = cNameObj.remark;
            }else {
                //单位
                let unit = PricingFileFindUtils.getUnit(arg.constructId,arg.singleId,arg.unitId);
                this._reorder(arg.projectOverviewList)
                unit.unitJBXX = arg.projectOverviewList
                let projectOverview = arg.projectOverviewList.find((item ) => item.addFlag === 0 &&item.name ==='单位工程名称');
                unit.upName = projectOverview.remark;

            }
        }else {
            //只有单位有工程特征工程特征
            let array = arg.projectOverviewList;
            let unit = PricingFileFindUtils.getUnit(arg.constructId,arg.singleId,arg.unitId);
            this._reorder(array);
            let lastJZMJ = unit.unitGCTZ.find(f=>f.name === "建筑面积(m、㎡)");
            let nowJZMJ = array.find(f=>f.name === "建筑面积(m、㎡)");
            if(lastJZMJ.context != nowJZMJ.context) {
                unit.average = nowJZMJ.context;
            }
            unit.unitGCTZ = array;

            await this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: arg.constructId,
                singleId: arg.singleId,
                unitId: arg.unitId,
            });
        }

    }

     lockBasicEngineeringInfo(arg){
        //工程项目
        let constructProjectJBXX = PricingFileFindUtils.getConstructProjectJBXX(arg.constructId)
        if(constructProjectJBXX===null){
            return;
        }
        for (let i = 0; i < constructProjectJBXX.length; i++) {
            if(constructProjectJBXX[i].groupCode === projectLevelConstant.group_code_2){
                constructProjectJBXX[i].lockFlag =arg.lockFlag
            }

        }
    }

    /**
     * 删除
     * @param arg
     * @return {Promise<void>}
     */
    async delete(arg) {
        if(arg.levelType === projectLevelConstant.construct){
            //工程项目
            let constructProjectJBXX = PricingFileFindUtils.getConstructProjectJBXX(arg.constructId)
            //根据ID过滤
            let filter = constructProjectJBXX.filter(item => item.sequenceNbr !==arg.sequenceNbr );
            constructProjectJBXX = filter;
            let projectObj = PricingFileFindUtils.getProjectObjById(arg.constructId);
            this._reorder(constructProjectJBXX)
            projectObj.constructProjectJBXX = constructProjectJBXX;
        }else{
            let unitProjectJBXXOrXMTZ = PricingFileFindUtils.getUnitProjectJBXXOrXMTZ(arg.constructId,arg.singleId,arg.unitId,arg.type);  //根据ID过滤
            let filter = unitProjectJBXXOrXMTZ.filter(item => item.sequenceNbr !==arg.sequenceNbr );
            unitProjectJBXXOrXMTZ = filter;
            let unit = PricingFileFindUtils.getUnit(arg.constructId,arg.singleId,arg.unitId);
            this._reorder(unitProjectJBXXOrXMTZ)
            if(arg.type === 1){
                unit.unitGCTZ = unitProjectJBXXOrXMTZ;
            }else{
                unit.unitJBXX = unitProjectJBXXOrXMTZ;
            }

        }
    }

    async initializationProject(args){

        let userHistoryFileListData = PricingFileFindUtils.userHistoryFileListData();
        let result = userHistoryFileListData.filter(k =>k.sequenceNbr === args.sequenceNbr);
        let obj = await PricingFileFindUtils.getProjectObjByPath(result.path);
        await  new Fixs(obj,obj.version).fix();
        PricingFileWriteUtils.writeToMemory(obj);
    }

    async updateYsfFile(args){
        let projectObj = PricingFileFindUtils.getProjectObjById(args.constructId);
    }


    async getNotFilledList(args){
        let memoryProject = null;


        //工程项目  只有基本信息
        memoryProject = PricingFileFindUtils.getConstructProjectJBXX(args.constructId);
        if (PricingFileFindUtils.getProjectObjById(args.constructId).biddingType ==2){
            memoryProject = PricingFileFindUtils.getProjectObjById(args.constructId).unitProject.unitJBXX;
        }

        //copy防止内存中数据更改
        let project = ConvertUtil.deepCopy(memoryProject);
        let map = new Map;
        //标段信息, 投标信息(投标才有), 项目附加信息, 设置工程编号
        let bdxx = new Array();
        let tbxx = new Array();
        let xmfjxx = new Array();
        let gcbh = new Array();
        //处理招标0 和控制价2
        if (args.type === 0 || args.type === 2){
            for (let i = 0; i < project.length; i++) {
                if ('建筑面积' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 1;
                    bdxx.push(project[i]);
                }
                if ('招标人(发包人)' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 2;
                    bdxx.push(project[i]);
                }
                if ('招标人(发包人)法人或其授权人' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 3;
                    bdxx.push(project[i]);
                }
                if ('工程造价咨询人' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 4;
                    bdxx.push(project[i]);
                }
                if ('工程造价咨询人法人或其授权人' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 5;
                    bdxx.push(project[i]);
                }
                //if ('编制人' === project[i].name && 16 === project[i].dispNo) {
                if ('编制人' === project[i].name && 2 === project[i].groupCode) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 6;
                    bdxx.push(project[i]);
                }
                //if ('编制时间' === project[i].name && 17=== project[i].dispNo) {
                if ('编制时间' === project[i].name && 2 === project[i].groupCode) {
                    let zbbzsj = ConvertUtil.deepCopy(project[i]);
                    zbbzsj.requiredFlag = 1;
                    zbbzsj.dispNo = 7;
                    bdxx.push(zbbzsj);

                    //附加信息2
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 2;
                    xmfjxx.push(project[i]);
                }
                if ('核对人(复核人)' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 8;
                    bdxx.push(project[i]);
                }
                if ('核对(复核)时间' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 9;
                    bdxx.push(project[i]);
                }
                if ('工期(日历天)' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 10;
                    bdxx.push(project[i]);
                }
                if ('质量承诺' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 11;
                    bdxx.push(project[i]);
                }

                //项目附加信息
                if ('编制单位法定代表人' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 1;
                    xmfjxx.push(project[i]);
                }
            }
        }

        //处理投标
        if (args.type === 1){
            for (let i = 0; i < project.length; i++) {
                //标段信息
                if ('建筑面积' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 1;
                    bdxx.push(project[i]);
                }
                if ('招标人(发包人)' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 2;
                    bdxx.push(project[i]);
                }
                if ('工期(日历天)' === project[i].name) {
                    let tbgq = ConvertUtil.deepCopy(project[i]);
                    tbgq.requiredFlag = 1;
                    tbgq.dispNo = 3;
                    bdxx.push(tbgq);

                    //投标
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 5;
                    tbxx.push(project[i]);
                }
                if ('质量承诺' === project[i].name) {
                    let tbzlcn = ConvertUtil.deepCopy(project[i]);
                    tbzlcn.requiredFlag = 1;
                    tbzlcn.dispNo = 4;
                    bdxx.push(tbzlcn);

                    project[i].requiredFlag = 1;
                    project[i].dispNo = 7;
                    tbxx.push(project[i]);
                }

                //投标信息
                //第一次进循环是处理前面3个  复制其他对象
                if(i===0){
                    //通过造价分析获取   投标总价、招标控制价
                    let zj = await this._jsonToXmlService.countZj({constructId:args.constructId, levelType:1})


                    //投标总价(万元)
                    let tbzj1 = ConvertUtil.deepCopy(project[i]);
                    tbzj1.sequenceNbr = Snowflake.nextId();
                    tbzj1.name = "投标总价(万元)";
                    tbzj1.dispNo = 1;
                    tbzj1.lockFlag = 1;
                    tbzj1.remark = NumberUtil.divide(zj,10000);
                    tbxx.push(tbzj1);
                    //投标总价(元)
                    let tbzj2 = ConvertUtil.deepCopy(project[i]);
                    tbzj2.sequenceNbr = Snowflake.nextId();
                    tbzj2.name = "投标总价(元)";
                    tbzj2.dispNo = 2;
                    tbzj2.lockFlag = 1;
                    tbzj2.remark = zj;
                    tbxx.push(tbzj2);
                    //投标总价(元)大写
                    let tbzj3 = ConvertUtil.deepCopy(project[i]);
                    tbzj3.sequenceNbr = Snowflake.nextId();
                    tbzj3.name = "投标总价(元)大写";
                    tbzj3.dispNo = 3;
                    tbzj3.lockFlag = 1;
                    tbzj3.remark = NumberUtil.numToCny(zj);//工程总造价 不含设备费及其税金  大写
                    tbxx.push(tbzj3);
                }

                if ('项目经理' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 4;
                    tbxx.push(project[i]);
                }
                if ('投标保证金(万元)' === project[i].name) {
                    project[i].dispNo = 6;
                    tbxx.push(project[i]);
                }
                if ('担保类型' === project[i].name) {
                    project[i].dispNo = 8;
                    tbxx.push(project[i]);
                }
                if ('投标人(承包人)' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 9;
                    tbxx.push(project[i]);
                }
                if ('投标人(承包人)法人或其授权人' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 10;
                    tbxx.push(project[i]);
                }
                if ('投标单位造价工程师' === project[i].name) {
                    project[i].dispNo = 11;
                    tbxx.push(project[i]);
                }
                if ('投标单位造价工程师资质证号' === project[i].name) {
                    project[i].dispNo = 12;
                    tbxx.push(project[i]);
                }
                //if ('编制人' === project[i].name  && 28 === project[i].dispNo) {
                if ('编制人' === project[i].name  && 3 === project[i].groupCode) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 13;
                    tbxx.push(project[i]);
                }
                //if ('编制时间' === project[i].name  && 29 === project[i].dispNo) {
                if ('编制时间' === project[i].name  && 3 === project[i].groupCode) {
                    let tbbzsj = ConvertUtil.deepCopy(project[i]);
                    tbbzsj.requiredFlag = 1;
                    tbbzsj.dispNo = 14;
                    tbxx.push(tbbzsj);

                    project[i].requiredFlag = 1;
                    project[i].dispNo = 2;
                    xmfjxx.push(project[i]);
                }
                //项目附加信息-投标
                if ('编制单位法定代表人' === project[i].name) {
                    project[i].requiredFlag = 1;
                    project[i].dispNo = 1;
                    xmfjxx.push(project[i]);
                }


            }

            //如果是投标增加一个集合
            map.set("tbxx", tbxx.sort((a, b) => a.dispNo - b.dispNo));
        }
        //工程编号-招投标都有
        gcbh = await this.getGcbhList(project[0], args);


        map.set("bdxx", bdxx.sort((a, b) => a.dispNo - b.dispNo));
        map.set("xmfjxx", xmfjxx.sort((a, b) => a.dispNo - b.dispNo));
        map.set("gcbh", gcbh);
        return map;
    }


    /**
     * 递归获取工程编号
     * @param args
     * @returns {Promise<void>}
     */
    async getGcbhList(project, args){
        let gcbh = new Array();
        let projectObj = PricingFileFindUtils.getProjectObjById(args.constructId);
        project.sequenceNbr = Snowflake.nextId();
        project.name = projectObj.constructName;
        project.remark = projectObj.constructCode;
        project.dispNo = 1;
        project.lockFlag = 1;
        gcbh.push(project);

        let single = projectObj.singleProjects;
        if(single !== null){
            for (let i = 0; i < single.length; i++) {
                let model2 = single[i];
                let project2 = ConvertUtil.deepCopy(project);
                project2.sequenceNbr = Snowflake.nextId();
                project2.parentId = project.sequenceNbr;
                project2.name = model2.projectName;
                project2.remark = model2.projectCode;
                project2.dispNo = 2;
                gcbh.push(project2);
                let unit = model2.unitProjects;
                if(unit !== null) {
                    for (let i = 0; i < unit.length; i++) {
                        let model3 = unit[i];
                        let project3 = ConvertUtil.deepCopy(project);
                        project3.sequenceNbr = Snowflake.nextId();
                        project3.parentId = project2.sequenceNbr;
                        project3.name = model3.upName;
                        project3.remark = model3.upCode;
                        project3.dispNo = 3;
                        gcbh.push(project3);
                    }
                }
            }
        }

        return gcbh;
    }

    /**
     * 保存
     * @param args
     * @returns {Promise<void>}
     */
    async saveNotFilledList(args){
        args.code = 1;
        let map = await this.getNewSaveList(args);
        let result = await this.getProjectOverviewList(args);
        //处理要保存的数据
        //处理招标0
        if (args.type === 0){
            for (let i = 0; i < result.length; i++) {
                let model = result[i].childrenList;
                for (let i = 0; i < model.length; i++) {
                    if ('建筑面积' === model[i].name) {
                        model[i].remark = map.get("zbjzmj");
                    }
                    if ('招标人(发包人)' === model[i].name) {
                        model[i].remark = map.get("zbzbr");
                    }
                    if ('招标人(发包人)法人或其授权人' === model[i].name) {
                        model[i].remark = map.get("zbfr");
                    }
                    if ('工程造价咨询人' === model[i].name) {
                        model[i].remark = map.get("zbgczx");
                    }
                    if ('工程造价咨询人法人或其授权人' === model[i].name) {
                        model[i].remark = map.get("zbgcfr");
                    }
                    if ('编制人' === model[i].name && 16 === model[i].dispNo) {
                        model[i].remark = map.get("zbbzr");
                    }
                    if ('编制时间' === model[i].name && 17=== model[i].dispNo) {
                        model[i].remark = map.get("zbbzsj");
                    }
                    if ('核对人(复核人)' === model[i].name) {
                        model[i].remark = map.get("zbhdr");
                    }
                    if ('核对(复核)时间' === model[i].name) {
                        model[i].remark = map.get("zbfhsj");
                    }
                    if ('工期(日历天)' === model[i].name) {
                        model[i].remark = map.get("zbgq");
                    }
                    if ('质量承诺' === model[i].name) {
                        model[i].remark = map.get("zbzlcn");
                    }
                    //项目附加信息
                    if ('编制单位法定代表人' === model[i].name) {
                        model[i].remark = map.get("zbbzr2");
                    }
                }
            }

        }

        // 控制价2
        if (args.type === 2){
            for (let i = 0; i < result.length; i++) {
                    if ('建筑面积' === result[i].name) {
                        result[i].remark = map.get("zbjzmj");
                    }
                    if ('招标人(发包人)' === result[i].name) {
                        result[i].remark = map.get("zbzbr");
                    }
                    if ('招标人(发包人)法人或其授权人' === result[i].name) {
                        result[i].remark = map.get("zbfr");
                    }
                    if ('工程造价咨询人' === result[i].name) {
                        result[i].remark = map.get("zbgczx");
                    }
                    if ('工程造价咨询人法人或其授权人' === result[i].name) {
                        result[i].remark = map.get("zbgcfr");
                    }
                    if ('编制人' === result[i].name && 16 === result[i].dispNo) {
                        result[i].remark = map.get("zbbzr");
                    }
                    if ('编制时间' === result[i].name && 17=== result[i].dispNo) {
                        result[i].remark = map.get("zbbzsj");
                    }
                    if ('核对人(复核人)' === result[i].name) {
                        result[i].remark = map.get("zbhdr");
                    }
                    if ('核对(复核)时间' === result[i].name) {
                        result[i].remark = map.get("zbfhsj");
                    }
                    if ('工期(日历天)' === result[i].name) {
                        result[i].remark = map.get("zbgq");
                    }
                    if ('质量承诺' === result[i].name) {
                        result[i].remark = map.get("zbzlcn");
                    }
                    //项目附加信息
                    if ('编制单位法定代表人' === result[i].name) {
                        result[i].remark = map.get("zbbzr2");
                    }
            }

        }

        if (args.type === 1){
            for (let i = 0; i < result.length; i++) {

                //标段信息
                if ('建筑面积' === result[i].name) {
                    result[i].remark = map.get("tbjzmj");
                }
                if ('招标人(发包人)' === result[i].name) {
                    result[i].remark = map.get("tbzbr");
                }
                if ('工期(日历天)' === result[i].name) {
                    result[i].remark = map.get("tbgq");
                }
                if ('质量承诺' === result[i].name) {
                    result[i].remark = map.get("tbzlcn");
                }

                if ('项目经理' === result[i].name) {
                    result[i].remark = map.get("tbxmjl2");
                }
                if ('投标保证金(万元)' === result[i].name) {
                    result[i].remark = map.get("tbbzj2");
                }
                if ('担保类型' === result[i].name) {
                    result[i].remark = map.get("tbdblx2");
                }
                if ('投标人(承包人)' === result[i].name) {
                    result[i].remark = map.get("tbr2");
                }
                if ('投标人(承包人)法人或其授权人' === result[i].name) {
                    result[i].remark = map.get("tbfr2");
                }
                if ('投标单位造价工程师' === result[i].name) {
                    result[i].remark = map.get("tbgcs2");
                }
                if ('投标单位造价工程师资质证号' === result[i].name) {
                    result[i].remark = map.get("tbgcszzh2");
                }
                if ('编制人' === result[i].name && 28 === result[i].dispNo) {
                    result[i].remark = map.get("tbbzr2");
                }
                if ('编制时间' === result[i].name && 29 === result[i].dispNo) {
                    result[i].remark = map.get("tbbzsj2");
                }
                //项目附加信息-投标
                if ('编制单位法定代表人' === result[i].name) {
                    result[i].remark = map.get("tbbzr3");
                }
            }
        }

        args.projectOverviewList = result;
        this.saveList(args);
    }

    /**
     * 保存时获取新数据集合
     * @param args
     * @returns {Promise<void>}
     */
    async getNewSaveList(args){
        let map = new Map;
        let bdxx = args.projectOverviewList.get("bdxx");
        let tbxx = args.projectOverviewList.get("tbxx");
        let xmfjxx = args.projectOverviewList.get("xmfjxx");
        //处理招标0 和控制价2
        if (args.type === 0 || args.type === 2){
            for (let i = 0; i < bdxx.length; i++) {
                if('建筑面积' === bdxx[i].name){
                    map.set("zbjzmj", bdxx[i].remark);
                }
                if('招标人(发包人)' === bdxx[i].name){
                    map.set("zbzbr", bdxx[i].remark);
                }
                if('招标人(发包人)法人或其授权人' === bdxx[i].name){
                    map.set("zbfr", bdxx[i].remark);
                }
                if('工程造价咨询人' === bdxx[i].name){
                    map.set("zbgczx", bdxx[i].remark);
                }
                if('工程造价咨询人法人或其授权人' === bdxx[i].name){
                    map.set("zbgcfr", bdxx[i].remark);
                }
                if('编制人' === bdxx[i].name){
                    map.set("zbbzr", bdxx[i].remark);
                }
                if('编制时间' === bdxx[i].name){
                    map.set("zbbzsj", bdxx[i].remark);
                }
                if('核对人(复核人)' === bdxx[i].name){
                    map.set("zbhdr", bdxx[i].remark);
                }
                if('核对(复核)时间' === bdxx[i].name){
                    map.set("zbfhsj", bdxx[i].remark);
                }
                if('工期(日历天)' === bdxx[i].name){
                    map.set("zbgq", bdxx[i].remark);
                }
                if('质量承诺' === bdxx[i].name){
                    map.set("zbzlcn", bdxx[i].remark);
                }
            }

            for (let i = 0; i < xmfjxx.length; i++) {
                if('编制单位法定代表人' === xmfjxx[i].name){
                    map.set("zbbzr2", xmfjxx[i].remark);
                }
                if('编制时间' === xmfjxx[i].name){
                    map.set("zbbzsj2", xmfjxx[i].remark);
                }
            }
        }

        if (args.type === 1){
            for (let i = 0; i < bdxx.length; i++) {
                if('建筑面积' === bdxx[i].name){
                    map.set("tbjzmj", bdxx[i].remark);
                }
                if('招标人(发包人)' === bdxx[i].name){
                    map.set("tbzbr", bdxx[i].remark);
                }
                if('工期(日历天)' === bdxx[i].name){
                    map.set("tbgq", bdxx[i].remark);
                }
                if('质量承诺' === bdxx[i].name){
                    map.set("tbzlcn", bdxx[i].remark);
                }
            }

            for (let i = 0; i < tbxx.length; i++) {
                if('项目经理' === tbxx[i].name){
                    map.set("tbxmjl2", tbxx[i].remark);
                }
                if('工期(日历天)' === tbxx[i].name){
                    map.set("tbgq2", tbxx[i].remark);
                }
                if('投标保证金(万元)' === tbxx[i].name){
                    map.set("tbbzj2", tbxx[i].remark);
                }
                if('质量承诺' === tbxx[i].name){
                    map.set("tbzlcn2", tbxx[i].remark);
                }
                if('担保类型' === tbxx[i].name){
                    map.set("tbdblx2", tbxx[i].remark);
                }
                if('投标人(承包人)' === tbxx[i].name){
                    map.set("tbr2", tbxx[i].remark);
                }
                if('投标人(承包人)法人或其授权人' === tbxx[i].name){
                    map.set("tbfr2", tbxx[i].remark);
                }
                if('投标单位造价工程师' === tbxx[i].name){
                    map.set("tbgcs2", tbxx[i].remark);
                }
                if('投标单位造价工程师资质证号' === tbxx[i].name){
                    map.set("tbgcszzh2", tbxx[i].remark);
                }
                if('编制人' === tbxx[i].name){
                    map.set("tbbzr2", tbxx[i].remark);
                }
                if('编制时间' === tbxx[i].name){
                    map.set("tbbzsj2", tbxx[i].remark);
                }

            }

            for (let i = 0; i < xmfjxx.length; i++) {
                if ('编制单位法定代表人' === xmfjxx[i].name) {
                    map.set("tbbzr3", xmfjxx[i].remark);
                }
                if ('编制时间' === xmfjxx[i].name) {
                    map.set("tbbzsj3", xmfjxx[i].remark);
                }
            }
        }

        return map;
    }


}
ProjectOverviewService.toString = () => '[class ProjectOverviewService]';
module.exports = ProjectOverviewService;
