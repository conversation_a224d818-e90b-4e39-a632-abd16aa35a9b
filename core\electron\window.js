/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-05 11:07:41
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-19 10:59:22
 */
const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const Conf = require('../config');
const Ps = require('../ps');
const EE = require("../../core/ee")

const Window = {
	/**
	 * 创建应用主窗口
	 */
	createWindow() {
		// todo
		// const protocolName = 'eefile';
		// protocol.registerFileProtocol(protocolName, (request, callback) => {
		//   const url = request.url.substring(protocolName.length + 3);
		//   console.log('[core] [lib/eeApp] registerFileProtocol ----url: ', url);
		//   callback({ path: path.normalize(decodeURIComponent(url)) })
		// });

		const config = Conf.all();
		const oo = {
			width: 1366,
			height: 768,
			useContentSize: true,
			frame: false, // 去掉默认的标题栏
		};
		let win = new BrowserWindow({ ...config.windowsOption, ...oo });
		// 菜单显示/隐藏
		if (config.openAppMenu === 'dev-show' && Ps.isProd()) {
			Menu.setApplicationMenu(null);
		} else if (config.openAppMenu === false) {
			Menu.setApplicationMenu(null);
		} else {
			// nothing
		}
		// 监听键盘按下事件
		win.webContents.on('before-input-event', (event, input) => {
			if (input.control && input.key === 'r') {
				event.preventDefault();
				return false;
			}
		});
		ipcMain.on('window-min', () => win.minimize());
		ipcMain.on('window-max', () => {
			if (win.isMaximized()) {
				win.unmaximize();
			} else {
				win.maximize();
			}
		});
		ipcMain.on('window-close', (closeEvent, args) => {
			if (args) {
				win.destroy();
			} else {
				win.hide();
			}
		});
		// DevTools
		//  if (!app.isPackaged && config.openDevTools) {
		if(EE.app.showDebugger){
		   win.webContents.openDevTools({
		     mode: 'undocked'
		   });
		 }
		return win;
	},
};

module.exports = Window;
