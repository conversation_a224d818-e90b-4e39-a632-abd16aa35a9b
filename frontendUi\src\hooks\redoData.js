/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-01-15 09:48:54
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-01-16 11:25:04
 */
const data = {
  // 各接口参数唯一标识对象，用于页面选中id映射
  idsMap: {
    sequenceNbr: 'string',
    pointLineId: 'string',
    prevDeRowId: 'string',
    targetSequenceNbr: 'string', //其他项目暂列金额唯一id
    pointLine: 'object',
    upDateLine: 'object',
    unitCostSummary: 'object', //费用汇总唯一标识对象
  },
  specialChannel: [
    {
      channel: ['controller.quantitiesController.updateQuantityData'],
      id: 'quotaListId',
    },
     {
      channel: ['controller.listFeatureController.editQdFeature'],
      id: 'qdId',
    }
  ],
  // channel 所属tabname，用于跳转子页面或分屏页面
  childrenTabName: {
    rcjmx: ['controller.rcjController.updateConstructRcj'],
    jrg: [],
    zcbfwf: [],
  },
  otherProjectIndex: {
    qtxmZlje: '暂列金额',
    qtxmZygczgj: '专业工程暂估价',
    qtxmZcbfwf: '总承包服务费',
    qtxmJrg: '计日工',
    qtxmQzysp: '签证及索赔',
  },
  paramsDataMap: [
    {
      channel: ['controller.itemBillProjectController.save'],
      kind: [
        {
          key: '01',
          value: '分部',
        },
        {
          key: '02',
          value: '子分部',
        },
        {
          key: '03',
          value: '清单',
        },
        {
          key: '04',
          value: '定额',
        },
      ],
    },
    {
      channel: ['controller.itemBillProjectController.remove'],
      kind: [
        {
          key: '01',
          value: '分部',
        },
        {
          key: '02',
          value: '子分部',
        },
        {
          key: '03',
          value: '清单',
        },
        {
          key: '04',
          value: '定额',
        },
      ],
    },
    {
      channel: ['controller.itemBillProjectController.updateDelTempStatusColl'],
      tempDeleteFlag: [
        {
          key: true,
          value: '临时删除',
        },
        {
          key: false,
          value: '取消临时删除',
        },
      ],
    },
    {
      channel: ['controller.branchProjectController.fbDataUpAndDownController'],
      operateAction: [
        {
          key: 'up',
          value: '升级',
        },
        {
          key: 'down',
          value: '降级',
        },
      ],
    },
    {
      channel: [
        'controller.branchProjectController.qdDeUpAndDown',
        'controller.conversionDeController.updateDeConversionInfo',
      ],
      operateAction: [
        {
          key: 'up',
          value: '上移',
        },
        {
          key: 'down',
          value: '下移',
        },
        {
          key: 'delete',
          value: '删除',
        },
      ],
    },
    {
      channel: ['controller.itemBillProjectController.batchDelByTypeOfColl'],
      operateAction: [
        {
          key: 1,
          value: '批量删除所有临时删除项',
        },
        {
          key: 2,
          value: '批量删除所有工程量为0项',
        },
      ],
    },
    {
      channel: ['controller.conversionDeController.switchConversionMod'],
      standardConvertMod: [
        {
          key: 1,
          value: '以标准人材机数据执行',
        },
        {
          key: 2,
          value: '以当前数据执行计算',
        },
      ],
    },
    {
      channel: ['controller.supplementController.defaultCodeColl'],
      clKind: [
        {
          key: 4,
          value: '设备',
        },
        {
          key: 5,
          value: '主材',
        },
      ],
    },
    {
      channel: ['controller.conversionDeController.switchConversionMainMatMod'],
      mainMatConvertMod: [
        {
          key: true,
          value: '勾选',
        },
        {
          key: false,
          value: '取消勾选',
        },
      ],
    },
    {
      channel: ['controller.quantitiesController.move'],
      direction: [
        {
          key: 0,
          value: '上移',
        },
        {
          key: 1,
          value: '下移',
        },
      ],
    },
  ],
};
export default data;
