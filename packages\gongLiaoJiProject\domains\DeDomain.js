const StandardDeModel = require('./deProcessor/models/StandardDeModel');
const DeTypeConstants = require('../constants/DeTypeConstants');
const LabelConstants = require('../constants/LabelConstants');
const DeBaseDomain = require('./DeBaseDomain');
const BaseDomain = require('./core/BaseDomain');
const DomainConstants = require('../constants/DomainConstants');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const PropertyUtil = require('./utils/PropertyUtil');
const EE = require('../../../core/ee');
const ResourceModel = require('./deProcessor/models/ResourceModel');
const { Snowflake } = require('../utils/Snowflake');
const ResourceConstants = require('../constants/ResourceConstants');
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const UnitUtils = require('../core/tools/UnitUtils');
const ResourceKindConstants = require("../constants/ResourceKindConstants");
const CommonConstants = require("../constants/CommonConstants");
const DeCommonConstants = require("../constants/DeCommonConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const RcjTypeEnum = require('../../../electron/enum/RcjTypeEnum');
const {FBCalculator} = require("./calculators/de/FBCalculator");
const {NumberUtil} = require("../utils/NumberUtil");
const {DeCalculator} = require("./calculators/de/DeCalculator");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const BranchProjectDisplayConstant = require("../constants/BranchProjectDisplayConstant");
const {DeTypeCheckUtil} = require("../domains/utils/DeTypeCheckUtil");
const DeQualityUtils = require('./utils/DeQualityUtils');
const WildcardMap = require('../core/container/WildcardMap');
const DeUtils = require('./utils/DeUtils');
const CostDeMatchConstants = require('../constants/CostDeMatchConstants');
const ProjectDomain = require("./ProjectDomain");

class DeDomain extends DeBaseDomain {
  resourceDomain;
  functionDataMap;
  constructor(ctx,resourceDomain) {
    super(ctx,'deMap');
    this.ctx = ctx;
    this.resourceDomain = resourceDomain;
    this.initData();
  }
  initData() {
    //this.initDefaultDE();
  }

  /**
   * 给定额编辑器初始化默认显示为 "单位工程" 的特殊行.
   *
   */
  initDefaultDE(constructId, unitId) {
    if (ObjectUtil.isEmpty(this.getRoot(unitId))) {
      let defaultDe = new StandardDeModel(constructId, unitId, LabelConstants.LABEL_DEFAULT_DE_ID.concat(Snowflake.nextId()), 0, DeTypeConstants.DE_TYPE_DEFAULT);
      defaultDe.name = DeTypeConstants.DE_TYPE_DEFAULT_LABEL;
      this.ctx.deMap.addNode(defaultDe);
      return defaultDe;
    }
  }

  /**
   * 获取当前单位工程下的默认定额行."单位工程"这一行
   * @returns {T}
   */
  getRoot(unitId) {
    return this.ctx.deMap.getAllNodes().find(item => item.type === DeTypeConstants.DE_TYPE_DEFAULT && item.unitId === unitId);
  }
  /**
   *
   * @param deRowId
   * @returns {null}
   */
  findBrothersDes(deRowId) {
    let brothers = null;
    let currentDeRow = this.ctx.deMap.getNodeById(deRowId);
    if (ObjectUtil.isNotEmpty(currentDeRow) && ObjectUtil.isNotEmpty(currentDeRow.parentId)) {
      let parentNode = this.ctx.deMap.getNodeById(currentDeRow.parentId);
      brothers = parentNode.children;
    }
    return brothers;
  }
  /**
   * 创建定额
   * @param deModel
   * @returns {*}
   */
  async createDeRow(deModel,index, checkType = false) {

    if (ObjectUtil.isEmpty(deModel.parentId)) {
      throw Error('The parent id of De can not be null.');
    }
    switch (deModel.type) {
      case DeTypeConstants.DE_TYPE_EMPTY:
        //空定额行
        deModel.displayType = DeTypeConstants.DE_TYPE_EMPTY_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_DEFAULT:
        //整个工程
        deModel.displayType = DeTypeConstants.DE_TYPE_DEFAULT_LABEL;
        deModel.totalNumber = "0";
        break;
      case DeTypeConstants.DE_TYPE_FB:
        // 分部
        deModel.totalNumber = "0";
        deModel.displayType = DeTypeConstants.DE_TYPE_FB_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_ZFB:
        // 子分部
        deModel.totalNumber = "0";
        deModel.displayType = DeTypeConstants.DE_TYPE_ZFB_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_DELIST:
        // 清单 概算定额
        deModel.displayType = DeTypeConstants.DE_TYPE_DELIST_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_DE:
        // 定额
        deModel.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_ANZHUANG_FEE:
        // 定额
        deModel.displayType = DeTypeConstants.DE_TYPE_ANZHUANG_FEE_LABEL;
        break;
      case DeTypeConstants.DE_TYPE_RESOURCE:
        //人材机 只在概算中有实现
        deModel.displayType = DeTypeConstants.DE_TYPE_RESOURCE_LABEL;
        deModel.isResouceDe = 1;
        break;
    }
    let parentNode = this.ctx.deMap.getNodeById(deModel.parentId);
    if(ObjectUtil.isEmpty(index))
    {
      this.ctx.deMap.addNode(deModel, parentNode);
    }
    else
    {
      this.ctx.deMap.addNodeAt(deModel, parentNode,index);
    }
    //填充箭头
    await this._fillArrow(deModel,parentNode);
    await this.doAfterCreate(deModel);
    deModel.assignDispNos();

    if(checkType){      
      //此时核查父级, 应该是这里啊  
      DeTypeCheckUtil.updateParentDeType(deModel,this.ctx);
    }
    if(deModel.type === DeTypeConstants.DE_TYPE_FB || deModel.type === DeTypeConstants.DE_TYPE_ZFB){
      deModel.costMajorName = null;
      deModel.costFileCode = null;
    }
    return deModel;
  }

  /**
   * 处理箭头
   * @param newNode
   * @param parentNode
   * @private
   */
  async _fillArrow(newNode, parentNode) {
    newNode.displaySign = ObjectUtils.isEmpty(newNode.children) ? BranchProjectDisplayConstant.noSign : BranchProjectDisplayConstant.open;
    //处理父节点箭头
    if (ObjectUtil.isNotEmpty(parentNode)) {
      parentNode.displaySign = BranchProjectDisplayConstant.open;
      await this.updateDe(parentNode);
    }

    if (newNode.type === DeTypeConstants.DE_TYPE_DE && newNode.displaySign === BranchProjectDisplayConstant.noSign && ObjectUtil.isNotEmpty(newNode.rcjList)) {
      //判断是否有主材人材机
      let filter = newNode.rcjList.filter(o => o.kind === RcjTypeEnum["TYPE5"].code);
      if (ObjectUtil.isNotEmpty(filter)) {
        newNode.displaySign = BranchProjectDisplayConstant.open;
      }
    }

  }

  /**
   * 创建定额后操作
   * @param deModel
   * @returns {Promise<void>}
   */
  async doAfterCreate(deModel) {
    let {service} = EE.app;
    //定额创建 初始化
    await service.gongLiaoJiProject.gljInitDeService.init(deModel);

  }


  /**
   * 定额行的 人材机
   * @param constructId
   * @param unitId
   * @param resourceId
   * @param rowId
   */
  async appendDeResource(constructId, unitId, resourceId, rowId) {
    let {service} = EE.app;
    let deRow = this.getDeById(rowId);
    if(ObjectUtil.isNotEmpty(deRow))
    {
      this.removeRowRelatedDatas(deRow);
    }

    deRow.type = DeTypeConstants.DE_TYPE_RESOURCE;
    deRow.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;//人材机默认显示定
    deRow.displaySign = BranchProjectDisplayConstant.noSign;
    let baseRCJ = await service.gongLiaoJiProject.gljBaseRcjService.queryRcjById({standardId:resourceId});
    PropertyUtil.copyProperties(baseRCJ, deRow, [...DeDomain.avoidProperty,"sequenceNbr"]);
    deRow.isDeResource = CommonConstants.COMMON_YES;
    baseRCJ.isDeResource = CommonConstants.COMMON_YES;
    baseRCJ.deRowId = rowId;
    deRow.marketPrice = baseRCJ.price;
    deRow.resQty = 0;
    deRow.totalNumber = 0;
    deRow.quantity = 0;
    deRow.deName = baseRCJ.materialName;
    deRow.deCode = baseRCJ.materialCode;
    deRow.deResourceKind = baseRCJ.kind;
    deRow.standardId = baseRCJ.sequenceNbr;
    deRow.specification = baseRCJ.specification;
    deRow.isFyrcj = baseRCJ.isFyrcj;
    deRow.measureType = null;
    deRow.costFileCode = null;
    deRow.qfCode = null;
    deRow.costMajorName = null;

    let parentDeRow = this.findFirstDeOrDeList(deRow);
    
    //取费专业初始化
    await this._initQfAndMeasureType(unitId,deRow, service);

    await this.attachDeRCJ(this.resourceDomain,[baseRCJ], deRow, [],CommonConstants.COMMON_YES,true);

    if(deRow.isTempRemove === CommonConstants.COMMON_YES
      || (ObjectUtil.isNotEmpty(parentDeRow) && parentDeRow.isTempRemove === CommonConstants.COMMON_YES)
    ){
      
      if(deRow.changeQuantity){
        deRow.originalQuantity = deRow.changeQuantity;//保留旧的原始工程量
      }
      deRow.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
      await this.tempRemoveDeRow(rowId);
    }else{
      await this.notify(deRow,true);
    }
    try {
      //自动计算=各种记取+费用汇总通知
      await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
      // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
      //   constructId: constructId,
      //   unitId: unitId,
      //   qfMajorType: deRow.qfCode
      // });
      //联动计算装饰超高人材机数量单价
      // await service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
    } catch (error) {
      console.error("捕获到异常:", error);
    }
    return deRow;
  }

  async appendUserResource(constructId, unitId, deRowId, userResource,add2UserRcj = true)
  {
    let {service} = EE.app;
    let deRow = this.getDeById(deRowId);
    if(ObjectUtil.isNotEmpty(deRow))
    {
      this.removeRowRelatedDatas(deRow);
    }
    
    let rootProject = this.ctx.treeProject.root;
    let isPricingTax = rootProject.projectTaxCalculation.taxCalculationMethod == '0';//1? 一般计税: 简易计税

    let parentDeRow = this.findFirstDeOrDeList(deRow);
    deRow.type = DeTypeConstants.DE_TYPE_USER_RESOURCE;
    deRow.deResourceKind = userResource.kind;//增加补充人材机类型
    deRow.isDeResource = CommonConstants.COMMON_YES;
    PropertyUtil.copyProperties(userResource ,deRow, [...DeDomain.avoidProperty,"sequenceNbr","resQty","parentId","deResourceKind","type","isDeResource"]);
    deRow.price = deRow.marketPrice;
    deRow.baseJournalPrice = deRow.dePrice;
    deRow.deName = deRow.materialName;
    deRow.deCode = deRow.materialCode;
    deRow.measureType = null;
    deRow.costFileCode = null;
    deRow.qfCode = null;
    deRow.costMajorName = null;
    
    await this._initQfAndMeasureType(unitId,deRow, service);
    // await this.attachDeRCJ([userResource], deRow, [],CommonConstants.COMMON_NO);
 
    //新增一条人材机-----开始
    let newRG = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr
          , deRow.kind);
    newRG.isDeResource = CommonConstants.COMMON_YES;
    this.typeTransttion(newRG);
    // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
    this.initResourceByUserDe(newRG, deRow.materialCode,deRow.materialName, deRow.price,1,deRow);
    newRG.specification = deRow.specification;
    newRG.taxRate = userResource.taxRate;
    newRG.unit = deRow.unit;
    this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newRG);
    if(newRG.isDataTaxRate === 1){
      if(isPricingTax){
        let taxRate= NumberUtil.divide(newRG.taxRate,100)+1;
        newRG.marketPrice =  NumberUtil.numberScale2(NumberUtil.divide( newRG.marketTaxPrice,taxRate)) ;
        newRG.baseJournalPrice = newRG.marketPrice;
      }else{
        let taxRate= NumberUtil.divide(newRG.taxRate,100)+1;
        newRG.marketTaxPrice =  NumberUtil.numberScale2(NumberUtil.multiply( newRG.marketPrice,taxRate)) ;
        newRG.baseJournalTaxPrice = newRG.marketTaxPrice;
      }
    }
    //新增一条人材机-----结束
    if(deRow.isTempRemove === CommonConstants.COMMON_YES
      || (ObjectUtil.isNotEmpty(parentDeRow) && parentDeRow.isTempRemove === CommonConstants.COMMON_YES)
    ){
      deRow.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
      await this.tempRemoveDeRow(deRow.sequenceNbr);
    }else{
      await this.notify(deRow);
    }
    if(add2UserRcj){
      this._addUserRcj2Map(newRG);
    }else{      
      newRG.supplementRcjFlag = RcjCommonConstants.SUPPLEMENT_RCJ_FLAG;
    }
    await this._rcjMemory(constructId,unitId,service,this.functionDataMap,ConvertUtil.deepCopy(newRG));
    try {
      await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        qfMajorType: deRow.qfCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
    return deRow;
  }

  _addUserDeRcj2Map(newRG){
    let rcjUserList = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
      newRG.supplementRcjFlag=RcjCommonConstants.SUPPLEMENT_RCJ_FLAG;
      newRG.supplementDeRcjFlag=RcjCommonConstants.SUPPLEMENT_DE_RCJ_FLAG;
      let deepResource = ConvertUtil.deepCopy(newRG);
      if(ObjectUtil.isEmpty(rcjUserList)){
        rcjUserList=[];
        this.functionDataMap.set(FunctionTypeConstants.PROJECT_USER_RCJ,rcjUserList);
      }
      rcjUserList.push(deepResource)
  }

  _addUserRcj2Map(newRG){
    newRG.supplementRcjFlag=RcjCommonConstants.SUPPLEMENT_RCJ_FLAG;
    let rcjUserList = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    let deepResource = ConvertUtil.deepCopy(newRG);
    if(ObjectUtil.isEmpty(rcjUserList)){
      rcjUserList=[];
      this.functionDataMap.set(FunctionTypeConstants.PROJECT_USER_RCJ,rcjUserList);
    }
    rcjUserList.push(deepResource)
  }
  /**
   *
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param userDe
   */
  async appendUserDe(constructId, unitId, deRowId, userDe, add2UserDeBase = true) {
    let {service} = EE.app; 

    let deRow = this.getDeById(deRowId);
    if(ObjectUtil.isNotEmpty(userDe) && ObjectUtil.isNotEmpty( deRow))
    {
      this.removeRowRelatedDatas(deRow);
    }
    deRow.type = DeTypeConstants.DE_TYPE_USER_DE;
    deRow.isDeResource = CommonConstants.COMMON_NO;
    PropertyUtil.copyProperties(userDe, deRow, [...DeDomain.baseDeToDeAvoidProperty,
      'price','total','totalNumber','CSum','RSum','JSum','rTotalSum','cTotalSum','jTotalSum','rfee'
      ,'cfee','jfee','sbfee','zcfee','parent','parentId','children','quantityExpression']);
    deRow.deRowId = deRow.sequenceNbr;
    deRow.resQty = 0;
    deRow.price = 0;
    deRow.totalNumber = 0;
    deRow.total = 0;
    deRow.CSum = 0;
    deRow.RSum = 0;
    deRow.JSum = 0;
    deRow.rTotalSum = 0;
    deRow.cTotalSum = 0;
    deRow.jTotalSum = 0;
    //  deRow.quantity = 0;
    deRow.classlevel01 = deRow.classifyLevel1;
    deRow.classlevel02 = deRow.classifyLevel2;
    deRow.classlevel03 = deRow.classifyLevel3;
    deRow.classlevel04 = deRow.classifyLevel4;
    deRow.classlevel05 = deRow.classifyLevel5;
    deRow.classlevel06 = deRow.classifyLevel6;
    deRow.classlevel07 = deRow.classifyLevel7;
    deRow.measureType = null;
    deRow.costFileCode = null;
    deRow.qfCode = null;
    deRow.costMajorName = null;
    deRow.standardId = null;
    //初始化及修改兼容
    if(ObjectUtils.isEmpty(deRow.quantity)){
      deRow.quantity = 0;
    }

    if(ObjectUtils.isNotEmpty(userDe.rcjList)){      
      this._createUserDeResouceByDeletede(unitId, userDe, deRow);
    }else{
      userDe.rcjList = this.createUserDeResource(userDe, deRow);
      let constructRcjArray = service.gongLiaoJiProject.gljRcjService.getAllRcj({constructId,unitId});
      for(let newRcj of userDe.rcjList){
        this._addUserDeRcj2Map(newRcj);
        //处理补充人材机的编码处理
        await service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCodeMemory(newRcj,true,constructRcjArray);
      }
    }

    let parentDeRow = this.findFirstDeOrDeList(deRow);
    
    //施工组织措施类别
    let measureTypeNoInited = true;
    let classifyResult = await service.gongLiaoJiProject.gljBaseDeService.getDeAndRcjByDeCode(constructId, unitId, userDe.classifyDeCode);
    if(ObjectUtils.isNotEmpty(classifyResult)){    
      let cslbItems = await service.gongLiaoJiProject.gljBaseCslbService.getByCode(classifyResult[0].cslbCode);
      if(ObjectUtils.isNotEmpty(cslbItems)){
        deRow.measureType = cslbItems[0].cslbName;
        measureTypeNoInited = false;
      }
      deRow.qfCode = classifyResult[0].qfCode;
      
    }
    await this._initQfAndMeasureType(unitId,deRow, service,measureTypeNoInited);
    //处理补充定额的 专业
    let classlevel01 = deRow.classifyLevel1?deRow.classifyLevel1.replace('工程','').replace('项目',''):'';
    deRow.classiflevel1 = this._removeChapterPrefix(classlevel01) +"-"+this._removeChapterPrefix(deRow.classifyLevel2);
    
    // if(add2UserDeBase){
    //   this.add2UserDeBase(constructId, deRow);
    // }
    //本身或父类临时删除
    if(deRow.isTempRemove === CommonConstants.COMMON_YES
      || (ObjectUtil.isNotEmpty(parentDeRow) && parentDeRow.isTempRemove === CommonConstants.COMMON_YES)
    ){
      
      deRow.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
      await this.tempRemoveDeRow(deRow.sequenceNbr);
    }else{
      await this.notify(deRow)

    }
    //加入内存中
    let rcjDeKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
    if(ObjectUtil.isNotEmpty(rcjs)){
      for(let item of rcjs){
        await this._rcjMemory(constructId,unitId,service,this.functionDataMap,ConvertUtil.deepCopy(item));
      }
    }
    try {
      // 添加补充的专业的取费表记录，并获取当前专业费率，包括：取费表未初始化的专业费率
      await service.gongLiaoJiProject.gljFreeRateService.addUnitFreeRateCostSummary(constructId, unitId, deRow.qfCode);

      await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        qfMajorType: deRow.qfCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }

    return deRow;
  }
  _createUserDeResouceByDeletede(unitId,userDe,deRow){
    let projectModel = this.ctx.treeProject.getAllNodes().find(item=>item.sequenceNbr === unitId);
      for(let rcjDetail of userDe.rcjList){
        let copyRcj = ConvertUtil.deepCopy(rcjDetail);
        copyRcj.sequenceNbr = Snowflake.nextId();
        copyRcj.parentId = deRow.sequenceNbr;
        copyRcj.deId = deRow.sequenceNbr;
        copyRcj.deRowId = deRow.sequenceNbr;
        copyRcj.unitId = unitId;
        //处理pbs
        if(ObjectUtils.isNotEmpty(rcjDetail.pbs)){
          copyRcj.pbs.forEach(pbsItem =>{
            pbsItem.parentId = copyRcj.sequenceNbr;
            pbsItem.unitId = unitId;
          });
        }
        // if(copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG)
        //   || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX)
        //   || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL)
        //   || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB)
        //   || copyRcj.materialCode.startsWith(DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC)){
          
        //   this._addUserRcjCodeCreateAndCheck(copyRcj,projectModel,false);
        // }

        this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, copyRcj);
      }
  }

 async _rcjMemory(constructId, unitId, service, businessMap, deepResource){
 //判定内存中不存在，则放入内存
  let objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
  if (ObjectUtils.isEmpty(objMap) || objMap.size===0) {
    businessMap.set(FunctionTypeConstants.RCJ_MEMORY,new Map());
    objMap = businessMap.get(FunctionTypeConstants.RCJ_MEMORY);
  }
  let  memoryRcj=objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId );
  if(ObjectUtils.isNotEmpty(memoryRcj)){
    let  existRcj = await service.gongLiaoJiProject.gljRcjCollectService.findAlikeRcj(memoryRcj,deepResource);
    if(ObjectUtils.isEmpty(existRcj)){
      memoryRcj.push(deepResource);
    }
  }else {
    let   memoryArray=new Array();
    memoryArray.push(deepResource)
    objMap.set(FunctionTypeConstants.UNIT_RCJ_MEMORY + constructId+ FunctionTypeConstants.SEPARATOR + unitId , memoryArray );
  }
 }
  /**
   * 通过code 查询定额 包括当前工程下的用户定额
   * @param constructId
   * @param unitId
   * @param deCode
   * @param deRowId
   * @returns {T}
   */
  async getDesAndAppendDe(constructId, unitId, deCode, deRowId) {
    let found = CommonConstants.COMMON_NO;

    let deModel = null;
    let result = null;
    if (ObjectUtil.isEmpty(deCode)) {
      return null;
    }
    let {service} = EE.app;
    //1先查询基础定额
    result = await service.gongLiaoJiProject.gljBaseDeService.getDeAndRcjByDeCode(constructId, unitId, deCode);
    if (ObjectUtil.isNotEmpty(result)) {
      result = result[0];//随机取一个
      //如果找到直接添加
      found = CommonConstants.COMMON_YES;
      deModel = await this.appendBaseDe(constructId, unitId, result.sequenceNbr, deRowId,true,true);
      await this.extendQuantity(constructId, unitId, deRowId);
    }
    else if(ObjectUtil.isEmpty(result))
    {
      //查找编码对应的人材机
      result = await service.gongLiaoJiProject.gljBaseRcjService.getRcjByCode(constructId, unitId, deCode);
      if(ObjectUtil.isNotEmpty(result))
      {
        found = CommonConstants.COMMON_YES;
        deModel = await this.appendDeResource(constructId, unitId, result[0].sequenceNbr, deRowId);
        await this.extendQuantity(constructId, unitId, deRowId)
      }
    }
    //如果没有查到 在当前项目中查找
    if (ObjectUtil.isEmpty(result)) {      
      //所有的用户定额需要共用
      let results = this.ctx.allDeMap.getAllNodes().filter(item => [DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_RESOURCE,DeTypeConstants.DE_TYPE_USER_DE,DeTypeConstants.DE_TYPE_USER_RESOURCE].includes(item.type) 
      && this._fixCode(item.deCode) === deCode && item.unitId === unitId);
      if(ObjectUtil.isNotEmpty(results)){
        if(results.length > 1){
          results.sort((a,b)=>b.updateDate-a.updateDate);
        }
        result = results[0];
        found = CommonConstants.COMMON_YES;
        deModel = await this.appendBaseDeByLocal(constructId, unitId, result, deRowId,false);
        await this.extendQuantity(constructId, unitId, deRowId);
      }
    }
    //如果没有查到
    if (ObjectUtil.isEmpty(result)) {
      //2查询用户定额
      let userDeBases = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_DE);
      if(ObjectUtil.isNotEmpty(userDeBases)) {

        result = userDeBases.find(item => this._fixCode(item.deCode) === deCode && item.unitId === unitId);
        if (ObjectUtil.isNotEmpty(result)) {
          found = CommonConstants.COMMON_YES;
          switch (result.type) {
            case DeTypeConstants.DE_TYPE_USER_DE:
              //3.1 如果是用户定额
              deModel = this.appendUserDe(constructId, unitId, deRowId, result,false);       
              // deModel = await this.appendBaseDeByLocal(constructId, unitId, result, deRowId,false);
              break;
            case DeTypeConstants.DE_TYPE_USER_RESOURCE:
              //3.2 如果是 用户定额人材机
              deModel = await this.appendUserResource(constructId, unitId, deRowId,result,false);

              break;
          }
        }

      }
      if(ObjectUtil.isEmpty(result)){

        let userDeBases = await service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructId, unitId);

        if(ObjectUtil.isNotEmpty(userDeBases)) {

          let resultUs = userDeBases.filter(item => this._fixCode(item.materialCode) === deCode && item.unitId === unitId);
          if (ObjectUtil.isNotEmpty(resultUs)) {
            if(resultUs.length > 1){
              resultUs.sort((a,b)=>b.updateDate-a.updateDate);
            }
            result = resultUs[0];
            found = CommonConstants.COMMON_YES;
            let newResult = ConvertUtil.deepCopy(result);
            newResult.unitId=unitId;
            newResult.deId=deRowId;
            newResult.deRowId=deRowId;
            newResult.sequenceNbr=null;
            newResult.parentId = null;
             //3.2 如果是 用户定额人材机
             deModel = await this.appendUserResource(constructId, unitId, deRowId,newResult,false);
          }
        }
      }
      if(ObjectUtil.isEmpty(result)){

        //还没有从这里取值
        let userDeBases = this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);

        if(ObjectUtil.isNotEmpty(userDeBases)) {

          let resultUs = userDeBases.filter(item => this._fixCode(item.materialCode) === deCode && item.unitId === unitId);
          if (ObjectUtil.isNotEmpty(resultUs)) {
            if(resultUs.length > 1){
              resultUs.sort((a,b)=>b.updateDate-a.updateDate);
            }
            result = resultUs[0];
            found = CommonConstants.COMMON_YES;
            let newResult = ConvertUtil.deepCopy(result);
            newResult.unitId=unitId;
            newResult.deId=deRowId;
            newResult.deRowId=deRowId;
            newResult.sequenceNbr=null;
            newResult.parentId = null;
             //3.2 如果是 用户定额人材机
             deModel = await this.appendUserResource(constructId, unitId, deRowId,newResult,false);
          }
        }
      }
      if(found === CommonConstants.COMMON_YES){
        await this.extendQuantity(constructId, unitId, deRowId)
      }
    }

    if(found === CommonConstants.COMMON_YES){
      return deModel;
    }
    return found;
  }


  _removeChapterPrefix(obj){
    if(ObjectUtil.isEmpty(obj)){
      return obj;
    }
    let aa = obj.replace(/^\(.*?\)/g,'');
    let bb = aa.replace(/^第([\s\S]*?)册\s*/g,'');
    let firstObj = bb.replace(/^第([\s\S]*?)章\s*/g,'');
    let dd = firstObj.replace(/(?:一|二|三|四|五|六|七|八|九|十)[、|^ ]/g,'');
    let ss = dd.replace(/[0-9A-za-z]([\s\S]*?)[\.|^ |]\s*/g,'');
    let secondObj = ss.replace(/^\d+(\.\d+)?/g,'');
    return secondObj;
  }
  _calculateLevel(deRow,typeLength){
    let count = 0;
     
    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.type) && ObjectUtil.isEmpty(deRow.fbType)){
      count++
    }   
    if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.type) && ObjectUtil.isEmpty(deRow.children)) {
      return -999;
    } 
     
    if( ObjectUtil.isNotEmpty(deRow.children)){
      if([DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(deRow.children[0].type)){
        let max = -5;
        for(let child of deRow.children){
          let childLevel = this._calculateLevel(child,typeLength);
          max = max>childLevel?max:childLevel;
        }
        count += max;  
      }else{
        let resouceFlag = true;
        for(let child of deRow.children){
          if(child.type !== DeTypeConstants.DE_TYPE_USER_RESOURCE && child.type !== DeTypeConstants.DE_TYPE_RESOURCE ){
            resouceFlag = false;
          }
        }
        if(resouceFlag){//都是人材机的分部 会抵消typelength长度
          count = count - typeLength + 1;
        }
      }

    }
    return count;
  }

  
  async arrangeDe(unitId,types){
    //获取根定额
    let rootDe = this.getRoot(unitId);
    //排序
    types.sort((a,b)=>a-b);
    let arrangeNodes = [];
    //获取分部数据
    let fbList = [];
    this._getFbDepth(rootDe,fbList,[DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
    //获取该单位下所有定额
    let allNodes = this.getDes(item=>item.unitId === unitId);
    //计算层级
    if(types.indexOf('4') === -1 && types.length > 0){
      let typeAddLength = types.length;
      let rootLevel = this._calculateLevel(rootDe,typeAddLength);
      if((rootLevel + typeAddLength)>4){
        return 500;
      }
    }
    //如果有删除类型，先删除数据
    if(types.indexOf('4') > -1 && fbList.length > 0){
      //重置父级
      rootDe.children = []
      for(let node of allNodes){
        if(![DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.type)
          &&node.parent 
          && [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.parent.type)){
            if(node.parent != rootDe){
              node.parent = rootDe;
              node.parentId = rootDe.sequenceNbr;
              rootDe.children.push(node);
            }
          arrangeNodes.push(node);
        }
      }
      if(arrangeNodes.length>0){
        for(let fbNode of fbList){
          this.ctx.deMap.removeNodeMapById(fbNode.sequenceNbr);
          fbNode.parent.removeChild(fbNode);
        }
      }
    }else{
      //获取整理后生成的分部
      let deleteFbList = [];
      for(let fbNode of fbList){
        if(ObjectUtil.isNotEmpty(fbNode.fbType)){
          deleteFbList.push(fbNode);
        }
      }
      //获取整理后的分部的子分部
      let childAllFbList = [];
      for(let delFbNode of deleteFbList){
        let childFbList = [];
        this.findChilds(delFbNode,childFbList,[DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
        childAllFbList = childAllFbList.concat(childFbList);
      }
      //把不存在的子分部加入删除分部中
      for(let childAllItem of childAllFbList){
        if(deleteFbList.indexOf(childAllItem) == -1){
          deleteFbList.push(childAllItem);
        }
      }
      //删除子目的分部关系
      for(let node of allNodes){
        if(ObjectUtil.isEmpty(node.parent) && ![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.type)){
          node.parent = this.getDeById(node.parentId);
        }
        if(![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.type)
          && node.parent 
          && [DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(node.parent.type)){
          if(deleteFbList.length >0 ){
            let upParentFb = this.getParentFromRange(deleteFbList,node);
            if(node !== upParentFb){
              node.parent = upParentFb.parent;
              node.parentId = upParentFb.parentId;
              upParentFb.parent.children.splice( upParentFb.parent.children.indexOf(upParentFb),0,node);
            }
          }
          arrangeNodes.push(node);
        }
      }
      //删除整理后的分部
      if(arrangeNodes.length>0){

        for(let fbNode of deleteFbList){
          this.ctx.deMap.removeNodeMapById(fbNode.sequenceNbr);
          fbNode.parent?fbNode.parent.removeChild(fbNode):null;
        }
      }
    }

    let caculateDe = null;
    let splitStr = "-AA-";
    if(arrangeNodes.length > 0){
      //补充定额
      let buchongNodes = [];
      let nonTypeNodes = [];
      for(let arrangeNode of arrangeNodes){
        if(arrangeNode.type === DeTypeConstants.DE_TYPE_EMPTY ){
          nonTypeNodes.push(arrangeNode);
        }
        if(arrangeNode.type === DeTypeConstants.DE_TYPE_USER_RESOURCE || arrangeNode.type === DeTypeConstants.DE_TYPE_RESOURCE ){
          buchongNodes.push(arrangeNode);
        }
      }
      //处理空类型的定额
      if(ObjectUtil.isNotEmpty(nonTypeNodes)){
        for(let noNode of nonTypeNodes){
          this.ctx.deMap.removeNodeMapById(noNode.sequenceNbr);
          noNode.parent?noNode.parent.removeChild(noNode):null;
        }
      }
      let parentMap = new Map();
      //处理不同类型分部
      for(let type of types){
        if(type == '4'){
          continue;
        }
        //处理并按父分部分类
        for(let arrangeNode of arrangeNodes){
          //过滤安装及人材机定额
          if(arrangeNode.type === DeTypeConstants.DE_TYPE_EMPTY
            || arrangeNode.type === DeTypeConstants.DE_TYPE_USER_RESOURCE 
            || arrangeNode.type === DeTypeConstants.DE_TYPE_RESOURCE){
            continue;
          }
          let key = arrangeNode.parentId + splitStr + this._getPrefix(arrangeNode,type);
          let parentFBList = null;
          if(parentMap.has(key)){
            parentFBList = parentMap.get(key);
          }else{
            parentFBList = [];
            parentMap.set(key,parentFBList)
          }
          let eIndex = parentFBList.findIndex(eItem=>eItem.sequenceNbr === arrangeNode.sequenceNbr);
          if(eIndex < 0){
            parentFBList.push(arrangeNode);
          }
        }
      }

      //创建 新的分部
      for(let [key,value] of parentMap){
        let newModel = new StandardDeModel(value[0].constructId,value[0].unitId,Snowflake.nextId(),value[0].parentId,value[0].parent.type ===DeTypeConstants.DE_TYPE_DEFAULT ?DeTypeConstants.DE_TYPE_FB :DeTypeConstants.DE_TYPE_ZFB);
        let keyArr = key.split(splitStr);
        newModel.deName = keyArr[1].trim();

        newModel.deCode = this._getArrangeCode(value[0].parent);
        newModel.fbType = '1';//无具体意思，判断是否整理分部的部分
        let index = null;
        if(ObjectUtil.isNotEmpty(value[0].parent)){
          index =value[0].parent.children.indexOf(value[0])
        }
        newModel = await this.createDeRow(newModel,index);
        newModel.displaySign = BranchProjectDisplayConstant.open;
        //重新挂载子目新的分部
        for(let valueItem of value){
          //解除父级children与现有关系
          valueItem.parent.removeChild(valueItem);
          newModel.addChild(valueItem)
        }
        caculateDe = newModel;
      }
      
      
      //处理人材机分部
      if(ObjectUtil.isNotEmpty(buchongNodes) && ObjectUtil.isNotEmpty(types) 
        && ((types.indexOf('4') === -1 &&types.length > 0)|| (types.indexOf('4') > -1 &&types.length > 1)) ){
        // 按parentid 分组
        let buMap = new Map();
        for(let node of buchongNodes){
          let buArr;
          if(buMap.has(node.parentId)){
            buArr =  buMap.get(node.parentId);
          }else{
            buArr = [];
            buMap.set(node.parentId,buArr);
          }
          buArr.push(node);
        }
        for(let [key,value] of buMap){
          let newModel = new StandardDeModel(value[0].constructId,value[0].unitId,Snowflake.nextId(),key,DeTypeConstants.DE_TYPE_ZFB);
          newModel.deName = '补充分部';
          newModel.fbType = '3';
          newModel.deCode = this._getArrangeCode(value[0].parent);
          newModel = await this.createDeRow(newModel);
          newModel.displaySign = BranchProjectDisplayConstant.open;
          //重新挂载子目新的分部
          for(let valueItem of value){
            valueItem.parent.removeChild(valueItem);
            newModel.addChild(valueItem);
          }
          caculateDe = newModel;
        }
      }
    }

    //处理计算引擎 ,随机一个分部都可以影响所有分部的重新计算
    if(caculateDe != null){
      
      let fc = FBCalculator.getInstance({constructId: caculateDe.constructId, unitId: caculateDe.unitId, deRowId: caculateDe.sequenceNbr},this.ctx);
      await fc.analyze();
    }
    //保存整理类型
    let objMap = this.functionDataMap.get(FunctionTypeConstants.YSH_TABLELIST);
    if(ObjectUtil.isEmpty(objMap)){
      objMap = new Map();
      this.functionDataMap.set(FunctionTypeConstants.YSH_TABLELIST,objMap);
    }
    objMap.set(FunctionTypeConstants.YSH_TABLELIST_ARRANGE+unitId,types);
    return 200;
  }
  _getArrangeCode(arrangeDe){
    let count = 0;
    for(let child of arrangeDe.children){
      if(child.fbType && child.fbType === '1'){
        count++;
      }
    }
    count ++;
    let countStr = count.toString().padStart(2,'0');
    if(arrangeDe.type === DeTypeConstants.DE_TYPE_DEFAULT || ObjectUtil.isEmpty(arrangeDe.fbType)){
      return countStr;
    }else{
      return arrangeDe.deCode+countStr;
    }
  }
  _getPrefix(arrangeNode,type){
    let prefix = null;
    if(type == "1"){
      prefix = arrangeNode.type === DeTypeConstants.DE_TYPE_USER_DE?arrangeNode.classifyLevel1:arrangeNode.classlevel01
      if(ObjectUtil.isEmpty(prefix)){
        return "---";
      }
    }
    if(type == "2"){
      prefix = arrangeNode.type === DeTypeConstants.DE_TYPE_USER_DE?arrangeNode.classifyLevel2:arrangeNode.classlevel02
      if(ObjectUtil.isEmpty(prefix)){
        return this._getPrefix(arrangeNode, "1");
      }
    }
    if(type == "3"){
      prefix = arrangeNode.type === DeTypeConstants.DE_TYPE_USER_DE?arrangeNode.classifyLevel3:arrangeNode.classlevel03;
      if(ObjectUtil.isEmpty(prefix)){
        return this._getPrefix(arrangeNode, "2");
      }
    }
    return this._removeChapterPrefix(prefix); 
  }
  //获取范围内定额的父级
  getParentFromRange(deList,deNode){
    let de = deList.find(item=>item.sequenceNbr === deNode.parentId);
    if(ObjectUtil.isEmpty(de)){
      return deNode;
    }
    return this.getParentFromRange(deList,de);
  }
  /**
   * 上移下移功能
   * @param {*} sequenceNbr 
   * @param {*} type 
   */
  async moveUpAndDown(deRowIds,type){

    let nodesFilter = this.getDes(item=>deRowIds.indexOf(item.sequenceNbr)>-1);
    // 过滤找到最顶级的同级定额
    let nodes = [];
    if(ObjectUtil.isNotEmpty(nodesFilter)){
      let parentId = null;
      let tempNodes = [];
      for(let node of nodesFilter){
        let parentNode = nodesFilter.find(item=>item.sequenceNbr === node.parentId);
        if(ObjectUtil.isNotEmpty(parentNode)){
          continue;
        }
        tempNodes.push(node);
      }

      for(let node of tempNodes){
        if(ObjectUtil.isEmpty(parentId)){
          parentId = node.parentId;
        }
        if(node.parentId === parentId){
          nodes.push(node);
        }
      }
    }

    if(ObjectUtil.isNotEmpty(nodes) && ObjectUtil.isNotEmpty(nodes[0].parentId))
    {
      let parentNode = this.getDeById(nodes[0].parentId);
      if(ObjectUtil.isNotEmpty(parentNode)){
        let childrens = parentNode.children;
        if(ObjectUtils.isEmpty(childrens)){
          childrens = this.getDes(item=>item.parentId === parentNode.sequenceNbr);
        }
        //重置父级排序
        childrens.forEach((item,index)=> item.index=index);

        if(type === "up"){
          //nodes 排序  正向
          nodes.sort((a,b)=>a.index-b.index);
          for(let mNode of nodes){
            let curIndex = mNode.index;
            if(curIndex > 0){
              let preNode = childrens.find(item=>item.index === (curIndex-1));
              let preRealIndex = nodes.findIndex(item=>item.sequenceNbr === preNode.sequenceNbr);
              if(preRealIndex>-1){
                continue;
              }
              mNode.index = preNode.index;
              preNode.index = curIndex;
            }
          }
        }  
        if(type === "down"){
          //nodes 排序  反向
          nodes.sort((a,b)=>b.index-a.index);
          for(let mNode of nodes){
            let curIndex = mNode.index;
            if(curIndex < (childrens.length-1)){
              let nextNode = childrens.find(item=>item.index === (curIndex+1));
              let nextRealIndex = nodes.findIndex(item=>item.sequenceNbr === nextNode.sequenceNbr);
              if(nextRealIndex>-1){
                continue;
              }
              mNode.index = nextNode.index;
              nextNode.index = curIndex;
            }
          }
        }
      }
    }
  }

  /**
   * 通过code 查询定额 包括当前工程下的用户定额
   * @param constructId
   * @param unitId
   * @param deCode
   * @param deRowId
   * @returns {T}
   */
  async getDesAndAppendDeAz(constructId, unitId, deCode, deRowId) {
    let found = {};
    found.found = CommonConstants.COMMON_NO;

    if (ObjectUtil.isEmpty(deCode)) {
      return null;
    }

    //1先查询当前行定额数据
    let row = this.getDeById(deRowId);

    //查询当前单位安装费用类型
    let sameDecodeList = [];
    let anDeList = this.getDeTreeDepth(constructId, unitId, null, ["07"]);
    if (ObjectUtil.isEmpty(anDeList)) {
      return found;
    } else {
      sameDecodeList = anDeList.filter(p => p.deCode === deCode && p.deRowId !== deRowId);
      if (ObjectUtil.isEmpty(sameDecodeList)) {
        return found;
      }
    }

    if (sameDecodeList.length === 1) {
      found.found = CommonConstants.COMMON_YES;
      row.type = sameDecodeList[0].type;
      await this.appendBaseDeAz(constructId, unitId, sameDecodeList[0].sequenceNbr, deRowId, true);
      await this.extendQuantity(constructId, unitId, deRowId);
    } else {
      found.found = 2;
      found.list = sameDecodeList;
    }

    return found;
  }

  /**
   * 通过code 查询定额 包括当前工程下的用户定额
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param resourcesDeRowId
   * @returns {T}
   */
  async getDeAndAppendDeAzInsert(constructId, unitId, deRowId, resourcesDeRowId) {
    let found = {};
    found.found = CommonConstants.COMMON_NO;

    let resourcesRow = this.getDeById(resourcesDeRowId);

    if (ObjectUtil.isEmpty(resourcesRow)) {
      return found;
    }
    found.found = CommonConstants.COMMON_YES;
    row.type = resourcesRow.type;
    await this.appendBaseDe(constructId, unitId, anDeList[0].sequenceNbr, deRowId, true);
    await this.extendQuantity(constructId, unitId, deRowId);

    return found;
  }

  _createDeTcResource(deModel,baseDe) {
    if (ObjectUtil.isNotEmpty(baseDe.compensation)  && baseDe.compensation.rgfCompensation != 0){
      let newRG = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(),deModel.sequenceNbr
          , ResourceKindConstants.TYPE_R);
      newRG.isDeResource = CommonConstants.COMMON_NO;
      newRG.isDeCompensation = CommonConstants.COMMON_YES;
      this.typeTransttion(newRG);
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newRG, '', DeCommonConstants.COMPENSATION_MATERIAL_RG_LABEL, 1,baseDe.compensation.rgfCompensation,deModel);

      this.resourceDomain.createResource(deModel.unitId, deModel.sequenceNbr, newRG);
    }
    if (ObjectUtil.isNotEmpty(baseDe.compensation)  && baseDe.compensation.clfCompensation != 0){
      let newCL = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(),deModel.sequenceNbr
          , ResourceKindConstants.TYPE_C);
      newCL.isDeResource = CommonConstants.COMMON_NO;
      newCL.isDeCompensation = CommonConstants.COMMON_YES;
      this.typeTransttion(newCL);
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newCL, '', DeCommonConstants.COMPENSATION_MATERIAL_CL_LABEL, 1,baseDe.compensation.clfCompensation,deModel);

      this.resourceDomain.createResource(deModel.unitId, deModel.sequenceNbr, newCL);
    }
    if (ObjectUtil.isNotEmpty(baseDe.compensation)  && baseDe.compensation.jxfCompensation != 0){
      let newJX = new ResourceModel(deModel.constructId, deModel.unitId, Snowflake.nextId(),deModel.sequenceNbr
          , ResourceKindConstants.TYPE_J);
      newJX.isDeResource = CommonConstants.COMMON_NO;
      newJX.isDeCompensation = CommonConstants.COMMON_YES;
      this.typeTransttion(newJX);
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newJX, '', DeCommonConstants.COMPENSATION_MATERIAL_JX_LABEL, 1,baseDe.compensation.jxfCompensation,deModel);

      this.resourceDomain.createResource(deModel.unitId, deModel.sequenceNbr, newJX);
    }
  }
  /**
   *
   * @param userDe
   * @param deRow
   */
  createUserDeResource(userDe, deRow) {
    let userResource = [];
    let projectModel = this.ctx.treeProject.getAllNodes().find(item=>item.sequenceNbr === deRow.unitId);
    if (ObjectUtil.isNotEmpty(userDe.rfee))
    {
      let newRG = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr
          , ResourceKindConstants.TYPE_R);
      newRG.isDeResource = CommonConstants.COMMON_NO;
      this.typeTransttion(newRG);
      let materialName = userDe.deName + DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_LABEL
      // BCRGF ;单位=元；名称=“补充人工费”；类别=人工费；消耗量=1；定额价=市场价=【输入的人工费】；其余值为空 ②材料：材料编码=
      this.initResourceByUserDe(newRG, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG, materialName, userDe.rfee,1,deRow);
      
      DeCommonConstants.initCodeAndName(newRG,projectModel);
      newRG.materialName = materialName
      userResource.push(newRG);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newRG);
    }
    if (ObjectUtil.isNotEmpty(userDe.cfee)) {
      let newCL = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr, ResourceKindConstants.TYPE_C);
      newCL.isDeResource = CommonConstants.COMMON_NO;
      this.typeTransttion(newCL);
      let materialName = userDe.deName + DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_LABEL
      // BCCLF ;名称=“补充材料费”；单位=元；类别=材料费；消耗量=1；定额价=市场价=【输入的材料费】；其余00值为空 ③主材：材料编码=
      this.initResourceByUserDe(newCL, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_LABEL, userDe.cfee,1,deRow);
     
      DeCommonConstants.initCodeAndName(newCL,projectModel);
      newCL.materialName = materialName
      userResource.push(newCL);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newCL);
    }
    if (ObjectUtil.isNotEmpty(userDe.jfee)) {
      let newJX = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr, ResourceKindConstants.TYPE_J);
      // BCJXF ;名称=“补充机械费”；单位=元；类别=机械费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空 ⑤设备：材料编码=
      newJX.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newJX);
      let materialName = userDe.deName + DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_LABEL
      this.initResourceByUserDe(newJX, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX, materialName, userDe.jfee,1,deRow);
      
      DeCommonConstants.initCodeAndName(newJX,projectModel);
      newJX.materialName = materialName
      userResource.push(newJX);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newJX);
    }
    if (ObjectUtil.isNotEmpty(userDe.zcfee)) {
      let newZC = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr, ResourceKindConstants.TYPE_ZC);
      newZC.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newZC);
      let materialName = userDe.deName + DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC_LABEL
      // BCZCF ;名称=“补充主材费”；单位=元；类别=主材费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空 ④机械：材料编码=
      this.initResourceByUserDe(newZC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_ZC, materialName, userDe.zcfee,1,deRow);
      
      DeCommonConstants.initCodeAndName(newZC,projectModel);
      newZC.materialName = materialName
      // 补充定额主材和设备 以前存在则同步
      this.zsRcjExtend(deRow.unitId, newZC);
      userResource.push(newZC);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newZC);
      deRow.isExistedZcSb = CommonConstants.COMMON_YES;
    }
    if (ObjectUtil.isNotEmpty(userDe.sbfee)) {
      let newSB = new ResourceModel(deRow.constructId, deRow.unitId, Snowflake.nextId(),deRow.sequenceNbr, ResourceKindConstants.TYPE_SB);
      newSB.isDeResource =  CommonConstants.COMMON_NO;
      this.typeTransttion(newSB);
      let materialName = userDe.deName + DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB_LABEL
      // BCSBF ;名称=“补充设备费”；单位=元；类别=人工费；消耗量=1；定额价=市场价=【输入的主材费】；其余值为空
      this.initResourceByUserDe(newSB, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_SB, materialName, userDe.sbfee,1,deRow);
      
      DeCommonConstants.initCodeAndName(newSB,projectModel);
      newSB.materialName = materialName
      // 补充定额主材和设备 以前存在则同步
      this.zsRcjExtend(deRow.unitId, newSB);
      userResource.push(newSB);
      this.resourceDomain.createResource(deRow.unitId, deRow.sequenceNbr, newSB);
      deRow.isExistedZcSb = CommonConstants.COMMON_YES;
    }
    return userResource;
  }

  typeTransttion(rcj){
    //rcj.kind=rcj.type ;
    for (let key in RcjTypeEnum) {
      if (RcjTypeEnum[key].code == rcj.type) {
        rcj.kind=RcjTypeEnum[key].code;
        rcj.type =  RcjTypeEnum[key].desc;
      }
    }
  }

  /**
   *
   * @param deRowModel
   */
  removeRowRelatedDatas(deRowModel) {
    if(ObjectUtil.isEmpty(deRowModel)) {
      return;
    }
    deRowModel.isFyrcj=null;//防止费用定额污染
    if(deRowModel.type === DeTypeConstants.DE_TYPE_USER_DE){
      let deRowBack = ConvertUtil.deepCopy(deRowModel);
      this.add2UserDeBase(deRowBack.constructId,deRowBack);
    }
    //删除标准换算
    
    let conversionMap = this.functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
    if(ObjectUtils.isNotEmpty(conversionMap)){
      let unitConversionMap = conversionMap.get(deRowModel.unitId);  
      let deConversionMap = unitConversionMap?.get(deRowModel.sequenceNbr);
      if(ObjectUtils.isNotEmpty(deConversionMap)){
        unitConversionMap.delete(deRowModel.sequenceNbr)
      }
    }

    let childs = [];
    this.findDeRows(deRowModel, childs);
    this.removeRowRelatedDataById(childs);
    
    this.ctx.resourceMap.removeByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRowModel.sequenceNbr);
  }

  findFirstDeOrDeList(deRow) {
    if(ObjectUtil.isEmpty(deRow)) {
      return;
    }
    let parentList = [];
    this.findParents(deRow,parentList,[DeTypeConstants.DE_TYPE_DE, DeTypeConstants.DE_TYPE_DELIST]);
    if(ObjectUtils.isEmpty(parentList)) {
      return;
    }
    return parentList[0];

  }

  async appendDeByUnitDe(localDe, unitId, deRowId,unitConversionMap,replaceResQty = false) {

    if(ObjectUtil.isEmpty(localDe)){
      return null;
    }
    let {service} = EE.app;
    let deModel = this.getDeById(deRowId);
    deModel.resQty = 0;
    deModel.totalNumber = 0;
    deModel.price = 0;
    //先清除数据
    if(ObjectUtil.isNotEmpty( deModel)){
      this.removeRowRelatedDatas(deModel);
    }
    //处理属性值
    PropertyUtil.copyProperties(localDe, deModel, DeDomain.baseDeToDeAvoidProperty.concat(['index','deRowId','parent','parentId','children']));
    if(!replaceResQty){
      deModel.quantity = 0; //重置工程量为0
      deModel.quantityExpression='';
      deModel.originalQuantity = 0;
      deModel.initDeRcjNameList = [];
    }
    deModel.type = localDe.type;
    deModel.unitId = unitId; //防止localDe unitId 与deModel不一致
    //处理定额的人材机
    let rcjList = localDe.rcjList;
    let originalInitChildCodes = deModel.initChildCodes;
    if(ObjectUtils.isNotEmpty(rcjList)){
      let initDeRcjNameList = [];
      let initChildCodes = [];
      for(let rcj of rcjList){

        let exitInitChildCode = null;
        if(ObjectUtils.isNotEmpty(originalInitChildCodes)){
          exitInitChildCode = originalInitChildCodes.find(item=>item.sequenceNbr == rcj.value.sequenceNbr);
        }
        let copyRcj =  ConvertUtil.deepCopy(rcj.value);
        this._resetRcjId(copyRcj,deModel.sequenceNbr);
        if(deModel.isTempRemove === CommonConstants.COMMON_YES && copyRcj.isTempRemove !== CommonConstants.COMMON_YES){
          copyRcj.isTempRemove = CommonConstants.COMMON_YES;
          copyRcj.changeResQty = copyRcj.resQty;
          copyRcj.resQty = 0;
        }
        this.resourceDomain.createResource(unitId,copyRcj.deRowId, copyRcj);
        let rcjNameObj = {};
        rcjNameObj.sequenceNbr = copyRcj.sequenceNbr;
        rcjNameObj.initMaterialName = copyRcj.materialName;
        rcjNameObj.code = copyRcj.materialCode;
        rcjNameObj.kind = copyRcj.kind;
        initDeRcjNameList.push(rcjNameObj);
        if(ObjectUtils.isNotEmpty(exitInitChildCode)){
          initChildCodes.push({code:copyRcj.materialCode,sequenceNbr:copyRcj.sequenceNbr,resQty:exitInitChildCode.resQty});
        }
      }
      deModel.initChildCodes = initChildCodes;
      deModel.initDeRcjNameList = initDeRcjNameList;
    }
    //处理标准换算    
    let deConversion = localDe.conversion ;
    if(ObjectUtil.isNotEmpty(deConversion)){
      let copyedConversion = ConvertUtil.deepCopy(deConversion);
      copyedConversion.deRowId = deModel.sequenceNbr;
      copyedConversion.sequenceNbr = deModel.sequenceNbr;
      copyedConversion.unitId = unitId;
      unitConversionMap.set(deModel.sequenceNbr,copyedConversion);
    }
    let unitProject =  this.ctx.treeProject.getNodeById(deModel.unitId);
    let oUnitProject =  this.ctx.treeProject.getNodeById(localDe.unitId);
     //取费专业类型不同时处理  
     if(unitProject.constructMajorType != oUnitProject.constructMajorType){
      //颠倒displayLable类型 仅 父级 反转  ，其他子级不需要反转啊
      DeTypeCheckUtil.checkAndUpdateDeType(deModel,this.ct,true);
    }
    //处理定额的工程量20250318
    DeUtils.setCostDeQuantityEq1(deModel);
    //处理子级定额
    if(ObjectUtils.isNotEmpty(localDe.children) &&  localDe.children.length > 0){
      let childIds = [];
      for(let child of localDe.children){        
        childIds.push(child.sequenceNbr);
      }
      await this.pasteDe(unitId,localDe.unitId,childIds, deModel,'childNoCircle',false);
    }else{
      //通知
      this.notify(deModel,true);
      DeTypeCheckUtil.checkAndUpdateDeType(deModel,this.ctx);
      try {
        //自动计算=各种记取+费用汇总通知
        await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
          unitId: deModel.unitId,
          singleId: null,
          constructId: deModel.constructId
        });
        // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        //   constructId: deModel.constructId,
        //   unitId: deModel.unitId,
        //   qfMajorType: deModel.qfCode
        // });   
      } catch (error) {
        console.error("捕获到异常:", error);
      }
    }
    
    return deModel;
  }

  async appendBaseDeByLocal(constructId, unitId, localDe, deRowId,checkType=false) {
    let localDeCopy = ConvertUtil.deepCopy(localDe);//不要污染原始数据
    let rcjList = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,localDe.sequenceNbr);
    localDeCopy.rcjList = rcjList;
    let conversionMap = await this.functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
    let unitConversionMap = conversionMap.get(localDeCopy.unitId);  
    localDeCopy.conversion = unitConversionMap?.get(localDe.sequenceNbr);

    return await this.appendDeByUnitDe(localDeCopy,unitId,deRowId,unitConversionMap,false);
  }

  /**
   * 给之前创建好的定额行关联基础定额,这样形成完整的工程定额
   * @param constructId
   * @param unitId
   * @param deStandardId
   * @param deRowId
   */
  async appendBaseDe(constructId, unitId, deStandardId, deRowId,checkType=false, isFront = false) {

    let {service} = EE.app;
    let deModel = this.getDeById(deRowId);
    deModel.quantity = 0;
    deModel.resQty = 0;
    deModel.totalNumber = 0;
    deModel.price = 0;
    deModel.isDeResource = CommonConstants.COMMON_NO;
    deModel.costFileCode = null;
    deModel.costMajorName=null;

    let baseDe = await service.gongLiaoJiProject.gljBaseDeService.getDeAndRcj(deStandardId);
    let cslbItems = await service.gongLiaoJiProject.gljBaseCslbService.getByCode(baseDe.cslbCode);
    if(cslbItems){
      baseDe.measureType = cslbItems[0].cslbName;
    }
    this.processMainMaterial (constructId,deRowId ,baseDe);
    await this._appendBaseDeByDe(constructId, unitId, baseDe, deModel, checkType, service,isFront);
    return deModel;
  }

  processMainMaterial (constructId,deRowId ,baseDe){
    let {service} = EE.app;
    let  taxMethod=this.ctx.treeProject.root.projectTaxCalculation.taxCalculationMethod;
    let businessMap = this.functionDataMap;
    let deMainMaterial = businessMap.get(FunctionTypeConstants.UNIT_DE_MAINMATERIAL);
    if(ObjectUtils.isEmpty(deMainMaterial)){
      deMainMaterial = new Map();
      businessMap.set(FunctionTypeConstants.UNIT_DE_MAINMATERIAL, deMainMaterial);
    }
    let  updateFlag =false;
    let   mainMaterialRcjs=deMainMaterial.get( deRowId );
    if(ObjectUtils.isNotEmpty(baseDe) && ObjectUtils.isNotEmpty(baseDe.deRcjRelationList) && ObjectUtils.isNotEmpty(mainMaterialRcjs)){
      for (let i = 0; i <baseDe.deRcjRelationList.length; i++) {
        let  relationRcj = baseDe.deRcjRelationList[i];
        let  rcj = baseDe.rcjList[i];
        let mainMaterialRcj = mainMaterialRcjs.find(item=>item.materialCode ===relationRcj.materialCode);
        if(ObjectUtils.isNotEmpty(mainMaterialRcj)){
          relationRcj.materialCode = mainMaterialRcj.materialCode;
          relationRcj.materialName = mainMaterialRcj.materialName;
          relationRcj.resQty = mainMaterialRcj.resQty;
          rcj.specification = mainMaterialRcj.specification;
          rcj.unit = mainMaterialRcj.unit;
          rcj.marketPrice= mainMaterialRcj.baseJournalPrice;
          rcj.marketTaxPrice= mainMaterialRcj.baseJournalPrice;
          service.gongLiaoJiProject.gljRcjService.calculateTax(rcj, taxMethod);
          rcj.baseJournalPrice = mainMaterialRcj.baseJournalPrice;
          rcj.baseJournalTaxPrice = mainMaterialRcj.baseJournalTaxPrice;
          updateFlag= true ;
        }
      }
    }
    if(updateFlag===true){
      deMainMaterial.delete(deRowId);
    }
  }

  async _appendBaseDeByDe(constructId, unitId, baseDe, deModel, checkType, service,isFront = false){
    if(ObjectUtil.isNotEmpty(baseDe) && ObjectUtil.isNotEmpty( deModel))
      {
        this.removeRowRelatedDatas(deModel);
      }
      await this.attachBaseDeProperty(baseDe, deModel);
      //appendBaseDe 初始化
      deModel.standardDeId = baseDe.standardDeId;
      deModel.value = baseDe.value;
      deModel.isAppendBaseDe = true;
      await service.gongLiaoJiProject.gljInitDeService.init(deModel);

      await this.attachSubDe(baseDe, deModel);
      //是否费用定额   用于自动计算
      deModel.isCostDe = DeUtils.isCostDeByBaseDe(baseDe);
      if(ObjectUtil.isNotEmpty(deModel.isCostDe) && (deModel.isCostDe === CostDeMatchConstants.ZJCS_DE||deModel.isCostDe == CostDeMatchConstants.AZ_DE)){
        deModel.measureType = null;
      }
      if(deModel.isCostDe == CostDeMatchConstants.AWF_DE || deModel.isCostDe == CostDeMatchConstants.ZJCS_DE){
        deModel.displayType =  DeTypeConstants.DE_TYPE_FEE_LABEL;
      }
      //安装费用收入插入位普通定额
      if(deModel.isCostDe == CostDeMatchConstants.AZ_DE){
        // deModel.isCostDe = CostDeMatchConstants.NON_COST_DE;
        if (deModel.unit == '%') {
          deModel.unit = '元';
        }
        // 费替换为’安‘
        deModel.displayType =  DeTypeConstants.DE_TYPE_ANZHUANG_FEE_LABEL
      }
      //处理展开/折叠标识
      deModel.displaySign = BranchProjectDisplayConstant.noSign;
      if (ObjectUtil.isNotEmpty(deModel.children)) {
        deModel.displaySign = BranchProjectDisplayConstant.open;
      }
      //处理定额的类型为 降
      if(deModel.value == 120 || deModel.isCostDe == CostDeMatchConstants.FXTJ_CG){
        deModel.type = DeTypeConstants.DE_TYPE_ZHUANSHI_FEE;
        deModel.displayType = DeTypeConstants.DE_TYPE_ZHUANSHI_FEE_LABEL;
      }
      if(isFront){
        //费用定额设置工程量初始为1
        DeUtils.setCostDeQuantityEq1(deModel);
      }
      
      if(ObjectUtils.isNotEmpty(deModel.isCostDe)&&  deModel.isCostDe !== CostDeMatchConstants.NON_COST_DE && (deModel.unit == '%'||deModel.unit == '元')) {
        deModel.unit = DeTypeConstants.DE_UNIT_LABEL;
        deModel.unitChanged = CommonConstants.COMMON_YES;
      }

      //取费专业不同，显示类型为借，随机更新父级标记
      let unitProject = await service.gongLiaoJiProject.gljProjectCommonService.getUnit(deModel.constructId, deModel.unitId);
      let changeCostFlag = unitProject.constructMajorType !== baseDe.libraryCode;
      if(changeCostFlag){
        deModel.remark = baseDe.libraryName;//费用专业不同，备注啊
        deModel.displayType = DeTypeConstants.DE_TYPE_JIE_LABEL;
        //借的子级为借
        if(deModel.type === DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(deModel.children)){
          deModel.children.forEach(item=>{
            item.displayType = DeTypeConstants.DE_TYPE_JIE_LABEL
            item.remark =  baseDe.libraryName;
            item.classiflevel1 = deModel.classiflevel1;///子级专业同父级
          });
        }
      }else{
        deModel.remark = baseDe.libraryName;//费用专业不管相同与否都要备注啊
        if(deModel.type === DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(deModel.children)){
          deModel.children.forEach(item=>{
            item.remark =  baseDe.libraryName;
            item.classiflevel1 = deModel.classiflevel1;///子级专业同父级
          });
        }
      }
      
      if (!ObjectUtil.isEmpty(baseDe.deRcjRelationList) && !ObjectUtil.isEmpty(baseDe.rcjList)) {
        //处理人材机
        await this.attachDeRCJ(this.resourceDomain,baseDe.deRcjRelationList, deModel, baseDe.rcjList);
      }
      if(ObjectUtil.isNotEmpty(baseDe.compensation)){
        this._createDeTcResource(deModel,baseDe)
      }
      deModel.updateValue(deModel);
  
      let parent = this.getDeById(deModel.parentId);
      if(checkType){
        DeTypeCheckUtil.updateParentDeType(deModel,this.ctx);
      }

      if(parent.isTempRemove === CommonConstants.COMMON_YES 
        || deModel.isTempRemove === CommonConstants.COMMON_YES){
          if(deModel.changeQuantity){
            deModel.originalQuantity = deModel.changeQuantity;//保留旧的原始工程量
            deModel.quantityExpression = deModel.changeQuantity;//保留旧的原始工程量
          }
          deModel.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
          await this.tempRemoveDeRow(deModel.sequenceNbr);
      }else{
        if(deModel.type === DeTypeConstants.DE_TYPE_DE && parent.type !== DeTypeConstants.DE_TYPE_DEFAULT) {
          await this.notify({constructId, unitId, deRowId :deModel.parentId}, true);
        } else {
          await this.notify({constructId, unitId, deRowId : deModel.sequenceNbr}, true);
        }
      }
      try {
        // 同步取费文件
        await service.gongLiaoJiProject.gljBaseFreeRateService.addFreeFileByDe(deModel,constructId,unitId);
        if(isFront){
          //自动计算=各种记取+费用汇总通知
          await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
            unitId: unitId,
            singleId: null,
            constructId: constructId
          });

        }
        // 同步更新费用代码
        // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        //   constructId: constructId,
        //   unitId: unitId,
        //   qfMajorType: deModel.qfCode
        // });
        //联动计算装饰超高人材机数量单价
        // await service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
      } catch (error) {
        console.error("捕获到异常:", error);
      }
  }


  /**
   * 给之前创建好的定额行关联基础定额,这样形成完整的工程定额
   * @param constructId
   * @param unitId
   * @param deStandardId
   * @param deRowId
   */
  async appendBaseDeAz(constructId, unitId, deStandardId, deRowId, checkType = false) {

    let {service} = EE.app;
    let deModel = this.getDeById(deRowId);
    deModel.quantity = 0;
    deModel.resQty = 0;

    let baseDe = this.getDeById(deStandardId);

    //处理借换标识
    //取费专业不同，显示类型为借，随机更新父级标记
    let unitProject = await service.gongLiaoJiProject.gljProjectCommonService.getUnit(deModel.constructId, deModel.unitId);
    let changeCostFlag = unitProject.constructMajorType !== baseDe.libraryCode;
    if (changeCostFlag) {
      deModel.remark = baseDe.libraryName;//费用专业不同，备注啊
      deModel.displayType = DeTypeConstants.DE_TYPE_JIE_LABEL;
      //借的子级为借
      if (deModel.type === DeTypeConstants.DE_TYPE_DELIST && ObjectUtils.isNotEmpty(deModel.children)) {
        deModel.children.forEach(item => {
          item.displayType = DeTypeConstants.DE_TYPE_JIE_LABEL
          item.remark = baseDe.libraryName;
        });
      }
    }


    //计取定额的人材机
    let baseDeRcjList = await service.gongLiaoJiProject.gljRcjService.getAllRcjDetail(constructId, unitId, baseDe.deRowId, DeTypeConstants.DE_TYPE_ANZHUANG_FEE);
    if (ObjectUtil.isNotEmpty(baseDeRcjList)) {
      //给当前添加人材机
      for (let item of baseDeRcjList) {
        let rcj = {};
        rcj.constructId = constructId;
        rcj.unitId = unitId;
        rcj.sequenceNbr = Snowflake.nextId();
        rcj.kind = item.kind;
        rcj.deRowId = deRowId;
        rcj.displaySign = item.displayType;
        rcj.isDeResource = item.isDeResource;
        rcj.dePrice = item.dePrice;
        rcj.marketPrice = item.marketPrice;
        rcj.unit = item.unit;
        rcj.levelMark = item.levelMark;
        rcj.type = item.type;
        rcj.parentId = deRowId;
        rcj.materialCode = item.materialCode;
        rcj.materialName = item.materialName;
        rcj.resQty = item.resQty;
        this.resourceDomain.createResource(unitId, deRowId, rcj);
      }
    }

    deModel.totalNumber = 0;
    deModel.price = baseDe.totalNumber;
    deModel.deCode = baseDe.deCode;
    deModel.deName = baseDe.deName;
    deModel.type = baseDe.type;
    deModel.unit = baseDe.unit;
    deModel.updateValue(deModel);

    let parent = this.getDeById(deModel.parentId);
    if (checkType) {
      DeTypeCheckUtil.updateParentDeType(deModel, this.ctx);
    }
    //本身或父级是临时删除，则需要临时删除处理
    if (parent.isTempRemove === CommonConstants.COMMON_YES
        || deModel.isTempRemove === CommonConstants.COMMON_YES) {
      if (deModel.changeQuantity) {
        deModel.originalQuantity = deModel.changeQuantity;//保留旧的原始工程量
      }
      deModel.isTempRemove = CommonConstants.COMMON_NO;//临时删除的行 不能删除先置为正常
      await this.tempRemoveDeRow(deModel.sequenceNbr);
    } else {
      if (deModel.type === DeTypeConstants.DE_TYPE_DE && parent.type !== DeTypeConstants.DE_TYPE_DEFAULT) {
        // await this.notify({constructId, unitId, deRowId: deModel.parentId}, true);
      } else {
        // await this.notify({constructId, unitId, deRowId}, true);
      }
    }
    try {
      await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        constructId: constructId,
        unitId: unitId,
        qfMajorType: deModel.qfCode
      });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
    return deModel;

  }
  _isRightKind(type,kind){
    if(type == '1' && kind == ResourceKindConstants.INT_TYPE_R){
      return true;
    }
    if(type == '3' && kind == ResourceKindConstants.INT_TYPE_J){
      return true;
    }
    if(type == '2' && (kind == ResourceKindConstants.INT_TYPE_C || kind == 6 || kind == 7 || kind == 8 || kind == 9 || kind == 10)){
      return true;
    }
    if(type == '4' && kind == ResourceKindConstants.INT_TYPE_SB){
      return true;
    }
    if(type == '5' && kind == ResourceKindConstants.INT_TYPE_ZC){
      return true;
    }
    return false;
  }
  _getRCJPrice(deRow,type){
    if(type == '1'){
      return deRow.RSum;
    }
    if(type == '3'){
      return deRow.JSum;
    }
    if(type == '2'){
      return deRow.CSum;
    }
    if(type == '4'){
      return deRow.SSum;
    }
    if(type == '5'){
      return deRow.ZSum;
    }
    return 0;
  }
  async updateRCJPrice(constructId, unitId, deRow,price,type){
    let {service} = EE.app;
    //删除调整数据
    let rcjDeKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
    let tzCode = DeCommonConstants.getTZCode(type);
    let delTzRcj = null;
    rcjs.forEach(item =>{        
      if(item.materialCode == tzCode){
        let deleteKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr,item.sequenceNbr);
        this.ctx.resourceMap.removeByPattern(deleteKey);
        delTzRcj = item;
      }        
    });

    let oldDeRow = deRow;
    //计算原始数据金额
    if(deRow.resourceTZ === CommonConstants.COMMON_YES){
      //计算不包主材设备就调整材料的单价， 
      await DeCalculator.getInstance({constructId, unitId,deRowId:deRow.sequenceNbr},this.ctx).analyze();
      oldDeRow = deRow;
    }
    let resQty =  price - this._getRCJPrice(oldDeRow,type);
    let differenceResource = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr,type);
    differenceResource.unit = "元";
    differenceResource.type = RcjTypeEnum['TYPE'+type].desc;
    differenceResource.kind = RcjTypeEnum['TYPE'+type].code;
    this.initResourceByUserDe(differenceResource,DeCommonConstants.getTZCode(type), DeCommonConstants.getTZName(type), 1,resQty,deRow);
    if(resQty != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResource);
      service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, differenceResource, 'addMerge', null, null);
    }else{
      if(ObjectUtil.isNotEmpty(delTzRcj)){
        service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, delTzRcj, 'del', null, null);
      }
    }
    //更新标识
    DeTypeCheckUtil.checkAndUpdateDeType(deRow, this.ctx);
    await this.notify(deRow, false);//单价修改不影响工程量
    deRow.resourceTZ = CommonConstants.COMMON_YES;
    //通知费用汇总
    try{
       //包含了所有
       await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
      // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
      //   constructId: constructId,
      //   unitId: unitId,
      //   qfMajorType: deRow.qfCode
      // });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  /**
   * 单价修改按比例分配
   * @param {*} constructId 
   * @param {*} unitId 
   * @param {*} deRowId 
   * @param {*} price 
   * @param {*} rSum 
   * @param {*} cSum 
   * @param {*} jSum 
   * @returns 
   */
  async updatePriceWithRCJ(constructId, unitId, deRowId,price, rSum,cSum,jSum) {
    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if(ObjectUtils.isEmpty(deRow)){
      return;
    }
    //1.先删除人材机
    let rcjDeKey = WildcardMap.generateKey(unitId, deRowId) + WildcardMap.WILDCARD;
    let rcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
    //用户定额的单价修改处理不一样，先删除人材机，然后增加即可
    if(deRow.type === DeTypeConstants.DE_TYPE_USER_DE){
      rcjs.forEach(item =>{        
        
          let deleteKey = WildcardMap.generateKey(unitId, deRowId,item.sequenceNbr);
          this.ctx.resourceMap.removeByPattern(deleteKey);
      });
      //2. 然后新增
      let newRcjLists = this.createUserDeResource({rfee:rSum,cfee:cSum,jfee:jSum},deRow);
      let constructRcjArray = service.gongLiaoJiProject.gljRcjService.getAllRcj({constructId,unitId});
      for(let newRcj of newRcjLists){
        //新增补充人材机放入缓存
        this._addUserDeRcj2Map(newRcj);
        //处理补充人材机的编码处理
        await service.gongLiaoJiProject.gljRcjCollectService.changeMaterialCodeMemory(newRcj,true,constructRcjArray);
      } 
    }else{
      rcjs.forEach(item =>{    
        if(item.isFyrcj ==0 
          || item.materialCode=== DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ
          || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ
          || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ){
            let deleteKey = WildcardMap.generateKey(unitId, deRowId,item.sequenceNbr);
            this.ctx.resourceMap.removeByPattern(deleteKey);
        }
    });
      //调整公共处理
      this._updatePriceCommon(this.resourceDomain,constructId, unitId,deRow,price,rSum,cSum,jSum);
    }
    //更新标识
    DeTypeCheckUtil.checkAndUpdateDeType(deRow, this.ctx);
    await this.notify(deRow, false);//单价修改不影响工程量

    //通知费用汇总
    try{
       //包含了所有
       await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
      // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
      //   constructId: constructId,
      //   unitId: unitId,
      //   qfMajorType: deRow.qfCode
      // });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }

  /**
   * 修改定额单价
   * @param {*} constructId 
   * @param {*} unitId 
   * @param {*} deRowId 
   * @param {*} price 
   * @param {*} isTc 
   * @returns 
   */
  async updatePrice(constructId, unitId, deRowId, price, isTc) {
    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if (ObjectUtil.isEmpty(deRow.price)) {
      throw Error("输入单价不能为空.");
    }

    let precision = await service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
    price = NumberUtil.numberFormat(price,precision.EDIT.DE.price);//定额保留两位，注意了啊

    
    let rootProject = this.ctx.treeProject.getNodeById(constructId);

    // let rcjs =  this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
    let rcjDeKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
    let rcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
    if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
     
      if(rootProject.pricingMethod === 1){
        deRow.price = price;
      }else{
        if(deRow.deResourceKind == ResourceKindConstants.INT_TYPE_ZC || deRow.deResourceKind == ResourceKindConstants.INT_TYPE_SB){
          deRow.baseJournalPrice = price;
        }else{
          throw Error("人材机定额基期价不能修改！");
        }
      }
      DeTypeCheckUtil.updateParentDeType(deRow, this.ctx);
      let rcjlist = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
      let rcjDetail = rcjlist.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES).value;
      //0 简易计税 含税
      if(rootProject.projectTaxCalculation.taxCalculationMethod == '0'){
        //rcjDetail.marketTaxPrice = price;
        await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId, singleId:null, unitId
          , deId:deRow.sequenceNbr
          , rcjDetailId:rcjDetail.sequenceNbr
          , constructRcj:{
            marketTaxPrice : price
          }});

      }else{
        //rcjDetail.marketPrice = price;
        await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId, singleId:null, unitId
          , deId:deRow.sequenceNbr
          , rcjDetailId:rcjDetail.sequenceNbr
          , constructRcj:{
            marketPrice : price
          }});
        
      }
      await this.notify(deRow, false); //单价修改不影响工程量
      return ;
    }
    
    let oldDeRow = deRow;
    let deleteRcj = false;    
    // if(deRow.resourceTZ === CommonConstants.COMMON_YES || isTc){
    //   //计算不包主材设备就调整材料的单价， 
    //   await DeCalculator.getInstance({constructId, unitId,deRowId},this.ctx).analyze();
    //   oldDeRow = deRow;
    // }else{
    //   oldDeRow = deRow;
    // }

    //1.0  先记录比列
    let resQtyR = null;
    let resQtyC = null;
    let resQtyJ = null;
    // 差额乘以比例 算出来的是消耗量  人 单位 为：人工 机械 为 ：台班 机械？
    //update 20240808 ye 保证原始比例不变
    let percentR = ObjectUtil.isEmpty(oldDeRow.RDSum) || oldDeRow.RDSum == 0 ?  0 : NumberUtil.divide(oldDeRow.RDSum, oldDeRow.baseJournalPrice);//调整了price
    if(rootProject.pricingMethod === 1){
      percentR = ObjectUtil.isEmpty(oldDeRow.RSum) || oldDeRow.RSum == 0 ?  0 : NumberUtil.divide(oldDeRow.RSum, oldDeRow.price);//调整了price
    }
    let percentC = ObjectUtil.isEmpty(oldDeRow.CDSum) || oldDeRow.CDSum == 0 ?  0 : NumberUtil.divide(oldDeRow.CDSum, oldDeRow.baseJournalPrice);
    if(rootProject.pricingMethod === 1){
      percentC = ObjectUtil.isEmpty(oldDeRow.CSum) || oldDeRow.CSum == 0 ?  0 : NumberUtil.divide(oldDeRow.CSum, oldDeRow.price);
    }
    // 2.0  删除调整数据
    let deleteTZList = [];
    let rcjTZUnLocked = true;
    let originalQtyMap = new Map();
    for(let item of rcjs){
      if(item.materialCode=== DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ
        || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ
        || item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ){ //修改单价导致费用人材机删除
          originalQtyMap.set(item.materialCode,item);
          deleteTZList.push(item);
          //删除人材机
          let resourceKey = WildcardMap.generateKey(unitId, deRowId, item.sequenceNbr);
          this.ctx.resourceMap.removeByPattern(resourceKey);
          deleteRcj = true;
          if(item.isNumLock){
            rcjTZUnLocked = false;
          }
      }        
    }
    //3.0 再删除费用人材机信息
    for(let item of rcjs){
      if(item.isFyrcj == 0){ //修改单价导致费用人材机删除
          //删除人材机
          let resourceKey = WildcardMap.generateKey(unitId, deRowId, item.sequenceNbr);
          this.ctx.resourceMap.removeByPattern(resourceKey);
          deleteRcj = true      }        
    }
    //4.0 删除rcj 需要重新计算
    if(deleteRcj){
      //计算不包主材设备就调整材料的单价， 
      await DeCalculator.getInstance({constructId, unitId,deRowId},this.ctx).analyze();
      oldDeRow = deRow;
    }
    //5.0 计算差值
    let oldPrice = oldDeRow.baseJournalPrice;
    if(rootProject.pricingMethod === 1){
      oldPrice = oldDeRow.price;
    }

    let difference = NumberUtil.subtract(price, oldPrice);
    
    // let percentJ = ObjectUtil.isEmpty(oldDeRow.JSum) || oldDeRow.JSum == 0 ?  0 : oldDeRow.JSum / oldDeRow.price ;
    let percentJ = NumberUtil.subtract(1,NumberUtil.add(percentR,percentC));
    resQtyR = NumberUtil.numberScale(NumberUtil.multiply(percentR , difference),precision.DETAIL.RCJ.resQty);//调整不受位数影响
    resQtyC = NumberUtil.numberScale(NumberUtil.multiply(percentC , difference),precision.DETAIL.RCJ.resQty);
    resQtyJ = NumberUtil.numberScale(NumberUtil.multiply(percentJ , difference),precision.DETAIL.RCJ.resQty);
    //调整公共处理
    this._updatePriceCommon(this.resourceDomain,constructId, unitId,deRow,price,resQtyR,resQtyC,resQtyJ,originalQtyMap);
    
    //更新标识
    DeTypeCheckUtil.checkAndUpdateDeType(deRow, this.ctx);
    await this.notify(deRow, false);//单价修改不影响工程量

    let newSum = NumberUtil.addParams(deRow.RDSum,deRow.CDSum,deRow.JDSum);
    if(rootProject.pricingMethod === 1){
      newSum = NumberUtil.addParams(deRow.RSum,deRow.CSum,deRow.JSum);
    }
    //强行平差
    if(rcjTZUnLocked && newSum !== price){
      let newRcjs =  this.ctx.resourceMap.getValues(rcjDeKey);
      let jxTzRcj = newRcjs.find(item=>item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ);
      if(ObjectUtil.isEmpty(jxTzRcj)){
        jxTzRcj = newRcjs.find(item=>item.materialCode === DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ);
      }
      let newResQty = NumberUtil.add(jxTzRcj.resQty,NumberUtil.subtract(price,newSum));
      jxTzRcj.originalQty = newResQty;
      await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId, singleId:null, unitId
        , deId:deRow.sequenceNbr
        , rcjDetailId:jxTzRcj.sequenceNbr
        , constructRcj:{
          resQty :newResQty 
        }});
    }
    //通知费用汇总
    try{
       //包含了所有
       await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
      // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
      //   constructId: constructId,
      //   unitId: unitId,
      //   qfMajorType: deRow.qfCode
      // });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }

  _updatePriceCommonNew(constructId, unitId,deRow,price,resQtyR,resQtyC,resQtyJ){
    let {service} = EE.app;

    let {differenceResourceR, differenceResourceC, differenceResourceJ} = this.createDeTcResourceForConversion(constructId, unitId,deRow,resQtyR,resQtyC,resQtyJ)

    if(resQtyR != 0){
      service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, differenceResourceR, 'addMerge', null, null);
    }
    if(resQtyC != 0){
      service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, differenceResourceC, 'addMerge', null, null);
    }
    if(resQtyJ != 0){
      service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, differenceResourceJ, 'addMerge', null, null);
    }
    if(resQtyR === 0 && resQtyC === 0 && resQtyJ === 0){
      service.gongLiaoJiProject.gljConversionInfoService.updateRcjSyncDeConversionInfo(constructId, unitId, deRow.sequenceNbr, null, 'addMerge', null, null);
    }

    deRow.price = price;
    deRow.resourceTZ = CommonConstants.COMMON_YES;//有过单价 调整的标识
  }

  createDeTcResourceForConversion(constructId, unitId,deRow,resQtyR,resQtyC,resQtyJ) {
    let differenceResourceR = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_R);
    differenceResourceR.unit = "元";
    let differenceResourceC = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_C);
    differenceResourceC.unit = "元";
    let differenceResourceJ = new ResourceModel(constructId, unitId, Snowflake.nextId(), deRow.sequenceNbr, ResourceKindConstants.TYPE_J);
    differenceResourceJ.unit = "元";

    differenceResourceR.type = RcjTypeEnum['TYPE1'].desc;
    differenceResourceC.type = RcjTypeEnum['TYPE2'].desc;
    differenceResourceJ.type = RcjTypeEnum['TYPE3'].desc;
    differenceResourceR.kind = RcjTypeEnum['TYPE1'].code;
    differenceResourceC.kind = RcjTypeEnum['TYPE2'].code;
    differenceResourceJ.kind = RcjTypeEnum['TYPE3'].code;
    this.initResourceByUserDe(differenceResourceR, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_RG_TZ_LABEL, 1,resQtyR,deRow);
    this.initResourceByUserDe(differenceResourceC, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_CL_TZ_LABEL, 1,resQtyC,deRow);
    this.initResourceByUserDe(differenceResourceJ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ, DeCommonConstants.ADDTIONAL_MATERIAL_CODE_JX_TZ_LABEL, 1,resQtyJ,deRow);
    if(resQtyR != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceR);
    }
    if(resQtyC != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceC);
    }
    if(resQtyJ != 0){
      this.resourceDomain.createResource(unitId, deRow.sequenceNbr, differenceResourceJ);
    }

    return {
      differenceResourceR,
      differenceResourceC,
      differenceResourceJ
    }
  }

  async updateUnit(constructId, unitId, deRowId, unit)
  {

    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    try {

      let oUnit = UnitUtils.removeCharter(deRow.unit);
      if(ObjectUtil.isEmpty(oUnit))
      {
        oUnit = 1;
      }
      let nUnit = UnitUtils.removeCharter(unit);
      if(ObjectUtil.isEmpty(nUnit))
      {
        nUnit = 1;
      }

      deRow.unit = unit;
      let isChangeQuantity = false;
      if(oUnit != nUnit)
      {//当系数不一样时,需要计算
        isChangeQuantity = true;
      }
      //查询精度设置
      let precision = await service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
      let precisionObj = DeUtils.getPrecisionByDe(precision,deRow);
      if(isChangeQuantity){
        let coefficient = NumberUtil.numberScale(oUnit/nUnit,5);
        let parent = this.getDeById(deRow.parentId);
        let parentQuantity = parent.quantity;
        if(ObjectUtil.isEmpty(parentQuantity) || parentQuantity === 0)
        {
          //由工程量表达式反算工程量
          let priceCodes = await this.getQuantityExpressionCodes(constructId,unitId,this.functionDataMap);
          if(ObjectUtil.isEmpty(priceCodes)){
            priceCodes = [];
          }
          //增加定额费用代码
          await service.gongLiaoJiProject.gljDeService.addPriceCodes(constructId, unitId, deRowId, priceCodes);
          //这里无需trycatch，内部玩耍无错误，有问题请核查来源
          let resultQuantity = DeQualityUtils.evalQualityWithCodes(deRow.quantityExpression,priceCodes);
          
          if(ObjectUtil.isEmpty(resultQuantity)){
            resultQuantity = 0;
          }
          deRow.quantity =  NumberUtil.numberScale(resultQuantity / nUnit , precisionObj.quantity);
          //  parentQuantity = deRow.quantity;
          //  deRow.quantity = NumberUtil.numberScale(parentQuantity * 1 * coefficient , precisionObj.quantity);
        }
        else
        {
          //原始工程量及消耗量重新计算
          deRow.resQty = NumberUtil.numberScale((deRow.resQty * coefficient), 3);
          deRow.quantity = NumberUtil.numberScale((deRow.resQty * parentQuantity), precisionObj.quantity);
          //工程量表达式被覆盖
          deRow.quantityExpression = deRow.originalQuantity = NumberUtil.multiply(deRow.quantity, nUnit);
          
        }
        //修改
        await this.notify({ constructId, unitId, deRowId },true);
        //单位变化引起消耗量变换，消耗量变换必定一起父级单价变换，此时切换类型
        DeTypeCheckUtil.updateParentDeType(deRow,this.ctx);
      }

      //人材机定额单位变化引起编码变化
      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE
        ||deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE
      ){
        let rcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
        let rcjDetail = rcjs.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES).value;
        rcjDetail.unit = unit;
        await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId, singleId:null, unitId
          , deId:deRow.sequenceNbr
          , rcjDetailId:rcjDetail.sequenceNbr
          , constructRcj:{
            unit:rcjDetail.unit
        }});
      }
      //包含了所有
      await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: null,
        constructId: constructId
      });
      // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
      //   constructId: constructId,
      //   unitId: unitId,
      //   qfMajorType: deRow.qfCode
      // });
    } catch (error) {
      console.error("捕获到异常:", error);
    }
    return PropertyUtil.filterObjectProperties(deRow, BaseDomain.avoidProperty);

  }

  /**
   * 递归查询子节点 并可通过类型过滤 返回找到的row 数组.
   * @param deRow
   * @param childRowList
   * @param types
   */
  findChilds(deRow,childRowList,types)
  {
    if(ObjectUtil.isNotEmpty(deRow) && ObjectUtil.isNotEmpty(deRow.children))
    {
      for (let child of deRow.children)
      {
        if(types.includes(child.type))
        {
          childRowList.push(child);
          this.findChilds(child,childRowList,types);
        }
      }

    }
  }

  /**
   * 修改消耗量
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param resQty
   * @returns {Promise<*>}
   */
  async updateResQty(constructId, unitId, deRowId, resQty)
  {
    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if(ObjectUtil.isNotEmpty(deRow) && ObjectUtil.isNotEmpty(resQty) 
      && deRow.resQty !== resQty)
    {

      deRow.resQty = resQty;
      let parentDeRow = this.ctx.deMap.getNodeById(deRow.parentId);
      //对工料机无用，此处不改动，做标记
      if (ObjectUtil.isNotEmpty(parentDeRow) && ObjectUtil.isNotEmpty(parentDeRow.quantity) && parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST )
      {
        deRow.quantity = NumberUtil.multiply(parentDeRow.quantity, resQty);
      }
      await  this.notify({ constructId, unitId, deRowId:deRow.parentId },true);
      //消耗量变换影响父级标识
      if( ObjectUtil.isNotEmpty(parentDeRow) && parentDeRow.type === DeTypeConstants.DE_TYPE_DELIST){
        DeTypeCheckUtil.updateParentDeType(deRow,this.ctx);
      }
      try {
        // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        //   constructId: constructId,
        //   unitId: unitId,
        //   qfMajorType: deRow.qfCode
        // });

        //联动计算装饰超高人材机数量
        // await service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
        //联动计取安装费
        // await service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "delete");
        //包含了所有
        await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
          unitId: unitId,
          singleId: null,
          constructId: constructId
        });
      } catch (error) {
        console.error("捕获到异常:", error);
      }
    }
    else {
      throw Error("输入值不能为空");
    }
    return PropertyUtil.filterObjectProperties(deRow, BaseDomain.avoidProperty);
  }

  /**
   *
   * @param constructId
   * @param unitId
   * @param deRowId
   * @param quantity
   * @returns {Promise<{}>}
   */
  async updateQuantity(constructId, unitId, deRowId, quantity, changeResQty = true, filterTempRemoveRow = true, resetDeQuantities = true, priceCodes = [], quantityExpression = 'QDL', autoCostDe = true) {
    let {service} = EE.app;
    let deRow = this.ctx.deMap.getNodeById(deRowId);
    if (!ObjectUtil.isEmpty(deRow)) {
      let  unitNbr = UnitUtils.removeCharter(deRow.unit);
      if(ObjectUtil.isEmpty(priceCodes)){
        priceCodes = await this.getQuantityExpressionCodes(constructId,unitId,this.functionDataMap);
      }
      if(ObjectUtil.isEmpty(priceCodes)){
        priceCodes = [];
      }
      //增加定额费用代码
      await service.gongLiaoJiProject.gljDeService.addPriceCodes(constructId, unitId, deRowId, priceCodes);

      //这里无需trycatch，内部玩耍无错误，有问题请核查来源
      let resultQuantity = DeQualityUtils.evalQualityWithCodes(quantity,priceCodes);
      deRow.quantityExpression = deRow.originalQuantity  = quantity;//工料机等同，
      //查询精度设置
      let precision = await service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
      let precisionObj = DeUtils.getPrecisionByDe(precision,deRow);
      if(ObjectUtil.isNotEmpty(unitNbr))
      {
        // deRow.originalQuantity = resultQuantity;
        deRow.quantity = NumberUtil.numberScale(NumberUtil.divide(resultQuantity,unitNbr),precisionObj.quantity);
      }
      else {
        // deRow.originalQuantity = resultQuantity;
        deRow.quantity =  NumberUtil.numberFormat(resultQuantity,precisionObj.quantity);
      }
      //
      if(changeResQty){
        let parent = this.ctx.deMap.getNodeById(deRow.parentId);
        //父级工程量为0 子级也为0
        if( parent.quantity === 0 && parent.type === DeTypeConstants.DE_TYPE_DELIST){
          deRow.quantity = 0;
        }

        if (parent.quantity > 0 && parent.type !== DeTypeConstants.DE_TYPE_DEFAULT 
          && parent.type !== DeTypeConstants.DE_TYPE_FB 
          && parent.type !== DeTypeConstants.DE_TYPE_ZFB){
            deRow.resQty = NumberUtil.numberScale(NumberUtil.divide(deRow.quantity,parent.quantity),15);
            //消耗量保留3为后0,重置工程量为0
            if(deRow.resQty === 0){
               deRow.quantity = 0;
            }
        }
      }
      // deRow.quantityExpression = quantityExpression? quantityExpression : 'QDL';
      // if (ObjectUtils.isNotEmpty(deRow.children)) {
      //   for (let child of deRow.children) {
      //     child.quantityExpression = deRow.quantityExpression;
      //   }
      // }
      if(resetDeQuantities){
        await service.gongLiaoJiProject.gljInitDeService.initDeQuantities(deRow);
        // 获取定额下所有子级定额
        let childrens = await service.gongLiaoJiProject.gljDeService.getAllChildren(deRow);
        let des = []
        des.push(deRow)
        des = des.concat(childrens).filter(item => item.type === DeTypeConstants.DE_TYPE_DELIST || item.type === DeTypeConstants.DE_TYPE_DE)
        for (let de of des) {
          await service.gongLiaoJiProject.gljInitDeService.initDeQuantities(de);
        }
      }
      let rcjDeKey = WildcardMap.generateKey(unitId, deRow.sequenceNbr) + WildcardMap.WILDCARD;
      let rcjlist =  this.ctx.resourceMap.getValues(rcjDeKey);
      //人材机定额
      if([DeTypeConstants.DE_TYPE_RESOURCE,DeTypeConstants.DE_TYPE_USER_RESOURCE].includes(deRow.type)){
        
        let rcjDetail = rcjlist.find(item=>item.isDeResource === CommonConstants.COMMON_YES);        
        rcjDetail.totalNumber = deRow.quantity;
        await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId, singleId:null, unitId
          , deId:deRow.sequenceNbr
          , rcjDetailId:rcjDetail.sequenceNbr
          , deType: deRow.type
          , constructRcj:{
            totalNumber : rcjDetail.totalNumber
          }});
      }else{
        //处理锁定数量
        // for(let rcjDetail of rcjlist){
        //   rcjDetail.numLockNum = 0;//修改工程量导致原锁定数量失效，，，配合人材机数量取消锁定操作
        // }

        //处理锁定
        if(ObjectUtils.isEmpty(deRow.quantity) || deRow.quantity == 0){
            
        }else{
          //如果人材机存在数量锁定，先更新消耗量，
          let rcjDetails = rcjlist.filter(item=>item.isNumLock);
        
          for(let rcjDetail of rcjDetails){
          
            await service.gongLiaoJiProject.gljRcjService.updateRcjDetail({constructId, singleId:null, unitId
              , deId:deRow.sequenceNbr
              , rcjDetailId:rcjDetail.sequenceNbr
              , constructRcj:{
                resQty : NumberUtil.divide(rcjDetail.totalNumber,deRow.quantity)
              }});
          }
        }
      }

      try {
        await  this.notify({ constructId, unitId, deRowId },true,filterTempRemoveRow,priceCodes);
        //自动计算=各种记取+费用汇总通知
        if (autoCostDe) {
          await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
            unitId: unitId,
            singleId: null,
            constructId: constructId
          });
        } else {
          await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            unitId: unitId,
            qfMajorType: deRow.qfCode
          });
        }
        //联动计算装饰超高人材机数量
        // await service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
        // await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
        //   constructId: constructId,
        //   unitId: unitId,
        //   qfMajorType: deRow.qfCode
        // });

        //联动计取安装费
        // await service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(constructId, unitId, deRowId, "delete");
        
        // //人材机数量变化，联动计算三材量并同步其他费  todo 没有建设其他废了
        // await service.gongLiaoJiProject.gljRcjCollectService.updateOtherProjectScGJ(constructId);
      } catch (error) {
        console.error("捕获到异常:", error);
      }
      return PropertyUtil.filterObjectProperties(deRow, BaseDomain.avoidProperty);
    }
  }

  /**
   * 处理定额基本信息
   * @param baseDe
   * @param deModel
   *
   */
  async attachBaseDeProperty(baseDe, deModel) {
    PropertyUtil.copyProperties(baseDe, deModel, DeDomain.baseDeToDeAvoidProperty);
    if(baseDe.isExistDe === CommonConstants.COMMON_YES)
    {
      deModel.type = DeTypeConstants.DE_TYPE_DELIST;
      deModel.displayType = DeTypeConstants.DE_TYPE_DELIST_LABEL;
      deModel.standardId = baseDe.sequenceNbr;

    }
    else
    {
      deModel.type = DeTypeConstants.DE_TYPE_DE;
      deModel.displayType = DeTypeConstants.DE_TYPE_DE_LABEL;
      deModel.standardId = baseDe.sequenceNbr;
    }
    deModel.initResQty = baseDe.resQty;//初始含量
    // let parentDeRow = this.findFirstDeOrDeList(deModel);
    // if(ObjectUtil.isNotEmpty(parentDeRow))
    // {
    //   deModel.costFileCode = parentDeRow.costFileCode;
    //   deModel.costMajorName = parentDeRow.costMajorName;
    // }else{
      
    //   //非概算定额  直接显示单位的取费专业，，gsinitdeservice init 中处理，此处不要跳过
    //   if(baseDe.libraryName.indexOf('概算定额') === -1){
    //     deModel.costFileCode = null;
    //   }else{
    //     deModel.costFileCode = baseDe.libraryCode;
    //     deModel.costMajorName = baseDe.projectType;
    //   }
    // }
    //处理classiflevel1  专业   字段
    let classlevel01 = deModel.classlevel01?deModel.classlevel01.replace('工程','').replace('项目',''):'';
    deModel.classiflevel1 = this._removeChapterPrefix(classlevel01) +"-"+this._removeChapterPrefix(deModel.classlevel02);
    //处理  取费专业  
    await this.updateDe(deModel);
  }
  /**
   * 清单下追加补充定额
   * @param baseDe
   * @param deModel
   */
  async attachSubDe(baseDe, deModel) {
    let {service} = EE.app;
    if (!ObjectUtil.isEmpty(baseDe.subDeList)) {
      //constructId,unitId,sequenceNbr,parentId,type
      let childCodes = [];
      for (let subDe of baseDe.subDeList) {
        let subDeModel = new StandardDeModel(deModel.constructId, deModel.unitId, Snowflake.nextId(), deModel.sequenceNbr, DeTypeConstants.DE_TYPE_DE);
        PropertyUtil.copyProperties(subDe, subDeModel, DeDomain.baseDeToDeAvoidProperty);
        subDeModel.initResQty = subDe.resQty;//初始化消耗量
        subDeModel.standardId = subDe.sequenceNbr;//指向标准id
        subDeModel.rcjList = subDe.rcjList;//指向标准id
        await this.createDeRow(subDeModel);

        // subDe.costFileCode = deModel.costFileCode;
        // subDe.costMajorName = deModel.costMajorName;
        childCodes.push({code:subDeModel.deCode,sequenceNbr:subDeModel.sequenceNbr,resQty:subDeModel.resQty});

        await this.attachDeRCJ(this.resourceDomain,subDe.deRcjRelationList, subDeModel,subDe.rcjList);
      }
      deModel.initChildCodes = childCodes;
      // try {
      //   await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
      //     constructId: deModel.constructId,
      //     unitId: deModel.unitId,
      //     qfMajorType: deModel.qfCode
      //   });
      // } catch (error) {
      //   console.error("捕获到异常:", error);
      // }
    }
  }
  /**
   * 获得定额
   * @param predicate
   * @returns {*}
   */
  getDe(predicate) {
    return this.ctx.deMap.getAllNodes().find(predicate);
  }
  /**
   * 获得定额
   * @param predicate
   * @returns {*}
   */
  getDes(predicate) {
    return this.ctx.deMap.getAllNodes().filter(predicate);
  }
  /**
   *
   * @returns {*|any[]}
   */
  getDeTree(predicate) {
    return PropertyUtil.shallowCopyAndFilterProperties(this.ctx.deMap.getAllNodes().filter(predicate), BaseDomain.avoidProperty).map(DeDomain.filter4DeTree);
  }


  updateDeIsShowAnnotations(unitId, isShowAnnotations) {
    let allNodes1 = this.ctx.deMap.getAllNodes();
    let allNodes = allNodes1.filter(o => o.unitId === unitId);
    for (let item of allNodes) {
      item.isShowAnnotations = isShowAnnotations;
    }
  }


  /**
   * 深度获取某个节点下的所有子节点 孙节点一直到最末级
   * @param constructId
   * @param deRowId
   * @param unitId
   * @param types
   * @returns {*}
   */
  getDeTreeDepth(constructId,unitId,deRowId,types,posId)
  {
    let de =  ObjectUtil.isEmpty(deRowId) ? this.getRoot(unitId) : this.ctx.deMap.getNodeById(deRowId);
    let deList = [];
    this._getDeDepth(de,deList,types,posId);
    return PropertyUtil.shallowCopyAndFilterProperties(deList,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree);
  }


  /**
   * 深度获取某个节点下的所有子节点 孙节点一直到最末级
   * @param constructId
   * @param deRowId
   * @param unitId
   * @param types
   * @returns {*}
   */
  getDeAllTreeDepth(constructId,unitId,deRowId,types)
  {
    let de =  ObjectUtil.isEmpty(deRowId) ? this.getRoot(unitId) : this.ctx.deMap.getNodeById(deRowId);
    let deList = [];
    this._getDeAllDepth(de,deList,types);
    return PropertyUtil.shallowCopyAndFilterProperties(deList,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree);
  }
  getFbTreeDepth(constructId,unitId,deRowId,types){
    let de =  ObjectUtil.isEmpty(deRowId) ? this.getRoot(unitId) : this.ctx.deMap.getNodeById(deRowId);
    let deList = [];
    this._getFbDepth(de,deList,types);
    return PropertyUtil.shallowCopyAndFilterProperties(deList,BaseDomain.avoidProperty).map(DeDomain.filter4DeTree);
  }
  _getFbDepth(de,deList,types)
  {
    if(ObjectUtil.isNotEmpty(de)) {
      if(ObjectUtil.isEmpty(types) || types.includes(de.type))
      {
        deList.push(de);
      }
      // let deItem = this.ctx.deMap.getNodeById(de.sequenceNbr);
      if (de.children && de.children.length > 0)
      {
          for (let subDe of de.children) {
            this._getFbDepth(subDe, deList, types);
          }
      }
    }
  }
  /**
   * 内部方法 外部勿用
   * @param de
   * @param deList
   * @param types
   */
  _getDeDepth(de,deList,types,posId)
  {
    if(ObjectUtil.isNotEmpty(de)) {
      if(ObjectUtil.isEmpty(types) || types.includes(de.type))
      {
        deList.push(de);
        //保证数据显示主材或设备
        if(de.isExistedZcSb === CommonConstants.COMMON_YES &&(de.type === DeTypeConstants.DE_TYPE_DE 
          || de.type === DeTypeConstants.DE_TYPE_USER_DE
          || (de.type === DeTypeConstants.DE_TYPE_DELIST&&de.displaySign === BranchProjectDisplayConstant.open))){
            this._addZcSb2De(de,deList);
          }
      }
      let deItem = this.ctx.deMap.getNodeById(de.sequenceNbr);
      if (deItem.children.length)
      {
        if (posId){
         this._getAncestorIds(deItem, posId).map(de=>{
           de.displaySign = BranchProjectDisplayConstant.open
         })
        }
        if (de.displaySign === BranchProjectDisplayConstant.open) {
          let childrens = deItem.children;
          //排序
          childrens.sort((a,b)=>a.index-b.index);
          for (let subDe of childrens) {
            this._getDeDepth(subDe, deList, types);
          }
        }
      }
    }
  }
  _getAncestorIds(treeData, nodeId) {
    const ancestorNodes = [];

    function traverse(node, path) {
      if (node.sequenceNbr === nodeId) {
        ancestorNodes.push(...path);
        return true;
      }

      if (node.children) {
        for (const child of Object.values(node.children)) {
          const found = traverse(child, [...path, node]);
          if (found) return true;
        }
      }

      return false;
    }

    traverse(treeData, []);
    return ancestorNodes;
  }
  /**
   * 内部方法 外部勿用
   * @param de
   * @param deList
   * @param types
   */
  _getDeAllDepth(de,deList,types)
  {
    if(ObjectUtil.isNotEmpty(de)) {
      if(ObjectUtil.isEmpty(types) || types.includes(de.type))
      {
        deList.push(de);
        //保证数据显示主材或设备
        if(de.isExistedZcSb === CommonConstants.COMMON_YES &&(de.type === DeTypeConstants.DE_TYPE_DE
          || de.type === DeTypeConstants.DE_TYPE_USER_DE
          || de.type === DeTypeConstants.DE_TYPE_DELIST)){
            this._addZcSb2De(de,deList);
          }
      }
      let deItem = this.ctx.deMap.getNodeById(de.sequenceNbr);
      if (deItem.children.length)
      {
        for (let subDe of deItem.children) {
          this._getDeAllDepth(subDe, deList, types);
        }
      }
    }
  } 

  _getRcjTypeEnumDescByCode(rcjTypeEnumCode) {
    for (let enumKey in RcjTypeEnum) {
      if (RcjTypeEnum[enumKey].code == rcjTypeEnumCode) {
        return RcjTypeEnum[enumKey].desc;
      }
    }
    return null;
  }

  /**
   * 更新定额,单纯的更新等额数据，向上汇总
   * @param deModel
   */
  updateDeOnlyOwnData(deModel) {
    let de = this.ctx.deMap.getNodeById(deModel.sequenceNbr);
    if (ObjectUtil.isNotEmpty(de)) {
      de.updateValue(deModel);
    } else {
      throw Error('没有找到工程:' + deModel.sequenceNbr);
    }
  }
   
  removeRowRelatedDataById(relatedRowIds)
  {
    for (let rowId of relatedRowIds)
    {
      this.ctx.resourceMap.removeByValueProperty(DeDomain.FIELD_NAME_ROW_ID,rowId);
      this.ctx.deMap.removeNode(rowId);
    }
  }

  /**
   * 通过定额行ID删除下面所有对应的定额 人材机
   * @param deRow
   * @param relatedRowIds
   */
  findDeRows(deRow,relatedRowIds)
  {
    for (let subDeRow of deRow.children)
    {
      relatedRowIds.push(subDeRow.sequenceNbr);
      if(subDeRow.type === DeTypeConstants.DE_TYPE_DELIST || subDeRow.type === DeTypeConstants.DE_TYPE_FB
          || subDeRow.type === DeTypeConstants.DE_TYPE_ZFB)
      {
        this.findDeRows(subDeRow,relatedRowIds);
      }

    }
  }
  /**
   * 通过定额ID 获得定额实例
   * @param deId
   * @returns {*}
   */
  getDeById(deId) {
    return this.ctx.deMap.getNodeById(deId);
  }
  static getClassName() {
    return DomainConstants.CODE_DE_DOMAIN;
  }

  /**
   * 所有定额在各自分部下按排序规则重新排序，排序规则如下：
   * 1-按“当前专业子目→借用子目→自行补充子目”顺序排序；
   * 2-对当前专业子目按章节顺序排序；
   * 3-对相同子目按输入的先后顺序排序；
   * 4-对自行补充子目按编码序列排序。
   * 5-借用定额、人材机定额均视为借用子目。
   * 6-自行补充定额、安装记取定额均视为自行补充子目。
   * @param {*} unitId 
   */
  async sortDe(unitId, libraryCode){
    let rootDe = this.getRoot(unitId);
    if(ObjectUtil.isEmpty(rootDe)){
      return;
    }
    
    this._sortChildDe(rootDe,libraryCode);
  }

  _sortChildDe(rootDe,libraryCode){
    if(ObjectUtil.isNotEmpty(rootDe.children)&& rootDe.children.length > 0 && ![DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(rootDe.children[0].type)){
      let  curFeeChild = []; //当前取费专业下的子目
      let  curRcjChild = [];
      let  jieFeeChild = []; //其他取费专业子目
      let jieRcjChild = [];
      let  buFeeChild = []; //补充子目
      for(let child of rootDe.children){
        if(child.type === DeTypeConstants.DE_TYPE_RESOURCE){
          if(libraryCode === child.costFileCode){
            curRcjChild.push(child);
          }else{
            jieRcjChild.push(child);
          }
          continue;
        }
        //补充定额
        if([DeTypeConstants.DE_TYPE_USER_DE,DeTypeConstants.DE_TYPE_USER_RESOURCE,DeTypeConstants.DE_TYPE_ANZHUANG_FEE].includes(child.type)){
          buFeeChild.push(child);          
        }else{
           //非补充定额
          if(libraryCode === child.costFileCode){
            curFeeChild.push(child);
          }else{
            jieFeeChild.push(child);          
          }
        }
      }
      //按照章节排序
      curFeeChild.sort((a,b)=>{ 
        if(a.sortNo>b.sortNo){
          return 1;
        }
        if(a.sortNo<b.sortNo){
          return -1;
        }
        return 0;

      })
      //rcj按编码排序
      curRcjChild.sort((a,b)=>{
        if(a.deCode>b.deCode){
          return 1;
        }
        if(a.deCode<b.deCode){
          return -1;
        }
        return 0; 
      })
      jieRcjChild.sort((a,b)=>{
        if(a.deCode>b.deCode){
          return 1;
        }
        if(a.deCode<b.deCode){
          return -1;
        }
        return 0; 
      })
      // jieFeeChild  按输入顺序，  借用定额、人材机定额均视为借用子目
      jieFeeChild.sort((a,b)=>{
        
        if(a.sortNo>b.sortNo){
          return 1;
        }
        if(a.sortNo<b.sortNo){
          return -1;
        }
        return 0; 
      })
      //buFeeChild  按照编码排序
      buFeeChild.sort((a,b)=>{
        if(a.deCode>b.deCode){
          return 1;
        }
        if(a.deCode<b.deCode){
          return -1;
        }
        return 0;        
      })

      let i = 0;
      for(let child of curFeeChild){
        child.index = i;
        i++;
      }
      for(let child of curRcjChild){
        child.index = i;
        i++;
      }
      for(let child of jieFeeChild){
        child.index = i;
        i++;
      }
      for(let child of jieRcjChild){
        child.index = i;
        i++;
      }
      for(let child of buFeeChild){
        child.index = i;
        i++;
      }
      return;
    }

    if(ObjectUtil.isNotEmpty(rootDe.children)&&rootDe.children.length > 0 && [DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(rootDe.type)){
      for(let child of rootDe.children){
          this._sortChildDe(child,libraryCode);
      }
    }
  }
  /**
   * 无限制，只找满足types的父级
   * @param {*} deRow 
   * @param {*} parentList 
   * @param {*} types 
   */
  newFindParents(deRow,parentList,types)
  {
    if(ObjectUtil.isNotEmpty(deRow.parentId) ) {
       
      let parentRow = this.getDeById(deRow.parentId);

      if (ObjectUtil.isNotEmpty(parentRow) && types.includes(parentRow.type))
      {
        parentList.push(parentRow);
        this.newFindParents(parentRow,parentList,types);
      }
    }
  }
  /**
   * parentRow.type === DeTypeConstants.DE_TYPE_FB || parentRow.type === DeTypeConstants.DE_TYPE_ZFB
   *           || parentRow.type === DeTypeConstants.DE_TYPE_DEFAULT
   * 找到当前定额上层的分部或者子分部,直到返回默认的顶级Row
   * @returns {*}
   * @param deRow
   * @param parentList
   * @param types
   */
  findParents(deRow,parentList,types)
  {
    if(ObjectUtil.isNotEmpty(deRow.parentId) ) {
      if(deRow.type === DeTypeConstants.DE_TYPE_DEFAULT)
      {
        parentList.push(deRow);
        return;
      }

      let parentRow = this.getDeById(deRow.parentId);

      if (ObjectUtil.isNotEmpty(parentRow) && types.includes(parentRow.type))
      {
        parentList.push(parentRow);
        this.findParents(parentRow,parentList,types);
      }
    }
  }
  /**
     * 临时删除定额
     * @param deRowId
     */
  async tempRemoveDeRow(deRowId, isFrontCall=false) {
    try {
      let {service} = EE.app;

      let deRow = this.ctx.deMap.getNodeById(deRowId);
      //不可对整个项目及分部子分部临时删除
      if(ObjectUtils.isEmpty(deRow) || deRow.isTempRemove === CommonConstants.COMMON_YES
        || deRow.type === DeTypeConstants.DE_TYPE_DEFAULT 
        || deRow.type === DeTypeConstants.DE_TYPE_FB
        || deRow.type === DeTypeConstants.DE_TYPE_ZFB ){
        return;
      }
      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE 
        || deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
          deRow.price = 0;
      }
      
      if(deRow.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE && ObjectUtil.isEmpty(deRow.originalQuantity)){
        let  unitNbr = UnitUtils.removeCharter(deRow.unit);
        if(ObjectUtil.isEmpty(unitNbr)){
          unitNbr = 1;
        }
        //不限制位数
        // deRow.originalQuantity = NumberUtil.numberScale(deRow.quantity * unitNbr,5);
        deRow.quantityExpression = deRow.originalQuantity = NumberUtil.multiply(deRow.quantity, unitNbr);
      }

      //标识删除
      deRow.isTempRemove = CommonConstants.COMMON_YES;
      if(isFrontCall){
        deRow.isFirstTempRemove = CommonConstants.COMMON_YES;
      }
      //记录工程量表达式
      deRow.changeQuantity = deRow.quantityExpression;
      //获得子项，并标记
      let childs = [];
      let allRcjs = [];
      let rcjs =  this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
      if(ObjectUtils.isNotEmpty(rcjs)){
        allRcjs = allRcjs.concat(rcjs);
      }
      this.findDeRows(deRow, childs);
      for(let childId of childs){
        //子定额设置为删除
        let childDeRow = this.ctx.deMap.getNodeById(childId);
        childDeRow.isFirstTempRemove = CommonConstants.COMMON_NO;
        
        //子定额已经临时删除了不再次删除
        if(childDeRow.isTempRemove === CommonConstants.COMMON_YES){
          continue;
        }
        childDeRow.isTempRemove = CommonConstants.COMMON_YES;
        childDeRow.changeQuantity = childDeRow.quantityExpression;

        let childRcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,childDeRow.sequenceNbr);
        if(ObjectUtils.isNotEmpty(childRcjs)){
          allRcjs = allRcjs.concat(childRcjs);
        }
        
      }
      //人材机数据
      for(let rcj of allRcjs) {
        //rcj
        if( rcj['value'].isTempRemove == CommonConstants.COMMON_YES){
          rcj['value'].isFirstTempRemove = CommonConstants.COMMON_NO;//重置
          continue;
        }
        rcj['value'].changeResQty = rcj['value'].resQty;
        rcj['value'].resQty = 0;
        rcj['value'].isTempRemove = CommonConstants.COMMON_YES;
        //pbs处理
        if(ObjectUtils.isNotEmpty(rcj['value'].pbs)){
          rcj['value'].pbs.forEach(pbsItem => {
            // pbsItem.changeResQty = pbsItem.resQty;
            // pbsItem.resQty = 0;
            pbsItem.isTempRemove = CommonConstants.COMMON_YES;
            pbsItem.isFirstTempRemove = CommonConstants.COMMON_NO;//重置
          });
        }
      };
      //更新该定额工程量0
      await this.updateQuantity(deRow.constructId,deRow.unitId,deRow.sequenceNbr,0,false,false,false);
     
      // let deRowBack = ConvertUtil.deepCopy(deRow);
      // 删除定额后，其他相关操作
      // 工程量明细，标准换算  都不需要处理
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }

  /**
     * 取消临时删除定额
     * @param deRowId
     */
  async cancelTempRemoveDeRow(deRowId) {
    try {
      let {service} = EE.app;

      let deRow = this.ctx.deMap.getNodeById(deRowId);
      //父级为临时删除的项不可取消临时删除
      let parentRow = this.ctx.deMap.getNodeById(deRow.parentId);
      //父级为临时删除不能取消临时删除
      if(parentRow.isTempRemove === CommonConstants.COMMON_YES 
        || deRow.isTempRemove === CommonConstants.COMMON_NO){
        return ;
      }
      //所有人材机
      let allRcjs = [];
      //
      let rcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
      if(ObjectUtils.isNotEmpty(rcjs)){
        allRcjs = allRcjs.concat(rcjs);
      }
      
      if(deRow.type === DeTypeConstants.DE_TYPE_RESOURCE || 
        deRow.type === DeTypeConstants.DE_TYPE_USER_RESOURCE){
          let rcjSelf = allRcjs.find(item=>item.value.isDeResource === CommonConstants.COMMON_YES)
          if(ObjectUtils.isNotEmpty(rcjSelf)){
            deRow.price =rcjSelf.value.price; 
          }
          //此处工料机无用，做标识，如果运行到if内证明需求已改
          let parentDe = this.ctx.deMap.getNodeById(deRow.parentId);
          if(parentDe.type === DeTypeConstants.DE_TYPE_DELIST){
            deRow.changeQuantity = deRow.resQty*parentDe.quantity;
          }
      }
      //标识删除恢复
      deRow.isTempRemove = CommonConstants.COMMON_NO;
      //获得子项，并标记
      let childs = [];
      this.findDeRows(deRow, childs);
      for(let child of childs){
        //子定额设置为删除
        let childDeRow = this.ctx.deMap.getNodeById(child);
        childDeRow.isTempRemove = CommonConstants.COMMON_NO;
        childDeRow.quantity = childDeRow.changeQuantity;
        //人材机数据
        let childRcjs = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,childDeRow.sequenceNbr);
        if(ObjectUtils.isNotEmpty(childRcjs)){
          allRcjs = allRcjs.concat(childRcjs);
        }
      }
      //人材机数据
      
      allRcjs.forEach(rcj  => {
        //rcj
        rcj['value'].resQty = rcj['value'].changeResQty;
        rcj['value'].isTempRemove = CommonConstants.COMMON_NO;
        if(ObjectUtils.isNotEmpty(rcj['value'].pbs)){
          rcj['value'].pbs.forEach(pbsItem => {
            // pbsItem.resQty = pbsItem.changeResQty;
            pbsItem.isTempRemove = CommonConstants.COMMON_NO;
          })
        }
      });
      //更新该定额工程量0   恢复
      await this.updateQuantity(deRow.constructId,deRow.unitId,deRow.sequenceNbr,deRow.changeQuantity,true,false,false);
     
      // let deRowBack = ConvertUtil.deepCopy(deRow);
      // 删除定额后，其他相关操作
      // 工程量明细，标准换算  都不需要处理
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  /**
   * 批量删除临时删除数据
   * @param deRowId
   */
  async realTempRemoveDeRow(constructId,unitProjects) {
    try {
      let {service} = EE.app;
      let relatedDeDeRowIds = [];
      let relatedRcjDetails = [];
      unitProjects.forEach(unitProject => {
        let rooDeRow = this.getRoot(unitProject.sequenceNbr);
        if(rooDeRow.isTempRemove === CommonConstants.COMMON_YES){
          relatedDeDeRowIds.push(rooDeRow.sequenceNbr);
        }
        this.findUnTempRemoveDeRows(rooDeRow, relatedDeDeRowIds,relatedRcjDetails);
      })
      return {de: relatedDeDeRowIds, rcj: relatedRcjDetails};
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  /**
   * 查找所有临时删除的定额数据
   * @param deRow
   * @param relatedRowIds
   */
  findUnTempRemoveDeRows(deRow,relatedRowIds, relatedRcjDetails)
  {
    //定额不是临删，但是人材机是临删的，需要处理
    if(deRow.isTempRemove === CommonConstants.COMMON_NO){
      let rcjLists = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,deRow.sequenceNbr);
      //需要对二次解析材料明细处理吗, 二次解析的配比，需要单独处理吗？
      for(let rcjDetailMap of rcjLists){
        let rcjDetail = rcjDetailMap.value;
        if(rcjDetail.isTempRemove === CommonConstants.COMMON_YES){
          relatedRcjDetails.push({sequenceNbr:rcjDetail.sequenceNbr,deRowId:rcjDetail.deRowId,unitId:deRow.unitId});
        }else{
          //有配比，要考虑配比
          if(ObjectUtils.isNotEmpty(rcjDetail.pbs)){
            rcjDetail.pbs.forEach(pbs => {
              if(pbs.isTempRemove === CommonConstants.COMMON_YES){
                relatedRcjDetails.push({sequenceNbr:pbs.sequenceNbr,deRowId:rcjDetail.deRowId,unitId:deRow.unitId});
              }});
          }
        }
      }
    }
    for (let subDeRow of deRow.children)
    {
      //找到临时删除项即可返回，不深挖
      if(subDeRow.isTempRemove === CommonConstants.COMMON_YES){
        relatedRowIds.push(subDeRow.sequenceNbr);
      }else{
        this.findUnTempRemoveDeRows(subDeRow,relatedRowIds,relatedRcjDetails);
      }
    }
  }

  async batchCancelTempRemoveDeRow(constructId,unitProjects) {
    try{
      let {service} = EE.app;
      let relatedDeDeRowIds = [];
      let relatedRcjDetails = [];
      unitProjects.forEach(unitProject => {
        let rooDeRow = this.getRoot(unitProject.sequenceNbr);
        if(rooDeRow.isTempRemove === CommonConstants.COMMON_YES){
          relatedDeDeRowIds.push(rooDeRow.sequenceNbr);
        }
        this.findUnTempRemoveDeRows(rooDeRow, relatedDeDeRowIds,relatedRcjDetails);
      })
      
      return {de: relatedDeDeRowIds, rcj: relatedRcjDetails};
    } catch (error) {
      console.error("捕获到异常:", error);
    }
  }
  /**
   * 粘贴定额
   * @param {*} constructId 
   * @param {*} unitId 
   * @param {*} oUnitId 
   * @param {*} idList 
   * @param {*} index 
   */
  async pasteDe(unitId,oUnitId,idList,prevDeRow, type,clearResqty = true){
    if(ObjectUtils.isEmpty(type)){
      type = "";
    }
    let {service} = EE.app;
    let preParentId = prevDeRow.parentId;
    let index = prevDeRow.index + 1;
    if(type === "child" || type === "childNoCircle"){
      preParentId = prevDeRow.sequenceNbr;
      if(prevDeRow.children &&  prevDeRow.children.length > 0){
        index = prevDeRow.children.length;
      }else{
        index = 0;
        prevDeRow.displaySign = BranchProjectDisplayConstant.open;
      }
    }
    let pasteDeList = [];
    let pasteRcjList = [];
    let pasteUserRcjList = [];
    let fbList = [DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB];
    let deList = [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_DE,DeTypeConstants.DE_TYPE_RESOURCE
      ,DeTypeConstants.DE_TYPE_ANZHUANG_FEE,DeTypeConstants.DE_TYPE_USER_DE,DeTypeConstants.DE_TYPE_ZHUANSHI_FEE
      ,DeTypeConstants.DE_TYPE_USER_RESOURCE,DeTypeConstants.DE_TYPE_EMPTY];
    let curTypeList = null;

    let parentNode = this.ctx.deMap.getNodeById(preParentId);
    if(parentNode&&parentNode.children && parentNode.children.length > 0){
      let newDeRow = parentNode.children[0];
      if(newDeRow.type === DeTypeConstants.DE_TYPE_FB || newDeRow.type === DeTypeConstants.DE_TYPE_ZFB){
        curTypeList = fbList;
      }else{
        curTypeList = deList;
      }
    }else if(parentNode && parentNode.type === DeTypeConstants.DE_TYPE_DELIST){
      curTypeList = deList;
    }
    
    let unitProject =  null;
    let oUnitProject =  null;

    //查询单位下的工程量明细数据
    let quantitiesMap = await this.functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
    let newDeIdQuantitiesMap = new Map();
    //查询单位下的标准换算数据
    let conversionMap = await this.functionDataMap.get(FunctionTypeConstants.UNIT_CONVERSION);
    let newDeIdConversionMap = new Map();
    //查询单位下的定额费用代码
    let deCostCode = await this.functionDataMap.get(FunctionTypeConstants.DE_COST_CODE);
    let newDeCostCode = {};
    let notifyQuantityMap = new Set();
    let rcjUserList = await this.functionDataMap.get(FunctionTypeConstants.PROJECT_USER_RCJ);
    for(let id of idList){
      let deRow = this.getDeById(id);
      if(!deRow){
        continue;
      }
      oUnitId = deRow.unitId;
      let parentList = [];
      this.findParents(deRow, parentList, [DeTypeConstants.DE_TYPE_DELIST,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB]);
      let idExists = idList.find(itemSequenceNbr=>{
        let parent = parentList.find(parentItem=>parentItem.sequenceNbr === itemSequenceNbr);
        if(parent){
          return true;
        }
        return false;
      });
      if(idExists){
        continue;
      }
      // 复制定额
      let newDeRow = ConvertUtil.deepCopy(deRow);
      // pasteDeList.push(newDeRow);      
      if(ObjectUtils.isEmpty(curTypeList)){
        if(newDeRow.type === DeTypeConstants.DE_TYPE_FB || newDeRow.type === DeTypeConstants.DE_TYPE_ZFB){
          curTypeList = fbList;
        }else{
          curTypeList = deList;
        }
      }else{
        if(curTypeList.indexOf(newDeRow.type) === -1){
          continue;
        }
      }
      
      //重置id
      this._resetDeRowId(newDeRow,unitId,preParentId,pasteDeList,pasteRcjList,pasteUserRcjList,type,prevDeRow.sequenceNbr,notifyQuantityMap,conversionMap,newDeIdConversionMap,quantitiesMap,newDeIdQuantitiesMap,deCostCode,newDeCostCode,rcjUserList);
      
      if(unitProject == null){
        unitProject =  this.ctx.treeProject.getNodeById(unitId);
      }
      if(oUnitProject == null){
        oUnitProject =  this.ctx.treeProject.getNodeById(oUnitId);
      }      
    }

    if(ObjectUtils.isNotEmpty(pasteDeList)){
      let libraryCodeSet = new Set();//设置libraryCoee;
      let clearQuantity = false;
      if(parentNode.type === DeTypeConstants.DE_TYPE_DELIST){
        clearQuantity = true;
      }
      //无需notify及通知
      let emptyDes = [];
      //处理定额粘贴
      for(let pasteDeRow of pasteDeList){
        if(pasteDeRow.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE){
          continue;
        }
        //跨单位复制的用户定额，需要增加到新的单位中用户定额缓存中
        // if(pasteDeRow.type ===  DeTypeConstants.DE_TYPE_USER_DE && unitId !== oUnitId){
        //   this.add2UserDeBase(pasteDeRow.constructId,pasteDeRow);
        // }
        if(pasteDeRow.type === DeTypeConstants.DE_TYPE_EMPTY){
          emptyDes.push(pasteDeRow);
        }else{
          libraryCodeSet.add(pasteDeRow.qfCode);
        }

        if(clearQuantity){
          pasteDeRow.quantity = 0;
          pasteDeRow.originalQuantity = 0;
          if(clearResqty){
            pasteDeRow.resQty = 0;
          }
          pasteDeRow.costFileCode = parentNode.costFileCode;
          pasteDeRow.costMajorName = parentNode.costMajorName;
        }
        pasteDeRow.children = [];//清空子级
        let parentDeRow = parentNode;
        if(pasteDeRow.parentId == parentNode.sequenceNbr){
          //维护定额map
          this._addNode2DeMap(pasteDeRow, parentNode,index);
          index += 1;
        }else{
          let newParentNode = pasteDeList.find(item=>item.sequenceNbr === pasteDeRow.parentId);
          parentDeRow = newParentNode;
           //维护定额map
           this._addNode2DeMap(pasteDeRow, newParentNode);
        }
        if(pasteDeRow.isTempRemove === CommonConstants.COMMON_YES){
          pasteDeRow.changeQuantity = 0;
        }
        if(parentDeRow.isTempRemove === CommonConstants.COMMON_YES&& pasteDeRow.isTempRemove !== CommonConstants.COMMON_YES){
          pasteDeRow.isTempRemove = CommonConstants.COMMON_YES;
          // pasteDeRow.changeQuantity = pasteDeRow.originalQuantity;
          pasteDeRow.changeQuantity = 0;
          pasteDeRow.originalQuantity = 0;
          pasteDeRow.quantity = 0;
        }
        if(parentDeRow.isTempRemove === CommonConstants.COMMON_YES
          && pasteDeRow.isTempRemove === CommonConstants.COMMON_YES
          && pasteDeRow.isFirstTempRemove === CommonConstants.COMMON_YES){
          pasteDeRow.isFirstTempRemove = CommonConstants.COMMON_NO;
        }
        if(parentDeRow.isTempRemove !== CommonConstants.COMMON_YES
          && pasteDeRow.isTempRemove === CommonConstants.COMMON_YES
          && pasteDeRow.isFirstTempRemove !== CommonConstants.COMMON_YES){
          pasteDeRow.isFirstTempRemove = CommonConstants.COMMON_YES;
        }

        if([DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(parentDeRow.type)){
          pasteDeRow.resQty = null;
        }
        
        if([DeTypeConstants.DE_TYPE_DEFAULT,DeTypeConstants.DE_TYPE_FB,DeTypeConstants.DE_TYPE_ZFB].includes(pasteDeRow.type)){
          pasteDeRow.resQty = null;        
          pasteDeRow.quantity = null;
          pasteDeRow.originalQuantity = null;
        }else{          
          if(!clearQuantity && notifyQuantityMap.has(pasteDeRow.sequenceNbr)){
            this._addNotifyQuantityMap(pasteDeRow);
          }
        }

        pasteDeRow.initDeRcjNameList = [];  //重置人材机初始化信息
      }
      //处理人材机粘贴
      let specialRcjList = [];
      for(let pasteRcj of pasteRcjList){
        let newParentNode = pasteDeList.find(item=>item.sequenceNbr === pasteRcj.deRowId);
        if(newParentNode.type === DeTypeConstants.DE_TYPE_ANZHUANG_FEE){
          continue;
        }
        if(newParentNode.isTempRemove === CommonConstants.COMMON_YES && pasteRcj.isTempRemove !== CommonConstants.COMMON_YES){
          pasteRcj.isTempRemove = CommonConstants.COMMON_YES;
          pasteRcj.changeResQty = pasteRcj.resQty;
          pasteRcj.resQty = 0;
        }
        this.resourceDomain.createResource(newParentNode.unitId,pasteRcj.deRowId, pasteRcj);

        //重新计算定额的人材机初始化信息
        let rcjNameObj = {};
        rcjNameObj.sequenceNbr = pasteRcj.sequenceNbr;
        rcjNameObj.initMaterialName = pasteRcj.materialName;
        rcjNameObj.code = pasteRcj.materialCode;
        rcjNameObj.kind = pasteRcj.kind;
        let initDeRcjNameList1 = newParentNode.initDeRcjNameList;
        if (ObjectUtil.isNotEmpty(initDeRcjNameList1)) {
          initDeRcjNameList1.push(rcjNameObj);
          newParentNode.initDeRcjNameList = initDeRcjNameList1;
        } else {
          let initDeRcjNameList2 = [];
          initDeRcjNameList2.push(rcjNameObj);
          newParentNode.initDeRcjNameList = initDeRcjNameList2;
        }
        if(pasteRcj.materialCode.indexOf('#')>-1){
          specialRcjList.push(pasteRcj);
        }
      }
      //处理用户人材机
      for(let pasteUserRcj of pasteUserRcjList){
        rcjUserList.push(ConvertUtil.deepCopy(pasteUserRcj));
      }
      //处理标准换算粘贴
      if(ObjectUtil.isNotEmpty(newDeIdConversionMap)){
        let unitConversionMap2 = conversionMap.get(unitId);
        if(ObjectUtil.isEmpty(unitConversionMap2)){
          unitConversionMap2 = new Map();
          conversionMap.set(unitId,unitConversionMap2);
        }
        for(let [key,value] of newDeIdConversionMap){
          value.deRowId = key;
          value.sequenceNbr = key;
          value.deId = key;
          value.unitId = unitId;
          unitConversionMap2.set(key,value);
        }
      }
      //处理工程量明细粘贴
      if(ObjectUtil.isNotEmpty(newDeIdQuantitiesMap)){
        let unitQuantitiesMap2 = quantitiesMap.get(unitId);
        if(ObjectUtil.isEmpty(unitQuantitiesMap2)){
          unitQuantitiesMap2 = new Map();
          quantitiesMap.set(unitId,unitQuantitiesMap2);
        }
        for(let [key,value] of newDeIdQuantitiesMap){
          value.deRowId = key;
          value.sequenceNbr = key;
          value.deId = key;
          value.unitId = unitId;
          unitQuantitiesMap2.set(key,value);
        }
      }
      //处理定额费用代码粘贴
      if(ObjectUtil.isNotEmpty(newDeCostCode)){
        deCostCode[unitId][newDeCostCode.deId] = newDeCostCode
      }

      //处理内存RCJ编码编码
      let objMap = this.functionDataMap.get(FunctionTypeConstants.RCJ_MEMORY);
      let unitMemory = objMap.get(FunctionTypeConstants.UNIT_RCJ_MEMORY + parentNode.constructId+ FunctionTypeConstants.SEPARATOR + unitId );
      for(let sRcjItem of specialRcjList){
        if(ObjectUtils.isEmpty(unitMemory)){
          unitMemory = new Array();
          unitMemory.push(sRcjItem);
        }else{
          let sRcjExist = unitMemory.find(item=>item.materialCode == sRcjItem.materialCode);
          if(ObjectUtils.isEmpty(sRcjExist)){
            unitMemory.push(ConvertUtil.deepCopy(sRcjItem));
          }
        }
        objMap.set(FunctionTypeConstants.UNIT_RCJ_MEMORY + parentNode.constructId+ FunctionTypeConstants.SEPARATOR + unitId , unitMemory);
      }
      
          
      // 处理计算引擎数据变化
      if(!(emptyDes.length > 0 && pasteRcjList.length ===0 && emptyDes.length === pasteDeList.length)){
        this.notify(parentNode,clearResqty?clearQuantity:!clearResqty);
      }
      //处理定额的类型变化
      for(let pasteDe of pasteDeList){
        DeTypeCheckUtil.checkAndUpdateDeType(pasteDe,this.ctx);
        //同步取费文件
        await service.gongLiaoJiProject.gljFreeRateService.updateDeFeeMajorAddFeeRate(pasteDe.constructId, unitId, pasteDe.libraryCode,pasteDe.costFileCode);
      }
      try {
        if(!(emptyDes.length > 0 && pasteRcjList.length ===0 && emptyDes.length === pasteDeList.length)){
          //联动计算装饰超高人材机数量
          // await service.gongLiaoJiProject.gljDeService.calculateZSFee(prevDeRow.constructId, prevDeRow.unitId, true);
          //联动计取安装费
          // await service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAlone(prevDeRow.constructId, prevDeRow.unitId, prevDeRow.deRowId, "delete");
          //包含了所有记取+费用汇总
          await service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
            unitId: unitId,
            singleId: null,
            constructId: prevDeRow.constructId
          });
        }else{          
          //费用汇总变化
          for(let libraryCode of libraryCodeSet){
            await service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice({
              constructId: prevDeRow.constructId,
              unitId: prevDeRow.unitId,
              qfMajorType: libraryCode
            });
    
          }
        }
      } catch (error) {
        console.error("捕获到异常:", error);
      }
    }
  }

  /**
   * 补充定额主材和设备 以前存在则同步
   * @param unitId
   * @param rcj
   */
  zsRcjExtend(unitId, rcj) {
    // 修改名称
    let rcjDeKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
    let rcjList = this.resourceDomain.getResource(rcjDeKey);
    let arcj = rcjList.find(item => item.materialName === rcj.materialName &&
        item.unit === rcj.unit &&
        item.type === rcj.type &&
        item.specification === rcj.specification &&
        item.materialCode === rcj.materialCode
    );
    if (arcj) {
      rcj.taxRate = arcj.taxRate;
      rcj.marketPrice = arcj.marketPrice;
      rcj.marketTaxPrice = arcj.marketTaxPrice;
      rcj.baseJournalPrice = arcj.baseJournalPrice;
      rcj.baseJournalTaxPrice = arcj.baseJournalTaxPrice;

      rcj.sourcePrice = arcj.sourcePrice; // 价格来源
      rcj.transferFactor = arcj.transferFactor; // 三材系数
      rcj.kindSc = arcj.kindSc; // 三材种类

      rcj.ifDonorMaterial = arcj.ifDonorMaterial; // 供应方式
      rcj.donorMaterialPrice = arcj.donorMaterialPrice; // 甲供价格
      rcj.donorMaterialNumber = arcj.donorMaterialNumber; // 甲供数量

      rcj.ifProvisionalEstimate = arcj.ifProvisionalEstimate; // 是否暂估
      rcj.ifLockStandardPrice = arcj.ifLockStandardPrice; // 锁定市场价

      rcj.producer = arcj.producer; // 产地
      rcj.manufactor = arcj.manufactor; // 厂家
      rcj.brand = arcj.brand; // 品牌
      rcj.deliveryLocation = arcj.deliveryLocation; // 送达地点
      rcj.qualityGrade = arcj.qualityGrade; // 质量等级

      rcj.remark = arcj.remark; // 备注
    }
  }

  _checkPasteDeHasMap(deRow){
    let deGclMap = this.functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
    if(ObjectUtil.isEmpty(deGclMap)){
        return false;
    }
    
    let deSet = deGclMap.get(deRow.unitId);
    if(ObjectUtil.isEmpty(deSet)){
        return false;
    }
    let deExist = deSet.find(item=>item.unitId === deRow.unitId && item.deRowId === deRow.sequenceNbr);
    if(ObjectUtil.isNotEmpty(deExist)){
      return true;              
    }
    return false;
  }

  _addNotifyQuantityMap(addDeRow){
    let deGclMap = this.functionDataMap.get(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY);
    if(ObjectUtil.isEmpty(deGclMap)){
        deGclMap = new Map();
        this.functionDataMap.set(FunctionTypeConstants.YSH_GCL_EXP_NOTIFY,deGclMap);
    }
    let deRowBak = {constructId:addDeRow.constructId, unitId:addDeRow.unitId, deRowId:addDeRow.sequenceNbr};
    let deSet = deGclMap.get(addDeRow.unitId);
    if(ObjectUtil.isEmpty(deSet)){
        deSet = [];
        deGclMap.set(addDeRow.unitId,deSet);
    }
    let deExist = deSet.find(item=>item.unitId === addDeRow.unitId && item.deRowId === addDeRow.sequenceNbr);
    if(ObjectUtil.isNotEmpty(deExist)){
        let index = deSet.indexOf(deExist);
        deSet.splice(index,1);
    }
    deSet.push(deRowBak);
  }

  _addNode2DeMap(deRow, parentNode,index){
    if(index){
      this.ctx.deMap.addNodeAt(deRow, parentNode,index);
    }else{
      this.ctx.deMap.addNode(deRow, parentNode);
    }
  }
  /**
   * 粘贴定额重置id
   * @param {*} newDeRow 旧的定额副本
   * @param {*} unitId 新的单位
   * @param {*} parentId 新的父级
   * @param {*} pasteDeList 要赋值的定额
   * @param {*} pasteRcjList 要复制的人材机
   * @param {*} pasteUserRcjList 要复制的用户人材机
   * @param {*} type
   * @param {*} topPreParentId 
   * @param {*} notifyQuantityMap shifou 
   * @param {*} conversionMap   标准换算
   * @param {*} newDeIdConversionMap   标准换算
   * @param {*} quantitiesMap   工程量明细
   * @param {*} newDeIdQuantitiesMap   工程量明细
   * @param {*} deCostCode   定额费用代码
   * @param {*} newDeCostCode   定额费用代码
   * @param {*} rcjUserList   用户人材机
   * @returns
   */
  _resetDeRowId(newDeRow,unitId,parentId,pasteDeList,pasteRcjList,pasteUserRcjList, type,topPreParentId,notifyQuantityMap,conversionMap,newDeIdConversionMap,quantitiesMap,newDeIdQuantitiesMap,deCostCode,newDeCostCode,rcjUserList){
    if(type === "childNoCircle" && topPreParentId === newDeRow.sequenceNbr){ //防止循环嵌套
      return;
    }
    let rcjList = this.ctx.resourceMap.findByValueProperty(DeDomain.FIELD_NAME_ROW_ID,newDeRow.sequenceNbr);
    let newDeId = Snowflake.nextId();
    let oUnitId = newDeRow.unitId;//旧的单位id，不要变化啊
    let oDeId = newDeRow.sequenceNbr;
    //先判断是否存在
    if(this._checkPasteDeHasMap(newDeRow,this.functionDataMap)){
      notifyQuantityMap.add(newDeId);
    }
    newDeRow.sequenceNbr = newDeId;
    newDeRow.unitId = unitId;
    newDeRow.parentId = parentId;
    newDeRow.deRowId = newDeRow.sequenceNbr;///此处人材机查询使用作为sequenceNbr
    //处理标准换算
    if(newDeRow.type === DeTypeConstants.DE_TYPE_DELIST || newDeRow.type === DeTypeConstants.DE_TYPE_DE){
      let unitConversionMap = conversionMap.get(oUnitId);
      if(ObjectUtil.isNotEmpty(unitConversionMap)){
        let deConversion = unitConversionMap?.get(oDeId);
        if(ObjectUtil.isNotEmpty(deConversion)){
          let copyedConversion = ConvertUtil.deepCopy(deConversion);
          newDeIdConversionMap.set(newDeRow.sequenceNbr,copyedConversion);
        }
      }
    }
    //处理工程量明细
    if (ObjectUtils.isNotEmpty(quantitiesMap)) {
      let unitQuantitiesMap = quantitiesMap.get(oUnitId);
      if(ObjectUtil.isNotEmpty(unitQuantitiesMap)){
        let deQuantities = unitQuantitiesMap?.get(oDeId);
        if(ObjectUtil.isNotEmpty(deQuantities)){
          let copyedQuantities = ConvertUtil.deepCopy(deQuantities);
          copyedQuantities.quotaListId = newDeRow.sequenceNbr;
          newDeIdQuantitiesMap.set(newDeRow.sequenceNbr,copyedQuantities);
        }
      }
    }
    //定额费用代码
    if (ObjectUtil.isNotEmpty(deCostCode)) {
      if (ObjectUtil.isNotEmpty(deCostCode[unitId])) {
        if (ObjectUtil.isNotEmpty(deCostCode[unitId][oDeId])) {
          Object.assign(newDeCostCode, ConvertUtil.deepCopy(deCostCode[unitId][oDeId]));
          newDeCostCode.deId = newDeRow.sequenceNbr
        }
      }
    }
    let oldInitChildCodes = newDeRow.initChildCodes;
    let needRcjInitCodes = [];
    for(let rcj of rcjList){
      let oldSequenceNbr = rcj.value.sequenceNbr;
      let copyRcj =  ConvertUtil.deepCopy(rcj.value);
      this._resetRcjId(copyRcj,newDeRow.sequenceNbr);
      copyRcj.unitId = unitId;
      pasteRcjList.push(copyRcj);
      if(ObjectUtils.isNotEmpty(oldInitChildCodes)){
        let oldRcj = oldInitChildCodes.find(oImte=>oImte.sequenceNbr === oldSequenceNbr);
        if(ObjectUtils.isNotEmpty(oldRcj)){
          needRcjInitCodes.push(copyRcj);
        }
      }

      if (rcj.value.supplementRcjFlag === RcjCommonConstants.SUPPLEMENT_RCJ_FLAG) {
        let userRcj = rcjUserList.find(item => item.sequenceNbr === rcj.value.sequenceNbr);
        if (userRcj) {
          let copyUserRcj =  ConvertUtil.deepCopy(userRcj);
          copyUserRcj.sequenceNbr = copyRcj.sequenceNbr;
          copyUserRcj.deRowId = copyRcj.deRowId;
          copyUserRcj.unitId = copyRcj.unitId;
          copyUserRcj.deId = copyRcj.deRowId;
          copyUserRcj.parentId = copyRcj.deRowId;
          pasteUserRcjList.push(copyUserRcj);
        }
      }
    }
    //处理定额的initCodes
    let initChildCodes = [];
    if(ObjectUtils.isNotEmpty(needRcjInitCodes)){
      needRcjInitCodes.forEach(rcjItem=>{
        let originalCode = rcjItem.materialCode;
        if(ObjectUtils.isNotEmpty(rcjItem.editFromConversion)){
          originalCode = rcjItem.editFromConversion.fromRCJCode;
        }
        initChildCodes.push({code:originalCode,sequenceNbr:rcjItem.sequenceNbr,resQty:rcjItem.originalQty})
      })
    }
    
    pasteDeList.push(newDeRow);
    if(ObjectUtil.isNotEmpty(newDeRow.children)){
      for (let child of newDeRow.children)
      {
        this._resetDeRowId(child,unitId,newDeRow.sequenceNbr,pasteDeList,pasteRcjList,pasteUserRcjList,type,topPreParentId,notifyQuantityMap,conversionMap,newDeIdConversionMap,quantitiesMap,newDeIdQuantitiesMap,deCostCode,newDeCostCode,rcjUserList)
        initChildCodes.push({code:child.deCode,sequenceNbr:child.sequenceNbr,resQty:child.originalQty});
      }
      
    }    
    newDeRow.initChildCodes = initChildCodes;
  }
  _resetRcjId(rcj,deRowId){
    
      rcj.sequenceNbr = Snowflake.nextId();
      rcj.deRowId = deRowId;
      rcj.deId = deRowId;
      rcj.parentId = deRowId;
      if(ObjectUtil.isNotEmpty(rcj.pbs)){
        for (let pbs of rcj.pbs)
        {
          pbs.sequenceNbr = Snowflake.nextId();
          pbs.parentId = rcj.sequenceNbr;
        }
      }
     
  }
}

DeDomain.toString = () => 'DeDomain';
module.exports = DeDomain;
