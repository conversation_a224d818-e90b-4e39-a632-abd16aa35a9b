<!--
 * @Descripttion: 单价构成
 * @Author: liuxia
 * @Date: 2023-05-29 14:22:33
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-02-18 14:12:42
-->
<template>
  <div class="prive-comTab-content" ref="clickInsideContent">
    <div
      class="head"
      v-if="
        props.currentInfo?.kind === '04' &&
        (props.currentInfo?.bdCode || props.currentInfo?.fxCode) &&
        !props.currentInfo.tempDeleteFlag &&
        props.currentInfo?.isCostDe !== 1
      "
    >
      <span v-for="item in btnList" :key="item.key">
        <a-button
          type="text"
          @click="btnClickFn(item.key)"
          :disabled="item.disabled"
        >
          <icon-font :type="item.icon"></icon-font>
          <span class="text">
            {{ item.name }}
          </span>
        </a-button>
      </span>
      <!-- <a-button type="text" @click="addDjgcData"
        ><icon-font type="icon-cs-zairumoban"></icon-font>载入模板</a-button
      >
      <a-button type="text" @click="deleteData"
        ><icon-font type="icon-cs-baocun"></icon-font>保存模板</a-button
      >
      <a-button type="text" @click="addDjgcData"
        ><icon-font type="icon-biaodan-charu"></icon-font>插入</a-button
      >
      <a-button type="text" @click="deleteData"
        ><icon-font type="icon-biaodan-shanchu"></icon-font>删除</a-button
      >
      <a-button type="text" @click="addDjgcData"
        ><icon-font type="icon-biaodan-charu"></icon-font>保存修改</a-button
      >
      <a-button type="text" @click="deleteData"
        ><icon-font type="icon-biaodan-shanchu"></icon-font>放弃修改</a-button
      > -->
    </div>
    <div class="content">
      <vxe-table
        ref="priceCompositionTable"
        :data="vexTableData"
        :column-config="{ resizable: true }"
        class="table-scrollbar table-edit-common"
        :cell-class-name="cellClassName"
        :edit-config="{
          trigger: 'click',
          mode: 'cell',
          enabled: currentInfo?.kind === '04' && !currentInfo.tempDeleteFlag,
          beforeEditMethod({ row, column }) {
            console.log(row);
            if (
              !['rate', 'caculateBase'].includes(column.field) &&
              row.isLock
            ) {
              //规费，安文费两行的除费率和计算基数外的字段不可修改
              return false;
            }
            if (isJieSuanOriginalFlag) return false;
            if (
              store.type === 'jieSuan' &&
              ['规费', '安全文明施工费'].includes(row.type)
            )
              return false;
            return true;
          },
        }"
        @cell-click="
          cellData => {
            useCellClickEvent(cellData, tableCellClick, ['sequenceNbr']);
          }
        "
        @cell-menu="menuClick"
        :menu-config="menuConfig"
        @menu-click="contextMenuClickEvent"
        @edit-closed="editClosedEvent"
        height="auto"
        :row-config="{ isCurrent: true, keyField: 'sequenceNbr' }"
        @current-change="currentChangeEvent"
        keep-source
        @keydown="vxeTableKeydown"
      >
        <vxe-column type="seq" :width="columnWidth(60)" title="行号">
        </vxe-column>
        <vxe-column
          field="sort"
          title="序号"
          :width="columnWidth(60)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model.trim="row.sort"
              type="text"
              @blur="clear()"
              @keyup="row.sort = sortAndlength(row.sort, 10)"
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="code"
          :width="columnWidth(120)"
          title="费用代号"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model.trim="row.code"
              type="text"
              @blur="clear()"
              @keyup="row.code = row.code.replace(/[^\w_]/g, '')"
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="name"
          :min-width="columnWidth(120)"
          title="名称"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <span>{{ row.name }}</span>
          </template>
          <template #edit="{ row }">
            <cell-textarea
              :clearable="false"
              v-model.trim="row.name"
              @blur="clear()"
              placeholder="请输入名称"
              :textHeight="row.height"
              @keyup="row.name = row.name.replace(/\-|\+|\*|\/|\.|\(|\)/g, '')"
            ></cell-textarea>
          </template>
        </vxe-column>
        <vxe-column
          title="计算基数"
          :min-width="columnWidth(150)"
          field="caculateBase"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            {{ row.caculateBase }}
          </template>
          <template #edit="{ row }">
            <vxeTableEditTable
              tableType="DJGC"
              @showTable="showTable"
              :filedValue="row.caculateBase"
              :propInfo="props.currentInfo"
              @update:filedValue="
                newValue => {
                  saveCustomInput(newValue, row, 'caculateBase', $rowIndex);
                }
              "
            ></vxeTableEditTable>
          </template>
        </vxe-column>

        <vxe-column
          title="基数说明"
          :min-width="columnWidth(200)"
          field="desc"
        ></vxe-column>
        <vxe-column
          field="rate"
          :width="columnWidth(120)"
          title="费率（%）"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model.trim="row.rate"
              :maxlength="10"
              type="text"
              @blur="(row.rate = pureNumber(row.rate, 4)), clear()"
            ></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          title="单价"
          :width="columnWidth(80)"
          field="displayUnitPrice"
        >
          <template #header v-if="store.type === 'jieSuan'">
            结算单价
          </template>
        </vxe-column>
        <vxe-column
          title="合价"
          :width="columnWidth(80)"
          field="displayAllPrice"
        >
          <template #header v-if="store.type === 'jieSuan'">
            结算合价
          </template>
        </vxe-column>
        <vxe-column
          title="费用类别"
          field="type"
          :width="columnWidth(100)"
          :edit-render="{}"
        >
          <template #edit="{ row }">
            <vxe-select v-model="row.type" transfer ref="showPanel">
              <vxe-option
                v-for="item in typeList"
                :key="item.value"
                :value="item.label"
                :label="item.label"
              ></vxe-option>
            </vxe-select>
          </template>
        </vxe-column>
        <vxe-column
          title="备注"
          :width="columnWidth(100)"
          field="remark"
        ></vxe-column>
      </vxe-table>
    </div>
  </div>
  <!-- <common-modal
    className="dialog-comm confirmation-dialog"
    title="确认"
    width="520"
    v-model:modelValue="confirmationStatus"
    @close="closeApplication(false)"
    :mask="true"
  >
    <div class="content">
      <p class="tips">
        当前【{{ confirmationTitle }}】单价构成文件发生变化，要保存修改吗？
      </p>
      <div class="label-list">
        <span class="title"> 应用范围</span>
        <a-select
          ref="select"
          v-model:value="modalVal.scopeValue"
          style="width: 60%"
        >
          <a-select-option value="useDe">应用至当前定额</a-select-option>
          <a-select-option value="usePart">应用至当前分部</a-select-option>
          <a-select-option value="useUnit">应用至当前单位</a-select-option>
          <a-select-option value="useProject"
            >应用至当前工程项目</a-select-option
          >
        </a-select>
      </div>
    </div>
    <span class="confirmation-btns">
      <a-button @click="closeApplication(false)">取消</a-button>
      <a-button
        type="primary"
        style="margin-left: 50px"
        @click="saveApplication()"
        >确定</a-button
      >
    </span>
  </common-modal> -->
  <common-modal
    className="dialog-comm confirmation-dialog"
    title="保存修改"
    width="550"
    height="400"
    v-model:modelValue="confirmationStatus"
    @close="closeApplication(false)"
    :mask="true"
  >
    <div class="centerCon">
      <div class="leftContent">
        <p class="title">
          <icon-font type="icon-yingyongfanwei" class="iconFont"></icon-font
          >应用范围
        </p>
        <a-radio-group
          v-model:value="modalVal.scopeValue"
          @change="scopeValChange($event)"
        >
          <template v-for="item in scopeValueList">
            <a-radio
              v-if="jieSuanScopeShow(item)"
              :style="radioStyle"
              :value="item.value"
              >{{ item.name }}</a-radio
            >
          </template>
        </a-radio-group>
      </div>
      <p class="line" v-if="!store.currentTreeInfo?.originalFlag"></p>
      <div class="rightContent" v-if="!store.currentTreeInfo?.originalFlag">
        <p class="title">
          <icon-font type="icon-gonghuofangshi" class="iconFont"></icon-font
          >应用规则
        </p>
        <a-radio-group
          v-model:value="modalVal.supplyWay"
          @change="supplyWayValChange($event)"
          :disabled="modalVal.scopeValue === 'useDe'"
        >
          <a-radio
            v-for="item in supplyWayList"
            :style="radioStyle"
            :value="item.value"
            >{{ item.name }}</a-radio
          >
        </a-radio-group>
      </div>
    </div>
    <p class="footer">
      <span
        ><icon-font type="icon-querenshanchu" class="iconFont"></icon-font
        >选择应用范围后默认关联对应的应用规则</span
      >
      <span class="btnList">
        <a-button @click="closeApplication(false)">取消</a-button>
        <a-button type="primary" @click="saveApplication()">确定</a-button>
      </span>
    </p>
  </common-modal>
  <djgc-mould
    ref="zjMouldRef"
    @onSuccess="onSuccessZj"
    :saveData="saveApiData"
  ></djgc-mould>
</template>

<script setup>
import {
  ref,
  onMounted,
  onUnmounted,
  computed,
  onBeforeUnmount,
  onDeactivated,
  reactive,
  watch,
  nextTick,
} from 'vue';
import { useCellClick } from '@/hooks/useCellClick';
import { onClickOutside } from '@vueuse/core';
import { globalData, settGlobalData } from './status.js';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const lodash = require('lodash');
const props = defineProps(['tableData', 'RefreshList', 'currentInfo', 'type']);
import { pureNumber, sortAndlength } from '@/utils/index';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
import feePro from '@/api/feePro';
import infoMode from '@/plugins/infoMode.js';
import { columnWidth } from '@/hooks/useSystemConfig';
import { message } from 'ant-design-vue';
import { useVxeTableKeyDown } from '@/hooks/useVxeTableKeyDown';
const { vxeTableKeydown } = useVxeTableKeyDown();
let comModel = ref(false); //计算基数编写弹框
let textValue = ref('');
let oldValue = ref('');
const priceCompositionTable = ref(null);
const clickInsideContent = ref(null);
const comArea = ref();
let isCurrent = ref(false);
let editCaculateBase = ref(false);
let vexTableData = ref([]);
let oldTableData = ref([]); //表格旧数据，不设置应用后的数据
const editLockList = reactive(['UPC_GF', 'UPC_AWF']); //初始化模板只有这两行类别对应字段有编辑限制
let useSetDataSeq = ref(); //应用设置的范围选中的定额
let currentRow = ref(null); //表格选中数据
let newGFandWF = reactive([]); //修改安文费和规费后会勾选useDe和usePart会弹框
const initNewGFandWF = () => {
  newGFandWF = [];
};
const jieSuanScopeShow = item => {
  if (store.type === 'jieSuan' && store.currentTreeInfo?.originalFlag) {
    return item.value === 'useDe';
  }
  return true;
};
let saveApiData = ref({}); //载入模板应用成功接口
onMounted(() => {
  initFirst();
  getTypeList();
  isCurrent.value = true;
});
watch(
  () => props.tableData,
  () => {
    initFirst();
  }
);
watch(
  () => props.currentInfo,
  (newVal, oldVal) => {
    currentRow.value = null;
    if (confirmationStatus.value) {
      if (!oldVal?.sequenceNbr) return;
      useSetDataSeq.value = oldVal.sequenceNbr;
    } else {
      if (!newVal?.sequenceNbr) return;
      useSetDataSeq.value = newVal.sequenceNbr;
    }
    jieSuanBtnDisbled();
  }
);
watch(
  () => globalData.isEditStatus,
  () => {
    let save = btnList.find(i => i.key === 'saveUpdate');
    let ababdan = btnList.find(i => i.key === 'abandon');
    save.disabled = !globalData.isEditStatus;
    ababdan.disabled = !globalData.isEditStatus;
    // saveTemp.disabled = !globalData.isEditStatus;
  }
);
const radioStyle = reactive({
  display: 'flex',
  margin: '7px',
  fontSize: '13px',
  whiteBreak: 'normal',
  wordWrap: 'break-word',
});
let typeList = reactive([]);
const getTypeList = () => {
  //获取费用类别下拉
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  feePro.upcTypesDJGC(apiData).then(res => {
    console.log('upcTypesDJGC', res, apiData);
    if (res.status === 200) {
      typeList = res.result;
    } else {
      typeList = [];
    }
  });
};
let btnList = reactive([
  {
    name: '载入模板',
    key: 'bringModal',
    icon: 'icon-cs-zairumoban',
    disabled: false,
  },
  {
    name: '保存模板',
    key: 'saveModal',
    icon: 'icon-cs-baocun',
    disabled: false,
  },
  {
    name: '插入',
    icon: 'icon-cs-charu',
    key: 'insert',
    disabled: false,
  },
  {
    name: '删除',
    icon: 'icon-cs-shanchu',
    key: 'delete',
    disabled: false,
  },
  {
    name: '保存修改',
    icon: 'icon-cs-baocun',
    key: 'saveUpdate',
    disabled: true,
  },
  {
    name: '放弃修改',
    icon: 'icon-cs-shanchu',
    key: 'abandon',
    disabled: true,
  },
]); //表头按钮功能
const btnClickFn = key => {
  console.log('btnClickFn-key', key);
  switch (key) {
    case 'bringModal':
      openZRMB();
      break;
    case 'saveModal':
      saveTempOperate();
      break;
    case 'insert':
      update('ADD', priceCompositionTable.value.getCurrentRecord());
      break;
    case 'delete':
      update('DELETE', priceCompositionTable.value.getCurrentRecord());
      break;
    case 'saveUpdate':
      // clickOutside();
      confirmationStatus.value = true;
      break;
    case 'abandon':
      closeApplication(false);
      break;
    case 'up':
      // 上移
      upOrDown(1);
      break;
    case 'down':
      // 下移
      upOrDown(2);
      break;
  }
  // getTableData(); //重新获取列表数据
};
const saveTempOperate = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    deId: props.currentInfo.sequenceNbr,
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
    constructName: store.currentTreeGroupInfo?.name,
  };
  console.log('保存模板呢', apiData);
  feePro.saveUPCtemplateDJGC(apiData).then(res => {
    console.log('保存模板返回呢', res, apiData);
    if (res.status === 200) {
      res.result.indexOf('取消') !== -1
        ? message.info(res.result)
        : message.success(res.result);
    } else {
      message.success(res.message ? 'res.message' : '保存模板失败');
    }
  });
};
const upOrDown = type => {
  // type:1-上移  0-下移
  let list = [...vexTableData.value];
  let tarIdx = list.findIndex(
    i => i.sequenceNbr === currentRow.value.sequenceNbr
  );
  let otherIdx = type === 1 ? tarIdx - 1 : tarIdx + 1;
  let other = list[otherIdx];
  //单位之间直接上下移动
  list[otherIdx] = list[tarIdx];
  list[tarIdx] = other;
  vexTableData.value = [...list];
  message.success('操作成功');
  let apiType = type === 1 ? 'UP' : 'DOWN';
  update(apiType, currentRow.value);
};
const currentChangeEvent = ({ row }) => {
  currentRow.value = row;
};
const setDelBtnDisbled = () => {
  let delBtn = btnList.find(i => i.key === 'delete');
  let delBtn2 = menuConfig.body.options[0].find(i => i.code === 'delete');
  if (vexTableData.value?.length > 1) {
    delBtn.disabled = false;
    delBtn2.disabled = false;
    delBtn2.className = 'redFont';
  } else {
    delBtn.disabled = true;
    delBtn2.disabled = true;
    delBtn2.className = '';
  }
  jieSuanBtnDisbled();
};
const isJieSuanOriginalFlag = computed(() => {
  return (
    store.type === 'jieSuan' &&
    store.currentTreeInfo?.originalFlag &&
    props.currentInfo.originalFlag
  );
});
const jieSuanBtnDisbled = () => {
  // 合同内的原始数据禁止
  btnList.forEach(item => {
    if (['bringModal', 'insert', 'delete', 'saveModal'].includes(item.key)) {
      item.disabled = isJieSuanOriginalFlag.value;
    }
  });
};
const initFirst = () => {
  console.log('单价构成数据-------------initFirst', props.tableData);
  if (!props.tableData || props.tableData.length === 0) {
    vexTableData.value = [];
    return;
  }
  menuConfig.body.disabled =
    props.currentInfo?.kind !== '04'
      ? true
      : props.currentInfo.tempDeleteFlag
      ? true
      : false;
  if (isJieSuanOriginalFlag.value) {
    // 合同内的原始数据禁止
    menuConfig.body.disabled = true;
  }
  dispposeData(props.tableData, true);
  vexTableData.value = handleList(props.tableData);
  setDelBtnDisbled();
  oldTableData.value =
    props.tableData && JSON.parse(JSON.stringify(props.tableData));
  nextTick(() => {
    if (vexTableData.value?.length === 0 || !vexTableData.value) return;
    priceCompositionTable.value.setCurrentRow(
      currentRow.value ? currentRow.value : vexTableData.value[0]
    );
  });
};
const dispposeData = (data, setData = false) => {
  if (!data) return;
  data.forEach(i => {
    i.oldName = i.name;
    if (setData && editLockList.includes(i.typeCode)) {
      // setData---为true设置规费，安文费旧数据
      newGFandWF.push({
        seq: i.sequenceNbr,
        oldRate: i.rate,
        newRate: i.rate,
      });
    }
  });
};
const showTable = bol => {
  //正在编辑计算基数弹框情况下不弹出设置应用范围
  editCaculateBase.value = bol;
};

const handleList = data => {
  return data?.map(
    ({
      name,
      displayUnitPrice,
      displayAllPrice,
      isLock,
      remark,
      ...otherData
    }) => {
      let cusData = {};
      remark = isLock ? '不计入综合单价' : '';
      if (props.currentInfo?.kind == '04') {
        cusData = {
          name,
          displayUnitPrice,
          displayAllPrice,
          remark,
          ...otherData,
        };
      } else {
        cusData = {
          name,
          remark,
          displayUnitPrice,
          displayAllPrice,
        };
      }

      return { ...cusData };
    }
  );
};

const confirmationTitle = computed(() => {
  return props.currentInfo.bdName.length > 15
    ? props.currentInfo.bdName.substr(0, 15) + '...'
    : props.currentInfo.bdName;
});
// 载入模板相关
let zjMouldRef = ref(null);
let flag = ref(false);
const onSuccessZj = (type = null) => {
  if (type === 'close') {
    openZRMBM.value = false;
    return;
  }
  flag.value = zjMouldRef.value.getIsReset();
  openZRMBM.value = false;
  zjMouldRef.value.close();
  if (flag.value) {
    globalData.isEditStatus = false;
    initNewGFandWF();
  } else {
    globalData.isEditStatus = true;
  }
  console.log(
    'onSuccessZj---zjMouldRef.value.getIsReset()',
    flag.value,
    globalData.isEditStatus,
    openZRMBM.value
  );
  refreshData();
  // getTableData();
};
//右键载入模板
let menuConfig = reactive({
  className: 'my-menus',
  body: {
    disabled: true,
    options: [
      [
        {
          name: '载入模板',
          code: 'bringModal',
          disabled: false,
          // visible: false,
        },
        {
          name: '保存模板',
          code: 'saveModal',
          disabled: true,
          visible: false,
        },
        {
          name: '插入',
          code: 'insert',
          disabled: false,
        },
        {
          name: '上移',
          code: 'up',
          disabled: false,
        },
        {
          name: '下移',
          code: 'down',
          disabled: false,
        },
        {
          name: '删除',
          code: 'delete',
          disabled: false,
          className: 'redFont',
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    console.log('visibleMethod', row, rowIndex, props.tableData.length);
    currentRow.value = row;
    let upTar = options[0].find(i => i.code === 'up');
    let downTar = options[0].find(i => i.code === 'down');
    setDelBtnDisbled();
    if (vexTableData.value?.length > 1) {
      if (rowIndex === 0) {
        upTar.disabled = true;
        downTar.disabled = false;
      } else if (rowIndex + 1 === vexTableData.value.length) {
        downTar.disabled = true;
        upTar.disabled = false;
      } else {
        downTar.disabled = false;
        upTar.disabled = false;
      }
    } else {
      downTar.disabled = true;
      upTar.disabled = true;
    }

    priceCompositionTable.value.setCurrentRow(currentRow.value);
    return true;
  },
});
const contextMenuClickEvent = ({ menu, row }) => {
  if (props.currentInfo?.kind !== '04') return;
  console.log('menu, row', menu, row);
  confirmationStatus.value = false;
  globalData.isEditStatus = false;
  btnClickFn(menu.code);
};
let openZRMBM = ref(false);
const openZRMB = () => {
  saveApiData.value = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    deId: props.currentInfo.sequenceNbr,
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
  };
  openZRMBM.value = true;
  zjMouldRef.value.open('djgc');
};
//双击单元格-蓝框
const cellClassName = ({ $columnIndex, row, column }) => {
  if (props.currentInfo?.kind !== '04') return;
  const selectName = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'index') {
    return 'index-bg ' + selectName;
  }
  return selectName;
};
// const editCalc = row => {
//   comModel.value = true;
//   textValue.value = {
//     ...row,
//     calculateFormula: row.caculateBase,
//   };
//   oldValue.value = row.caculateBase;
//   globalData.isEditStatus = true;
// };

//编辑计算基数弹框确认
const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};

// 更新列表数据
const getTableData = () => {
  props.RefreshList(true);
};

const clear = () => {
  //清除编辑状态
  const $table = priceCompositionTable.value;
  $table.clearEdit();
};
const editClosedEvent = ({ row, column }) => {
  console.log('单价构成修改', row);
  const $table = priceCompositionTable.value;
  const field = column.field;
  let value = row[field];
  const reg = /[^\d\.]/g;
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field === 'caculateBase' && value?.length === 0) {
    message.warn(`计算基数不可为空`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'rate' && (Number(value) > 100 || Number(value) < 0)) {
    //数值超过限制还原
    message.warn(`可输入数值范围：0-100`);
    $table.revertData(row, field);
    return;
  }
  if (field === 'code') {
    let tar = vexTableData.value.find(
      i =>
        i.code.toLowerCase() === value.toLowerCase() &&
        i.sequenceNbr !== row.sequenceNbr
    );
    if (tar) {
      message.error(`当前费用代码已被使用`);
      $table.revertData(row, field);
      return;
    }
  }
  settGlobalData(props.currentInfo.sequenceNbr, props.type);
  //修改接口
  update('UPDATE', row, field);
};
const getIsUpdate = () => {
  const arr1 = JSON.parse(JSON.stringify(oldTableData.value));
  vexTableData.value.forEach(i => {
    delete i['height'];
    i.rate = i.rate + '';
  });
  const arr2 = JSON.parse(JSON.stringify(vexTableData.value));
  const flag = lodash.isEqual(arr1, arr2); //true-有修改  false-没有修改
  globalData.isEditStatus = !flag;
  settGlobalData(props.currentInfo.sequenceNbr, props.type);
  console.log('getIsUpdate', globalData.isEditStatus, arr1, arr2);
  // return !flag;
};
const update = async (type, row, field = null) => {
  if (!row) {
    message.info('请选择一条数据进行操作');
    return;
  }
  if (field === 'type') {
    let tar = typeList.find(i => i.label === row[field]);
    field = 'typeCode';
    row[field] = tar.value;
  }
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    deId: props.currentInfo.sequenceNbr,
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
    optionData: {
      option: type, //add delete up down
      sequenceNbr: row.sequenceNbr,
      column: field,
      value: field ? row[field] : null,
    },
  };
  // if (!priceCompositionTable.value.isUpdateByRow(row, field)) return;
  if (editLockList.includes(row.typeCode)) {
    let tar = newGFandWF.find(i => i.seq === row.sequenceNbr);
    if (tar) {
      tar.newRate = row.rate;
    } else {
      newGFandWF.push({
        seq: row.sequenceNbr,
        oldRate: row.rate,
        newRate: row.rate,
      });
    }
  }
  useSetDataSeq.value = props.currentInfo.sequenceNbr;
  let res = await feePro.cellEditorDJGC(apiData);
  console.log('单价构成编辑保存', apiData, res);
  if (res.status === 200) {
    // 刷新页面数据--更新状态
    if (!['UP', 'DOWN'].includes(type)) {
      await refreshData(row.sequenceNbr, type);
    }
    message.success('操作成功');
    getIsUpdate();
  } else {
    priceCompositionTable.value.revertData(row, field);
    message.error(res.message);
  }
};
const refreshData = async (seq = null, type = null) => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    deId: props.currentInfo.sequenceNbr,
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
  };
  await feePro.queryforDeIdDJGC(apiData).then(res => {
    console.log('refreshData', res, apiData);
    if (res.status === 200) {
      dispposeData(res.result);
      vexTableData.value = handleList(res.result);
      if (type === 'ADD') {
        let index;
        vexTableData.value.map((i, idx) => {
          if (i.sequenceNbr === seq) {
            index = idx + 1;
          }
        });
        currentRow.value = vexTableData.value[index];
      } else if (type === 'DELETE') {
        currentRow.value = vexTableData.value[0];
      } else {
        if (vexTableData.value.length === 0) {
          vexTableData.value = handleList(props.tableData);
        }
        seq
          ? (currentRow.value = vexTableData.value.find(
              i => i.sequenceNbr === seq
            ))
          : (currentRow.value = vexTableData.value[0]);
      }
      nextTick(() => {
        priceCompositionTable.value.setCurrentRow(currentRow.value);
      });
      let saveTemp = btnList.find(i => i.key === 'saveUpdate');
      console.log('eeeeeeeeeeeeeeee', zjMouldRef.value.getIsEdit());
      if (!zjMouldRef.value.getIsEdit()) {
        globalData.isEditStatus = false;
        settGlobalData(props.currentInfo.sequenceNbr, props.type);
      } else {
        globalData.isEditStatus = true;
        settGlobalData(props.currentInfo.sequenceNbr, props.type);
      }
      setDelBtnDisbled();
    } else {
      vexTableData.value = [];
      setDelBtnDisbled();
    }
  });
};
let modalVal = reactive({
  scopeValue: 'useDe',
  supplyWay: null,
});
const supplyWayList = [
  {
    name: '刷新应用范围下名称一致的【单价构成文件】',
    value: 'name',
  },
  {
    name: '刷新应用范围下前缀一致的【单价构成文件】',
    value: 'prefix',
  },
  {
    name: '刷新应用范围下所有【单价构成文件】',
    value: 'all',
  },
];
let scopeValueList = reactive([
  {
    name: '应用至当前定额',
    value: 'useDe',
    selectSupplyVal: null,
  },
  {
    name: '应用至当前分部',
    value: 'usePart',
    selectSupplyVal: 'all',
  },
  {
    name: '应用至当前单位',
    value: 'useUnit',
    selectSupplyVal: 'name',
  },
  {
    name: '应用至当前工程项目',
    value: 'useProject',
    selectSupplyVal: 'name',
  },
]);
const scopeValChange = eve => {
  const val = eve.target.value;
  let target = scopeValueList.find(i => i.value === modalVal.scopeValue);
  if (target.selectSupplyVal) {
    modalVal.supplyWay = target.selectSupplyVal;
  } else {
    // modalVal.supplyWay = supplyWayList[0].value;
    modalVal.supplyWay = null;
  }
};
const supplyWayValChange = eve => {
  const val = eve.target.value;
  let target = scopeValueList.find(i => i.value === modalVal.scopeValue);
  target.selectSupplyVal = val;
};
const confirmationStatus = ref(false);

const closeApplication = isFlag => {
  if (!isFlag) {
    let list =
      props.tableData && JSON.parse(JSON.stringify(oldTableData.value));
    vexTableData.value = handleList([...list]);
    let apiData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      deId: props.currentInfo.sequenceNbr,
      pageType: props.type === 1 ? 'fbfx' : 'csxm',
    };
    feePro.cancelEditorDJGC(apiData).then(res => {});
  }
  console.log('closeApplication', isFlag, !isFlag);
  confirmationStatus.value = false;
  globalData.isEditStatus = false;
  initNewGFandWF();
};
const saveApplication = () => {
  //确定应用范围
  let flag = newGFandWF.find(i => i.oldRate !== i.newRate) ? true : false;
  console.log('saveApplication', flag);
  let apiData = {
    constructId: globalData.constructId,
    singleId: globalData.singleId, //单项ID
    unitId: globalData.unitId, //单位ID
    deId: globalData.deIdSeq,
    type: modalVal.scopeValue,
    way: modalVal.supplyWay,
    isUpdateAWForGF: flag,
    pageType: props.type === 1 ? 'fbfx' : 'csxm',
  };
  flag &&
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText:
        '单价构成中的规费、安文费会按照取费设置设定的费率重新刷新，如若不符，请到取费设置中更改',
      confirm: () => {
        feePro.resetRateDJGC({ deId: useSetDataSeq.value }).then(res => {
          console.log('单价构成规费变化', apiData, res);
          if (res.status === 200) {
            infoMode.hide();
            useAll(apiData);
          } else {
            message.error(res.message);
          }
        });
      },
    });
  !flag && useAll(apiData);
};
const useAll = apiData => {
  console.log('useAll---应用范围', apiData);
  feePro.applyEditorDJGC(apiData).then(res => {
    if (res.status === 200) {
      // 刷新页面数据--更新状态
      confirmationStatus.value = false;
      console.log('useAll---应用范围返回', res, confirmationStatus.value);
      message.success('应用成功');
      closeApplication(true);
      getTableData();
    } else {
      confirmationStatus.value = false;
      message.error(res.message);
    }
  });
};
//如果页面编辑变化点击编辑区和切换页签弹框设置应用范围
onBeforeUnmount(() => {
  clickOutside();
});

// onDeactivated(() => {
//   clickOutside()
// })
let menuisClick = ref(false);
const menuClick = ({ $event, type }) => {
  menuisClick.value = true;
};
const showPanel = ref(null);
const clickOutside = () => {
  console.log('zjMouldRef.value', openZRMBM.value);
  if (openZRMBM.value) return; //打开载入模板不可以拥有
  if (confirmationStatus.value) return;
  let isPanelVisible = showPanel.value?.isPanelVisible();
  console.log('showPanel', showPanel.value, isPanelVisible, menuisClick.value);
  if (menuisClick.value) {
    //执行右键操作的时候也不可以弹应用框
    menuisClick.value = false;
    return;
  }
  console.log('9999999999999999999', globalData.pageType, props.type);
  if (
    globalData.pageType === props.type
    // (props.type === 2 && store.tabSelectName === '措施项目') ||
    // (props.type === 1 && store.tabSelectName === '分部分项')
  ) {
    console.log(
      'bbbbbbbbbbbbbbb',
      !isPanelVisible,
      !comModel.value,
      isCurrent.value,
      globalData.isEditStatus,
      !editCaculateBase.value
    );
    if (
      !isPanelVisible &&
      !comModel.value &&
      isCurrent.value &&
      globalData.isEditStatus &&
      !editCaculateBase.value
    ) {
      confirmationStatus.value = true;
    }
  }
};
onClickOutside(clickInsideContent, clickOutside);
</script>

<style lang="scss" scoped>
.btns {
  position: absolute;
  width: 200px;
  bottom: 10px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
.prive-comTab-content {
  width: 100%;
  height: 100%;
  .head {
    display: flex;
    .ant-btn {
      padding: 5px 6px !important;
      .anticon {
        font-size: 22px;
      }
      .text {
        margin-left: 1px !important;
        vertical-align: bottom;
        display: inline-block;
        padding-bottom: 3px;
      }
    }
  }
  .content {
    width: 100%;
    // display: flex;
    // justify-content: space-between;
    height: calc(100% - 40px);
  }
}
.confirmation-dialog {
  .tips {
    font-size: 14px;
  }
  .label-list {
    margin: 16px 0 32px;
    display: flex;
    align-items: center;
    .title {
      font-weight: 400;
      font-size: 14px;
      color: #287cfa;
      margin-right: 16px;
    }
  }
  .confirmation-btns {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.iconFont {
  margin-right: 5px;
}
.centerCon {
  width: 100%;
  height: 270px;
  display: flex;
  justify-content: space-between;
  border: 1px solid #b9b9b9;
  .leftContent,
  .rightContent {
    padding: 10px;
    .title {
      color: #287cfa;
    }
  }
  .line {
    width: 2px;
    max-height: 480px;
    margin-right: 20px;
    background: linear-gradient(
      180deg,
      rgba(66, 158, 252, 0) 0%,
      rgba(58, 148, 251, 0.55) 28%,
      #348cfb 51%,
      rgba(46, 133, 250, 0.55) 73%,
      rgba(40, 124, 250, 0) 100%
    );
    opacity: 0.52;
  }
}
.footer {
  width: 100%;
  position: absolute;
  display: flex;
  justify-content: space-between;
  margin: 15px 0 0;
  .btnList {
    margin-top: -6px;
    width: 150px !important;
    display: flex;
    justify-content: space-between;
  }
}
</style>
