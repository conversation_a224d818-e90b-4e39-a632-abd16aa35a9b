const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const AppContext = require("../core/container/APPContext");
const {ObjectUtil} = require("../../../common/ObjectUtil");


class GljCommonController extends Controller {


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取菜单栏数据
     * @param arg
     * @return {Promise<void>}
     */
    async getMenuData(arg) {
        let menuData = await this.service.gongLiaoJiProject.gljCommonService.getMenuData(arg);
        return ResponseData.success(menuData);
    }

    /**
     * 获取外层菜单栏数据
     * @param arg
     * @return {Promise<void>}
     */
    getMenuList(arg) {
        return this.service.gongLiaoJiProject.gljCommonService.getMenuList(arg);
    }

    /**
     *   表格列设置查询
     * @param arg
     * @returns {*}
     */
    async getTableList(arg) {
        let result = await this.service.gongLiaoJiProject.gljCommonService.getTableList(arg)
        return ResponseData.success(result);
    }

    /**
     *   表格列设置保存
     * @param arg
     * @returns {*}
     */
    async saveTableList(arg) {
        let objMap = await this.service.gongLiaoJiProject.gljCommonService.saveTableList(arg);
        return ResponseData.success(objMap);
    }

    /**
     * 工程项目设置-便捷性设置
     * @param arg
     * @returns {*}
     */
    async getProjectSetting(arg) {
        let result = await this.service.gongLiaoJiProject.gljCommonService.getProjectSetting(arg);
        return ResponseData.success(result);
    }

    /**
     * 保存工程项目设置-便捷性设置
     * @param arg
     * @returns {*}
     */
    async saveProjectSetting(arg) {
        await this.service.gongLiaoJiProject.gljCommonService.saveProjectSetting(arg);
        return ResponseData.success();
    }

    /**
     * 查询--存储路径   工程项目便捷性设置查询
     * @return {string|null}
     */
    async selectFolder(args) {
        return await this.service.gongLiaoJiProject.gljCommonService.selectFolder(args);
    }

    /**
     * 判断项目是否有变更
     * @param arg
     * @return {Promise<string|null|*>}
     */
    async diffProject(arg) {
        return await this.service.gongLiaoJiProject.gljCommonService.diffProject(arg);
    }




    /**
     * 选择性导入ysf文件
     * @param arg
     * @returns {*}
     */
    async importGljFile(arg){
        return await this.service.gongLiaoJiProject.gljCommonService.importGljFile(arg);
    }
    /**
     * 保存导入后的ysf文件
     * @param arg
     * @returns {*}
     */
    async saveImportProject(args, clear="清除历史记录- -单位工程结构变化，节约内存"){
        try {
            await this.service.gongLiaoJiProject.gljCommonService.saveImportProject(args);
            if (args.importConstructId !== args.constructId) {
                //防止自己导入自己后内存数据清空
                AppContext.removeContext(args.importConstructId);
            }
        } catch (e) {
            if (args.importConstructId !== args.constructId) {
                //防止自己导入自己后内存数据清空
                AppContext.removeContext(args.importConstructId);
            }
            console.log("导入失败：" + e.message);
        }
        return ResponseData.success(true);
    }
    /**
     * 删除导入项目缓存
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteImportProject(args) {
        //项目导入导出取消后；关闭项目窗口后
        if (ObjectUtil.isNotEmpty(args.importConstructId)) {
            AppContext.removeContext(args.importConstructId);
        }
        if (ObjectUtil.isNotEmpty(args.constructId)) {
            AppContext.removeContext(args.constructId);
        }
        return ResponseData.success(true);
    }
    /**
     * ysf文件选择性导出
     * @param arg
     * @return {*}
     */
    exportGljFile(arg){
        return this.service.gongLiaoJiProject.gljCommonService.exportGljFile(arg);
    }

    /**
     * 所有页签缓存
     * @param arg
     * @returns {*}
     */
    async getTableSettingCache(arg) {
        let result = await this.service.gongLiaoJiProject.gljCommonService.getTableSettingCache(arg)
        return ResponseData.success(result);
    }

    /**
     * 设置所有页签缓存
     * @param arg
     * @returns {*}
     */
    async setTableSettingCache(arg) {
        await this.service.gongLiaoJiProject.gljCommonService.setTableSettingCache(arg)
        return ResponseData.success(true);
    }

    /**
     * 定额操作缓存
     * @param arg
     * @returns {*}
     */
    async getDeSettingCache(arg) {
        let result = await this.service.gongLiaoJiProject.gljCommonService.getDeSettingCache(arg)
        return ResponseData.success(result);
    }

    /**
     * 获取精度设置
     * @param args
     * @returns {*}
     */
    async getPrecisionSetting(args) {
        let {constructId} = args;
        let result = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId)
        return ResponseData.success(result);
    }

    /**
     * 精度设置
     * @param arg
     * @returns {*}
     */
    async setPrecisionSetting(arg) {
        let result = await this.service.gongLiaoJiProject.gljCommonService.setPrecisionSetting(arg)
        return ResponseData.success(result);
    }

    /**
     * 设置定额操作缓存
     * @param arg
     * @returns {*}
     */
    async setDeSettingCache(arg) {
        await this.service.gongLiaoJiProject.gljCommonService.setDeSettingCache(arg)
        return ResponseData.success(true);
    }
}

GljCommonController.toString = () => '[class GljCommonController]';
module.exports = GljCommonController;
