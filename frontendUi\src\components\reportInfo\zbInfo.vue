<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2024-11-18 09:40:31
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-21 16:40:43
-->
<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="props.infoVisible"
    :title="dialogTitle"
    :mask="false"
    :lockView="false"
    :lockScroll="false"
    :destroyOnClose="true"
    width="800"
    @cancel="close"
    @close="close"
  >
    <div class="head-action">
      <a-tabs
        v-model:activeKey="componentName"
        type="card"
        @change="tableChange"
        :hideAdd="true"
      >
        <a-tab-pane v-for="pane in tabList" :key="pane.key" :tab="pane.title">
        </a-tab-pane>
      </a-tabs>
    </div>
    <keep-alive>
      <div class="content">
        <component
          ref="materialRef"
          :is="components.get(componentName)"
          :tableData="tableData"
        ></component>
      </div>
    </keep-alive>
    <div :class="{ footer: !hasDuplicates }">
      <div v-if="!hasDuplicates" class="check-info">
        <icon-font class="icon-font" type="icon-querenshanchu"></icon-font
        >工程编号唯一且不可重复
      </div>
      <div class="footer-btn-list">
        <a-button @click="close">取消</a-button>
        <a-button type="primary" @click="checkInfo()">确定</a-button>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { defineAsyncComponent, markRaw, ref, toRaw, watch } from 'vue';
import api from '@/api/projectDetail';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';

let tabList = ref([
  {
    title: '标段信息',
    key: 'bdInfo',
  },
  // {
  //   title: '投标信息',
  //   key: 'tbInfo',
  // },
  {
    title: '项目附加信息',
    key: 'fjInfo',
  },
  {
    title: '设置工程编号',
    key: 'setCodeInfo',
  },
]);
const route = useRoute();
const components = markRaw(new Map());
components.set(
  'bdInfo',
  defineAsyncComponent(() => import('./bdInfo.vue'))
);
components.set(
  'tbInfo',
  defineAsyncComponent(() => import('./tbInfo.vue'))
);
components.set(
  'fjInfo',
  defineAsyncComponent(() => import('./fjInfo.vue'))
);
components.set(
  'setCodeInfo',
  defineAsyncComponent(() => import('./setCodeInfo.vue'))
);

let componentName = ref('bdInfo');
let tableData = ref([]);
let dialogTitle = ref('招标信息');
const props = defineProps(['infoVisible', 'xmlType', 'indexLoading', 'pageFr']);

const emits = defineEmits(['update:infoVisible', 'successHandle']);
let store = projectDetailStore();
let hasDuplicates = ref(true);

watch(
  () => props.infoVisible,
  value => {
    if (value) {
      console.log('```````````````', props.xmlType, tabList.value);
      dialogTitle.value = props.xmlType === 1 ? '投标信息' : '招标信息';
      getNotFilledList();
      let initTabList = tabList.value.filter(
        item => !['tbInfo'].includes(item.key)
      );
      if (props.xmlType === 1 && !initTabList.find(x => x.key === 'tbInfo')) {
        initTabList.splice(1, 0, {
          title: '投标信息',
          key: 'tbInfo',
        });
      }
      tabList.value = initTabList;
    }
  }
);
const tableChange = event => {};

const close = () => {
  emits('update:infoVisible', false);
};

const getNotFilledList = () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    type: props.xmlType,
  };
  console.log('🚀 ~ api.getNotFilledList ~ apiData:', apiData);
  api.getNotFilledList(apiData).then(res => {
    console.log('111111111', res);
    if (res.status === 200 && res.result) {
      tableData.value = res.result;
    }
  });
};

const checkInfo = () => {
  hasDuplicates.value = tableData.value
    .get('gcbh')
    .every((item, index, self) => {
      if (item.dispNo === 1) return true; // 工程级别跟随创建项目时的编码，可为空
      return self.findIndex(t => t.remark === item.remark) === index;
    });
  let bdxxCheck = tableData.value
    ?.get('bdxx')
    ?.every(
      item =>
        (item.requiredFlag === 1 && item.remark) || item.requiredFlag === 0
    );
  let xmfjxxCheck = tableData.value
    ?.get('xmfjxx')
    ?.every(
      item =>
        (item.requiredFlag === 1 && item.remark) || item.requiredFlag === 0
    );
  let tbxxCheck = tableData.value
    ?.get('tbxx')
    ?.every(
      item =>
        (item.requiredFlag === 1 && item.remark) || item.requiredFlag === 0
    );
  tableData.value.get('gcbh').forEach(item => {
    item.children = null;
    item._X_ROW_CHILD = null;
  });
  if (!bdxxCheck) {
    message.error('当前必填项未补充完整，请补充后提交');
    componentName.value = 'bdInfo';
    return;
  }
  if (props.xmlType === 1 && !tbxxCheck) {
    componentName.value = 'tbInfo';
    message.error('当前必填项未补充完整，请补充后提交');
    return;
  }
  if (!xmfjxxCheck) {
    componentName.value = 'fjInfo';
    message.error('当前必填项未补充完整，请补充后提交');
    return;
  }
  if (!hasDuplicates.value) {
    componentName.value = 'setCodeInfo';
    return;
  }
  if (hasDuplicates.value) {
    saveNotFilledList();
  }
  console.log('hasDuplicates', hasDuplicates);
};

const saveNotFilledList = () => {
  console.log('saveNotFilledList', JSON.parse(JSON.stringify(tableData.value)));
  let apiData = {
    projectOverviewList: toRaw(tableData.value),
    constructId: route.query.constructSequenceNbr,
    levelType: 1,
    type: props.xmlType,
  };
  console.log('apiData', apiData);
  api.saveNotFilledList(apiData).then(res => {
    console.log('保存事件', res);
    if (res.status === 200 && res.result) {
      emits('successHandle');
    }
  });
};
</script>

<style lang="scss" scoped>
.content {
  height: 520px;
}
.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .check-info {
    line-height: 32px;
    font-size: 12px;
    color: #2a2a2a;
    .icon-font {
      font-size: 16px;
    }
  }
}
.footer-btn-list {
  float: right;
}
</style>
