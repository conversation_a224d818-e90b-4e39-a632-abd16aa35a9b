/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-06 09:48:35
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-11-27 17:26:52
 */
import redo from '@/hooks/redo';
import { projectDetailStore } from '@/store/projectDetail';

const { ipcRenderer } =
  (window.require && window.require('electron')) || window.electron || {};

/**
 * ipc
 * 官方api说明：https://www.electronjs.org/zh/docs/latest/api/ipc-renderer
 *
 * 属性/方法
 * ipc.invoke(channel, param) - 发送异步消息（invoke/handle 模型）
 * ipc.sendSync(channel, param) - 发送同步消息（send/on 模型）
 * ipc.on(channel, listener) - 监听 channel, 当新消息到达，调用 listener
 * ipc.once(channel, listener) - 添加一次性 listener 函数
 * ipc.removeListener(channel, listener) - 为特定的 channel 从监听队列中删除特定的 listener 监听者
 * ipc.removeAllListeners(channel) - 移除所有的监听器，当指定 channel 时只移除与其相关的所有监听器
 * ipc.send(channel, ...args) - 通过channel向主进程发送异步消息
 * ipc.postMessage(channel, message, [transfer]) - 发送消息到主进程
 * ipc.sendTo(webContentsId, channel, ...args) - 通过 channel 发送消息到带有 webContentsId 的窗口
 * ipc.sendToHost(channel, ...args) - 消息会被发送到 host 页面上的 <webview> 元素
 */

/**
 * 根据当前项目上下文自动添加缺失的constructId, singleId, unitId参数
 * @param {Object} params - 原始参数对象
 * @returns {Object} - 添加了缺失参数的对象
 */
function addMissingProjectIds(params) {
  if (!params || typeof params !== 'object') {
    return params;
  }

  try {
    // 获取项目store实例
    const projectStore = projectDetailStore();

    // 如果没有当前树信息，直接返回原参数
    if (!projectStore.currentTreeInfo) {
      console.log('[IPC] 没有当前树信息，跳过ID自动添加');
      return params;
    }

    const result = { ...params };
    let hasChanges = false;

    // 检查并添加constructId
    if (!result.constructId && result.constructId !== 0) {
      if (projectStore.currentTreeInfo.levelType === 1) {
        result.constructId = projectStore.currentTreeInfo?.id;
      } else if (projectStore.currentTreeGroupInfo?.constructId) {
        result.constructId = projectStore.currentTreeGroupInfo.constructId;
      }
      if (result.constructId) {
        hasChanges = true;
        console.log('[IPC] 自动添加constructId:', result.constructId);
      }
    }

    // 检查并添加singleId
    if (!result.singleId && result.singleId !== 0) {
      if (projectStore.currentTreeInfo.levelType === 2) {
        result.singleId = projectStore.currentTreeInfo?.id; // 单项ID
        if (result.singleId) {
          hasChanges = true;
          console.log('[IPC] 自动添加singleId (levelType=2):', result.singleId);
        }
      } else if (projectStore.currentTreeInfo.levelType === 3 && projectStore.currentTreeGroupInfo?.singleId) {
        result.singleId = projectStore.currentTreeGroupInfo.singleId; // 单项ID
        hasChanges = true;
        console.log('[IPC] 自动添加singleId (levelType=3):', result.singleId);
      }
    }

    // 检查并添加unitId
    if (!result.unitId && result.unitId !== 0) {
      if (projectStore.currentTreeInfo.levelType === 3) {
        result.unitId = projectStore.currentTreeInfo?.id; // 单位ID
        if (result.unitId) {
          hasChanges = true;
          console.log('[IPC] 自动添加unitId:', result.unitId);
        }
      }
    }

    if (hasChanges) {
      console.log('[IPC] 参数自动添加完成，原参数:', params, '新参数:', result);
    }

    return result;
  } catch (error) {
    console.error('[IPC] 自动添加项目ID时出错:', error);
    return params;
  }
}

/**
 * 是否为EE环境
 */
const isEE = ipcRenderer ? true : false;
const ipc =  new Proxy(ipcRenderer || {}, {
  get: function (target, prop) {
    if (prop === 'invoke') {
    const startTime = performance.now();
      return function (channel, params) {
        // 处理参数并添加缺失的项目ID
        let processedParams = params;

        if (params instanceof Object) {
          // 添加缺失的constructId, singleId, unitId
          processedParams = addMissingProjectIds(params);
          processedParams.token = localStorage.getItem('token');
        } else if (params instanceof String) {
          processedParams = JSON.parse(params);
          if (processedParams instanceof Object) {
            // 添加缺失的constructId, singleId, unitId
            processedParams = addMissingProjectIds(processedParams);
            processedParams.token = localStorage.getItem('token');
          }
          processedParams = JSON.stringify(processedParams);
        }

        return target.invoke(channel, processedParams).then(result => {
          let whiteList = [
            'controller.commonController.diffProject',
            'controller.constructProjectController.getBottomSummary',
            'controller.gongLiaoJiProject.gljCommonController.diffProject'
          ];
          const endTime = performance.now();
          if (!whiteList.includes(channel))
            console.log(
              '拦截到的数据:',
              'channel:',
              channel,
              'params',
              processedParams,
              'result:',
              result,
              `撤销时间：${endTime - startTime}ms`
            );
          if (result?.redo) {
            redo.addData(channel,result.redo,processedParams);
            redo.getList();
          }
          return result;
        });
      };
    }
    return target[prop];
  },
});
export { ipc, isEE };
