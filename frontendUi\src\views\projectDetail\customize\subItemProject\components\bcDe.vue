<template>
  <common-modal
    className="dialog-comm"
    title="补充定额"
    width="auto"
    v-model:modelValue="props.visible"
    :destroy-on-close="true"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="form-wrap">
      <div class="form-content">
        <a-spin tip="解析中..." :spinning="spinning">
          <a-form :model="inputData" ref="form" @finish="onSubmit">
            <a-form-item
              label="定额编码"
              name="bdCode"
              class="form-item-two"
              :rules="[
                {
                  required: true,
                  message: '请输入定额编码!',
                },
              ]"
            >
              <a-input
                v-model:value.trim="inputData.bdCode"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="单位"
              class="form-item-two"
              name="unit"
              :rules="[{ required: true, message: '请选择单位!' }]"
            >
              <vxeTableEditSelect
                :filedValue="inputData.unit"
                :list="projectStore.unitListString"
                :isNotLimit="true"
                @update:filedValue="
                  newValue => {
                    saveCustomInput(newValue, inputData, 'unit', $rowIndex);
                  }
                "
              ></vxeTableEditSelect>
            </a-form-item>
            <a-form-item
              label="定额名称"
              name="bdName"
              class="form-item-one"
              :rules="[{ required: true, message: '请输入定额名称!' }]"
            >
              <a-input
                v-model:value="inputData.bdName"
                placeholder="请输入"
                @blur="updateName"
              />
            </a-form-item>
            <a-form-item
              label="工程量表达式"
              name="quantityExpression"
              class="form-item-one"
              :rules="[{ validator: checkQuantityExpression, trigger: 'blur' }]"
            >
              <a-input
                v-model:value.trim="inputData.quantityExpression"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="所属章节"
              name="path"
              class="form-item-one"
              :rules="[{ required: true, message: '请选择所属章节!' }]"
            >
              <a-input
                v-model:value.trim="inputData.path"
                @click="showChapter"
                placeholder="请输入"
              >
                <template #suffix>
                  <DownOutlined
                    @click="showChapter"
                    :style="{
                      fontSize: '12px',
                      color: 'rgba(0, 0, 0, 0.25)',
                    }"
                  />
                </template>
              </a-input>
              <select-chapter
                v-model:filedValue="inputData.libraryCode"
                v-model:visible="chapterStatus"
                :treeData="treeData"
                :groupTypeList="groupTypeList"
                :isDeType="1"
                @selectChange="selectChange"
                @selectInfo="selectInfo"
              ></select-chapter>
            </a-form-item>

            <a-form-item label="人工费(元)" name="rfee" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.rfee"
                @blur="blurChange('rfee')"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item label="材料费(元)" name="cfee" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.cfee"
                placeholder="请输入"
                @blur="blurChange('cfee')"
              />
            </a-form-item>
            <a-form-item label="机械费(元)" name="jfee" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.jfee"
                placeholder="请输入"
                @blur="blurChange('jfee')"
              />
            </a-form-item>

            <a-form-item label="主材费(元)" name="zcfee" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.zcfee"
                placeholder="请输入"
                @blur="blurChange('zcfee')"
              />
            </a-form-item>

            <a-form-item label="设备费(元)" name="sbfee" class="form-item-one">
              <a-input
                v-model:value.trim="inputData.sbfee"
                placeholder="请输入"
                @blur="blurChange('sbfee')"
              />
            </a-form-item>
            <a-form-item class="form-item-one">
              <div class="footer-btn-list">
                <a-button type="primary" ghost @click="cancel">取消</a-button>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="spinning || loading"
                  >新建</a-button
                >
              </div>
            </a-form-item>
          </a-form>
        </a-spin>

        <vxe-table
          ref="vexTable"
          :data="tableData"
          keep-source
          :column-config="{ resizable: true }"
          :row-config="{ isCurrent: true, keyField: 'dispNo' }"
          :tree-config="{
            children: 'rcjDetailsDTOs',
            expandAll: true,
          }"
          height="300"
          @cell-click="useCellClickEvent"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod: cellBeforeEditMethod,
          }"
          @edit-closed="editClosedEvent"
          class="table-scrollbar table-edit-common"
          :cell-class-name="selectedClassName"
          :menu-config="contextmenuList"
          @menu-click="onContextMenuClick"
          :scroll-y="{ enabled: true, gt: 0 }"
        >
          <vxe-column width="50" field="dispNo"> </vxe-column>
          <vxe-column tree-node title="编码" field="materialCode" width="120">
            <template #default="{ row }">
              <div>{{ row.materialCode }}</div>
            </template>
          </vxe-column>
          <vxe-column title="类别" field="type" width="100">
            <template #default="{ row }">
              <span>{{ row.type }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="名称"
            field="materialName"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.materialName }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input :maxlength="50" v-model="row.materialName" />
            </template>
          </vxe-column>
          <vxe-column
            title="规格型号"
            field="specification"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.specification }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input :maxlength="50" v-model="row.specification" />
            </template>
          </vxe-column>
          <vxe-column title="单位" field="unit" width="70" :edit-render="{}">
            <template #default="{ row }">
              <span>{{ row.unit }}</span>
            </template>
            <template #edit="{ row }">
              <vxeTableEditSelect
                :transfer="true"
                :filedValue="row.unit"
                :list="projectStore.unitListString"
                @update:filedValue="
                  newValue => {
                    row.unit = newValue;
                  }
                "
              ></vxeTableEditSelect>
            </template>
          </vxe-column>
          <vxe-column
            title="消耗量"
            field="resQty"
            width="90"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.resQty }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.resQty"
                @blur="formatBlur(row, 'resQty')"
              />
            </template>
          </vxe-column>

          <vxe-column
            v-if="projectStore.deStandardReleaseYear === '12'"
            :title="'市场价'"
            field="marketPrice"
            width="110"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ row.marketPrice }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-if="
                  (row.levelMark === 0 ||
                    (row.levelMark !== 0 &&
                      row.rcjDetailsDTOs?.length === 0)) &&
                  isPartEdit &&
                  !isChangeAva(row)
                "
                v-model="row.marketPrice"
                @blur="formatBlur(row, 'marketPrice')"
              />
              <span v-else>{{ row.marketPrice }}</span>
            </template>
          </vxe-column>
          <vxe-column
            v-if="
              projectStore.deStandardReleaseYear === '22' &&
              projectStore.taxMade === 1
            "
            :title="'不含税市场价'"
            field="priceMarket"
            width="110"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ isChangeAva(row) ? '-' : row.priceMarket }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.priceMarket"
                v-if="
                  (row.levelMark === 0 ||
                    !row.rcjDetailsDTOs ||
                    (row.levelMark !== 0 && row.rcjDetailsDTOs.length === 0)) &&
                  isPartEdit &&
                  !isChangeAva(row)
                "
                @blur="formatBlur(row, 'priceMarket')"
              />
              <span v-else>{{ isChangeAva(row) ? '-' : row.priceMarket }}</span>
            </template>
          </vxe-column>
          <vxe-column
            v-if="
              projectStore.deStandardReleaseYear === '22' &&
              projectStore.taxMade === 0
            "
            :title="'含税市场价'"
            field="priceMarketTax"
            width="110"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ isChangeAva(row) ? '-' : row.priceMarketTax }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.priceMarketTax"
                v-if="
                  (row.levelMark === 0 ||
                    !row.rcjDetailsDTOs ||
                    (row.levelMark !== 0 && row.rcjDetailsDTOs.length === 0)) &&
                  isPartEdit &&
                  !isChangeAva(row)
                "
                @blur="formatBlur(row, 'priceMarketTax')"
              />
              <span v-else>{{
                isChangeAva(row) ? '-' : row.priceMarketTax
              }}</span>
            </template>
          </vxe-column>
          <vxe-column
            v-if="projectStore.deStandardReleaseYear === '12'"
            title="除税系数"
            field="taxRemoval"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ row.taxRemoval }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.taxRemoval"
                @blur="formatBlur(row, 'taxRemoval')"
              />
            </template>
          </vxe-column>
          <vxe-column
            v-if="projectStore.deStandardReleaseYear === '22'"
            title="税率"
            field="taxRate"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ isChangeAva(row) ? '-' : row.taxRate }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.taxRate"
                v-if="!isChangeAva(row) && row.supplementDeRcjFlag !== 1"
                @blur="formatBlur(row, 'taxRate')"
              />
              <span v-else>{{ isChangeAva(row) ? '-' : row.taxRate }}</span>
            </template>
          </vxe-column>
        </vxe-table>
        <material-machine-index
          v-model:indexVisible="indexVisible"
          :indexLoading="indexLoading"
          @addChildrenRcjData="addChildrenRcjData"
          @currentInfoReplace="currentInfoReplace"
        ></material-machine-index>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import {
  getCurrentInstance,
  ref,
  reactive,
  watch,
  onMounted,
  toRaw,
  toRefs,
  defineAsyncComponent,
  computed,
} from 'vue';
import {
  isNumericExpression,
  everyNumericHandler,
  quantityExpressionHandler,
} from '@/utils/index';
import api from '@/api/projectDetail';
import { DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail';
import infoMode from '../../../../../plugins/infoMode';
import { removeSpecialCharsFromPrice } from '@/utils/index';
import { useBcData } from '@/hooks/useBcData.js';
import { pureNumber } from '@/utils/index';
import { useCellClick } from '@/hooks/useCellClick.js';
const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  resetCellData,
} = useCellClick({ rowKey: 'dispNo' });
import MaterialMachineIndex from '../../materialMachineIndex/index.vue';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
const { costPriceFormat, rcjDetailAmountFormat, rateFormat } =
  useDecimalPoint();
const selectChapter = defineAsyncComponent(() =>
  import('@/components/selectChapter/index.vue')
);

const projectStore = projectDetailStore();

const checkQuantityExpression = (rule, value) => {
  if (value === null || !value.length) return Promise.resolve();
  const [isSuccess, msg] = quantityExpressionHandler(inputData);
  if (isSuccess) {
    return Promise.reject(msg);
  }
  return Promise.resolve();
};
const formatBlur = (row, filed) => {
  let regex = /-?\d{0,8}(\.\d{0,6}|100)?/;
  let val = (row[filed]?.match(regex) || [''])[0];
  if (filed === 'taxRate') {
    val = rateFormat(val);
  }
  if (['taxRemoval', 'resQty'].includes(filed)) {
    val = rcjDetailAmountFormat(val, 'resQty' === filed ? 0 : '');
  }
  if (['priceMarketTax', 'priceMarket', 'marketPrice'].includes(filed)) {
    val = costPriceFormat(val);
  }
  row[filed] = val;
};
const expressionBlur = () => {
  inputData.quantityExpression = everyNumericHandler(
    inputData.quantityExpression
  );
};

const form = ref();
const loading = ref(false);
const spinning = ref(false);
const emit = defineEmits(['update:visible', 'onSuccess', 'deSaveData']);
const store = projectDetailStore();

let { defaultCodeColl, bcCode } = useBcData();
let chapterStatus = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  code: {
    type: String,
    default: '',
  },
  currentInfo: {
    type: Object,
    default: null,
  },
  type: {
    type: Number,
    default: 1,
  },
});

const colStyle = reactive({
  colSize: null,
});

const inputData = reactive({
  bdCode: null, //项目编码
  bdName: null, //项目名称
  quantityExpression: 'QDL', // 工程量表达式
  quantityVariableValue: 0, // 前端计算工程量表达式替换QDL值
  originalQuantityExpression: [], // 原始工程量表达式避免报错
  unit: null, // 单位
  classifyLevel1: null, // 定额章节1
  classifyLevel2: null, // 定额章节2
  classifyLevel3: null, // 定额章节3
  classifyLevel4: null, // 定额章节4
  rfee: null, // 人工费
  cfee: null, // 材料费
  jfee: null, // 机械费
  zcfee: null, // 主材费
  sbfee: null, // 设备费
  path: null, // 选择的定额章节
  isSupplement: 1,
  rcjFlag: 0,
});
let groupTypeList = ref([]);
let treeData = ref([]);
let expandedKeys = ref([]);
let tableData = ref([]);
const indexVisible = ref(false);
let currentMaterialInfo = ref(null);
let indexLoading = ref(false); // 索引页面loading
const currentInfo = ref();
const vexTable = ref();

onMounted(() => {});

// 章节点击展示弹窗
const showChapter = () => {
  chapterStatus.value = true;
};
// input框输入值置为空
const reset = () => {
  loading.value = false;
  for (let key in inputData) {
    inputData[key] = null;
  }
  inputData.isSupplement = 1;
  inputData.rcjFlag = 0;
  inputData.quantityExpression = 'QDL';
  inputData.originalQuantityExpression = [];
};

const cancel = () => {
  indexVisible.value = false;
  emit('bcCancel', 2);
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};

watch(
  () => [props.visible, bcCode.value],
  (val, oldVal) => {
    if (val) {
      form.value?.resetFields();
      reset();
      inputData.bdCode = props.code;
      inputData.libraryCode = store.currentTreeInfo.libraryCode;
      tableData.value = [];
      resetCellData();
      if (val[0] && val[0] !== oldVal[0] && !props.code) {
        defaultCodeColl(2);
      }
      queryDeLibrary();
      if (bcCode.value) {
        inputData.bdCode = bcCode.value;
      }
    }
  }
);

/**
 * 获取定额树数据
 */
const getDeList = code => {
  api
    .deListByTree({
      code,
      deStandardReleaseYear: projectStore?.deStandardReleaseYear,
    })
    .then(res => {
      console.log('获取定额树结构', res);
      treeData.value = getDeInitData([res.result]);
      const firstChildOfFirstDirectory = findFirstChild(
        treeData.value[0]?.childrenList
      );
      console.log('firstChildOfFirstDirectory', firstChildOfFirstDirectory);
      inputData.path = firstChildOfFirstDirectory.path;
      selectInfo(firstChildOfFirstDirectory);
    });
};

const getDeInitData = tree => {
  return tree.map(item => {
    item.key = item.name + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.name;
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getDeInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.name + Math.ceil(Math.random() * 10000 + 1),
            path: (item.path ? item.path : item.name) + '/' + n.name,
          }))
        )
      : null;
    return item;
  });
};

const findFirstChild = tree => {
  if (tree && tree.length > 0) {
    let item = tree.filter(
      x =>
        store.currentTreeInfo.secondInstallationProjectName &&
        x.name.includes(store.currentTreeInfo.secondInstallationProjectName)
    )[0];
    for (const node of tree) {
      if (item) {
        return findFirstChild(item.childrenList);
      } else {
        if (node.childrenList && node.childrenList.length > 0) {
          return findFirstChild(node.childrenList);
        }
      }
    }
    return tree[0]; // 找到第一个目录下的第一个子集
  }
  return null; // 没有子节点或者树为空
};

const queryDeLibrary = () => {
  api.queryDeLibrary(store.currentTreeInfo.deStandardId).then(res => {
    if (res.status === 200) {
      groupTypeList.value = res.result;
      getDeList(store.currentTreeInfo.libraryCode);
    }
  });
};

const selectChange = value => {
  console.log('selectChange', value);
  getDeList(value);
};

const selectInfo = obj => {
  console.log('obj', obj);
  let pathList = obj.path.split('/');
  pathList.forEach((item, index) => {
    inputData[`classifyLevel${index}`] = item;
  });
  inputData.path = obj.path;
};

const onSubmit = () => {
  loading.value = true;
  console.log('inputDAta', inputData);
  inputData.bdName = inputData.bdName.trim();
  inputData.rcjList = JSON.parse(JSON.stringify(tableData.value));
  isStandardDe();
};

// 判断输入的定额编码是否为主定额库编码
const isStandardDe = () => {
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    code: inputData.bdCode,
  };
  api.isStandardDe(apiData).then(res => {
    if (res.status === 200) {
      if (res.result) {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-querenshanchu',
          infoText: '定额编码不可与标准定额编码相同，请重新输入',
          confirm: () => {
            infoMode.hide();
            loading.value = false;
          },
        });
      } else {
        indexVisible.value = false;
        emit('deSaveData', inputData);
      }
    }
  });
};

const getRcjType = value => {
  if (value === 'rfee') {
    return '人工费';
  } else if (value === 'cfee') {
    return '材料费';
  } else if (value === 'jfee') {
    return '机械费';
  } else if (value === 'zcfee') {
    return '主材费';
  } else if (value === 'sbfee') {
    return '设备费';
  }
  return '';
};

const getRcjPriceValue = value => {
  if ('人工费'.includes(value)) {
    return 'rfee';
  } else if ('材料费'.includes(value)) {
    return 'cfee';
  } else if ('机械费'.includes(value)) {
    return 'jfee';
  } else if ('主材费'.includes(value)) {
    return 'zcfee';
  } else if ('设备费'.includes(value)) {
    return 'sbfee';
  }
  return '';
};

const blurChange = type => {
  inputData[type] = costPriceFormat(inputData[type]);
  getSupplementDeByRcj(type);
};

// 补充定额下挂人材机查询
const getSupplementDeByRcj = type => {
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    de: JSON.parse(JSON.stringify(inputData)),
  };
  console.log('apiData', apiData);
  api.getSupplementDeByRcj(apiData).then(res => {
    console.log('补充定额下挂人材机查询', res);
    if (res.status === 200 && res.result) {
      res.result.forEach((item, index) => {
        let rcjType = getRcjType(type);
        console.log('rcjType', rcjType);
        let obj = tableData.value.filter(
          x => x.materialCode === item.materialCode
        )[0];
        if (obj) {
          let price = 0;
          tableData.value.forEach(list => {
            if (list.supplementDeRcjFlag !== 1 && rcjType.includes(list.type)) {
              price +=
                Number(list.resQty) *
                (projectStore.deStandardReleaseYear === '12'
                  ? Number(list.marketPrice)
                  : projectStore.taxMade === 1
                  ? Number(list.priceMarket)
                  : Number(list.priceMarketTax));
            }
          });
          console.log('price11111111', price, obj.type, rcjType);
          if (obj.type === rcjType) {
            obj[
              projectStore.deStandardReleaseYear === '12'
                ? 'marketPrice'
                : projectStore.taxMade === 1
                ? 'priceMarket'
                : 'priceMarketTax'
            ] = costPriceFormat((inputData[type] - price) / Number(obj.resQty));
            console.log('obj', obj);
          }
        } else {
          item.taxRate = 0;
          item.supplementDeRcj = 1;
          item.priceBaseJournal = 1;
          item.priceBaseJournalTax = 1;
          let price = 0;
          tableData.value.forEach(data => {
            if (
              data.supplementDeRcjFlag !== 1 &&
              item.type.includes(data.type)
            ) {
              price +=
                Number(data.resQty) *
                (projectStore.deStandardReleaseYear === '12'
                  ? Number(data.marketPrice)
                  : projectStore.taxMade === 1
                  ? Number(data.priceMarket)
                  : Number(data.priceMarketTax));
            }
          });
          let priceType = getRcjPriceValue(item.type);
          console.log(
            '新增数据1111111',
            price,
            inputData[priceType],
            item.resQty
          );
          item[
            projectStore.deStandardReleaseYear === '12'
              ? 'marketPrice'
              : projectStore.taxMade === 1
              ? 'priceMarket'
              : 'priceMarketTax'
          ] = costPriceFormat(
            Number(inputData[priceType] - price) / Number(item.resQty)
          );
          tableData.value.push(item);
        }
        tableData.value.forEach((list, index) => {
          list.dispNo = index + 1;
        });
      });
    }
  });
};

const contextmenuList = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 1,
          name: '增加材料',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod: ({ options, column, columnIndex, row, rowIndex }) => {
    console.log('🚀 ~ row:', row, options);
    return true;
  },
});

const onContextMenuClick = ({ menu, row }) => {
  currentInfo.value = row;
  console.log(
    '🚀 ~ onContextMenuClick ~ currentInfo.value:',
    currentInfo.value
  );
  const value = menu.code;
  switch (value) {
    case 1:
      const $table = vexTable.value;
      // let parentData = $table.getParentRow(row);
      // console.log('parentData', parentData);
      // row.parentId = parentData?.sequenceNbr;
      openIndexList(row);
      break;
    default:
      break;
  }
};

const openIndexList = row => {
  indexVisible.value = true;
  // currentMaterialInfo.value = row;
  // projectStore.SET_SUB_CURRENT_MATERIAL_INFO(row);
};

const addChildrenRcjData = row => {
  indexLoading.value = true;
  row.isSupplement = 0;
  row.supplementDeRcj = 2;
  row.priceMarketTax = row.priceBaseJournalTax;
  row.priceMarket = row.priceBaseJournal;
  row.marketPrice = row.dePrice;
  tableData.value.push(row);
  tableData.value = tableData.value.map((item, index) => ({
    dispNo: index + 1,
    resQty: 0,
    ...item,
  }));

  indexLoading.value = false;
};

const currentInfoReplace = row => {
  if (!currentInfo.value) {
    message.warning('请先选择要替换的材料');
    return;
  }
  let currentIndex = vexTable.value.getRowIndex(currentInfo.value);
  console.log('index', row, currentIndex);
  let tempList = JSON.parse(JSON.stringify(tableData.value));
  tempList[currentIndex] = row;
  tableData.value = tempList;
  tableData.value.forEach((item, index) => {
    item.dispNo = index + 1;
    item.taxRate = 0;
    item.priceMarket = 1;
    item.marketPrice = 1;
    item.priceMarketTax = 1;
    item.priceBaseJournal = 1;
    item.priceBaseJournalTax = 1;
  });
  console.log('tableData', tableData.value);
};

const updateName = () => {
  tableData.value = tableData.value.map(item => {
    if (item.supplementDeRcjFlag === 1) {
      item.materialName =
        (inputData.bdName ? inputData.bdName : '') + '补充' + item.type;
    }
    return item;
  });
};

// 基期价、市场价为“-
const isChangeAva = row => {
  return (
    projectStore.deStandardReleaseYear === '22' &&
    ![null, undefined].includes(row.isChangeAva) &&
    Number(row.isChangeAva) === 0
  );
};

// ['QTCLFBFB','34000001-2','J00004','J00031','J00031','C11384','C00007','C000200'] 不能编辑
const isPartEdit = computed(() => {
  return !(
    [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
      'C11408',
      'C11388',
      'J00006',
      'J00008',
    ].includes(currentInfo.value?.materialCode) &&
    currentInfo.value?.unit === '%'
  );
});

const tableCellClick = ({ row }) => {
  // if (row.supplementDeRcjFlag === 1) return false;
  return true;
};

// 单元格退出编辑事件
const editClosedEvent = async ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;
  console.log('row11111111', row, $table.isUpdateByRow(row, field));
  // if ($table.isUpdateByRow(row, field)) {
  if (tableData.value.length > 0) {
    if (
      field === 'resQty' ||
      field === 'marketPrice' ||
      field === 'priceMarket' ||
      field === 'priceMarketTax'
    ) {
      let key = getRcjPriceValue(row.type);
      inputData[key] = 0;
      tableData.value.forEach(item => {
        if (!item[field]) item[field] = 0;
        console.log('item.type', item.kind, row.kind, projectStore.deType);
        if (item.kind === row.kind) {
          inputData[key] +=
            Number(item.resQty) *
            (projectStore.deStandardReleaseYear === '12'
              ? Number(item.marketPrice)
              : projectStore.taxMade === 1
              ? Number(item.priceMarket)
              : Number(item.priceMarketTax));
        }
      });
      inputData[key] = Number(inputData[key]).toFixed(2);
    }
    console.log('inputData1111111', inputData);
  }
  // }
};
</script>
<style lang="scss" scoped>
@import './style.scss';
</style>
