'use strict';


const GljFxtjCostMatchStrategy = require('./gljFxtjCostMatchStrategy');
const EE = require('../../../../../core/ee');
const { GljCostMatchUtil } = require('./gljCostMatchUtil');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const { BaseDeFwxsCgRelation2022 } = require('../../../models/BaseDeFwxsCgRelation2022');
const { BaseDe2022 } = require('../../../models/BaseDe2022');
const ProjectDomain = require('../../../domains/ProjectDomain');
const WildcardMap = require('../../../core/container/WildcardMap');
const RcjTypeEnum = require('../../../enums/RcjTypeEnum');
const CostDeMatchConstants = require('../../../constants/CostDeMatchConstants');
const StandardDeModel = require('../../../../PreliminaryEstimate/domains/deProcessor/models/StandardDeModel');
const { Snowflake } = require('../../../../PreliminaryEstimate/utils/Snowflake');
const DeTypeConstants = require('../../../constants/DeTypeConstants');
const { ArrayUtil } = require('../../../utils/ArrayUtil');

/**
 * 房修土建  中小型机械使用费 记取
 */
class GljFxthZxxjxFeeMatchHandle extends GljFxtjCostMatchStrategy {

  constructor() {
    super();
    this.defaultParentName = '中小型机械使用费';
  }

  async customizeViewHandler(args, baseDeList) {
    const { constructId, singleId, unitId } = args;
    // 过滤掉人材机中有机械费的
    let validBaseDeArr = await this.filterBaseDe(constructId, singleId, unitId, baseDeList);
    let treeData = ObjectUtils.cloneDeep(await GljCostMatchUtil.getTreeList(validBaseDeArr, constructId));
    treeData.map(item => {
      item.matchFlag = 1;                       // 是否记取 0：不计取  1：记取
      item.xjybFlag = 2;                      // 1：现浇   2：预拌
    });
    return {
      'treeData': treeData,
      'qdName': this.defaultParentName
    };
  }


  async filterBaseDe(constructId, singleId, unitId, baseDeList) {
    let validBaseDeArr = [];
    let unitRcjList = ProjectDomain.getDomain(constructId).getResourceDomain().getResource(WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD);
    const deSequenceNbrList = baseDeList.map(item => item.sequenceNbr);
    const deRcjMap = new Map();
    for (const rcj of unitRcjList) {
      if (deSequenceNbrList.includes(rcj.deId)) {
        const rcjArr = deRcjMap.get(rcj.deId);
        ObjectUtils.isNotEmpty(rcjArr) ? rcjArr.push(rcj) : deRcjMap.set(rcj.deId, [rcj]);
      }
    }
    if (deRcjMap.size > 0) {
      for (const key of deRcjMap.keys()) {
        const deRcjArr = deRcjMap.get(key);
        if (ObjectUtils.isEmpty(deRcjArr.find(item => item.kind == RcjTypeEnum['TYPE3'].code))) {
          validBaseDeArr.push(baseDeList.find(item => item.sequenceNbr == key));
        }
      }
    }
    return validBaseDeArr;
  }

  async customizeConfirmCostDe(baseDeArr, args) {
    const { constructId, singleId, unitId, feeType, constructionMeasureType, qdId } = args;
    let result = {
      'costDeArr': [],
      'costDeBaseDe': {}
    };
    let costDeArr = [];
    let costDeBaseDe = {};
    if (constructionMeasureType == CostDeMatchConstants.DYFBFX) {
      const parentGroup = ArrayUtil.group(baseDeArr, 'parentId');
      for (const key of parentGroup.keys()) {
        // keys是对应分部时，这一组基数定额的父级id   也就是费用定额的父级
        const baseDeArr = parentGroup.get(key);
        // 本次要添加的费用定额对应的基数定额
        await this.addCostDeByBaseDe(args, key, costDeArr, costDeBaseDe, baseDeArr);
      }
    } else {
      if (constructionMeasureType == CostDeMatchConstants.ZJCS) {
        // 在指定措施的时候  由于措施项目下的定额只能挂在措施项下
        // 所以是直接找有没有叫【中小型机械使用费】的措施项，有就直接把费用定额挂在这个下面
        // 没有就需要新建一个叫【中小型机械使用费】的措施项，再把费用定额挂在新建的这个措施项下
        let qdNode = null;
        if (ObjectUtils.isNotEmpty(qdId)) {
          qdNode = ProjectDomain.getDomain(constructId).csxmDomain.getDeById(qdId);
        }
        if (ObjectUtils.isEmpty(qdNode)) {
          const cgNodeList = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => {
              return item.unitId === unitId
                && item.type == DeTypeConstants.DE_TYPE_DELIST
                && item.pricingMethod == 2
                && item.deName == this.defaultParentName
                && ObjectUtils.isNotEmpty(item.parent)
                && item.parent.type != DeTypeConstants.DE_TYPE_DELIST
                && item.parent.deName != '不可竞争措施项目';
            }
          );
          if (ObjectUtils.isNotEmpty(cgNodeList)) {
            cgNodeList.sort((a, b) => a.index - b.index);
            qdNode = cgNodeList[0];
          } else {
            // 新建一个【中小型机械使用费】的措施项
            const csxmRoot = ProjectDomain.getDomain(constructId).csxmDomain.getRoot(unitId);
            const csxmData = ProjectDomain.getDomain(constructId).csxmDomain.getDes(item => item.unitId === unitId);
            let parent = csxmData.filter(item => item.parentId == csxmRoot.deRowId && item.deName != '不可竞争措施项目');
            parent.sort((a, b) => a.index - b.index);
            let newQdNode = new StandardDeModel(constructId, unitId, Snowflake.nextId(), parent[0].deRowId, DeTypeConstants.DE_TYPE_DELIST);
            newQdNode.deName = this.defaultParentName;
            newQdNode.pricingMethod = 2;
            qdNode = await ProjectDomain.getDomain(constructId).csxmDomain.createDeRow(newQdNode);
          }
        }
        // 根据已经确定的父级和已有的基数定额添加费用定额，并记录费用定额和对应的基数定额
        await this.addCostDeByBaseDe(args, qdNode.sequenceNbr, costDeArr, costDeBaseDe, baseDeArr);
      } else {
        // 指定分部
        let qdNode = null;
        if (ObjectUtils.isNotEmpty(qdId)) {
          qdNode = ProjectDomain.getDomain(constructId).deDomain.getDeById(qdId);
        }
        if (ObjectUtils.isEmpty(qdNode)) {
          // 在指定分部的时候，由于预算书中定额可以直接挂在顶级的【单位工程】下
          // 所以先看基数定额的父级是不是顶级的【单位工程】，如果是那么不考虑分部的问题，直接把费用定额挂在顶级【单位工程】下
          // 如果父级不是单位工程，那么就找有没有叫【中小型机械使用费】的分部，有就挂在下面，没有就新建一个叫【中小型机械使用费】的分部，再把费用定额挂在新建的这个分部下
          const yssData = ProjectDomain.getDomain(constructId).deDomain.getDes(item => item.unitId === unitId);
          const yssRoot = ProjectDomain.getDomain(constructId).deDomain.getRoot(unitId);
          if (baseDeArr[0].parentId == yssRoot.deRowId) {
            qdNode = yssRoot;
          } else {
            const cgjxFb = yssData.find(item => (item.type == DeTypeConstants.DE_TYPE_FB || item.type == DeTypeConstants.DE_TYPE_ZFB) && item.deName == this.defaultParentName);
            if (ObjectUtils.isNotEmpty(cgjxFb)) {
              const firstCgFb = this.getFirstCgFb(constructId, unitId, this.defaultParentName);
              if (ObjectUtils.isNotEmpty(firstCgFb)) {
                qdNode = firstCgFb;
              } else {
                let newQdNode = new StandardDeModel(constructId, unitId, Snowflake.nextId(), yssRoot.deRowId, DeTypeConstants.DE_TYPE_FB);
                newQdNode.deName = this.defaultParentName;
                qdNode = await ProjectDomain.getDomain(constructId).deDomain.createDeRow(newQdNode);
              }
            } else {
              let newQdNode = new StandardDeModel(constructId, unitId, Snowflake.nextId(), yssRoot.deRowId, DeTypeConstants.DE_TYPE_FB);
              newQdNode.deName = this.defaultParentName;
              qdNode = await ProjectDomain.getDomain(constructId).deDomain.createDeRow(newQdNode);
            }
          }
        }
        // 根据已经确定的父级和已有的基数定额添加费用定额，并记录费用定额和对应的基数定额
        await this.addCostDeByBaseDe(args, qdNode.sequenceNbr, costDeArr, costDeBaseDe, baseDeArr);
      }
    }

    result.costDeArr = costDeArr;
    result.costDeBaseDe = costDeBaseDe;
    return result;
  }

  async addCostDeByBaseDe(args, parentId, costDeArr, costDeBaseDe, baseDeArr) {
    const baseDeFwxsCgRelation = await EE.app.db.gongLiaoJiProject.manager.getRepository(BaseDeFwxsCgRelation2022).findOne({
      where: { value: CostDeMatchConstants.ZXXJX }
    });
    // 费用定额对应的基础定额数据
    const baseDe = await EE.app.db.gongLiaoJiProject.manager.getRepository(BaseDe2022).findOne({
      where: {
        libraryCode: baseDeFwxsCgRelation.libraryCode,
        deCode: baseDeFwxsCgRelation.deCode,
        deName: baseDeFwxsCgRelation.deName
      }
    });
    let domain;
    if (ObjectUtils.isNotEmpty(ProjectDomain.getDomain(args.constructId).deDomain.getDeById(parentId))) {
      domain = ProjectDomain.getDomain(args.constructId).deDomain;
    } else {
      domain = ProjectDomain.getDomain(args.constructId).csxmDomain;
    }
    const newCostDe = new StandardDeModel(args.constructId, args.unitId, Snowflake.nextId(), parentId, DeTypeConstants.DE_TYPE_DE);
    newCostDe.isCostDe = CostDeMatchConstants.FXTJ_ZXXJX;
    newCostDe.unit = baseDe.unit;
    newCostDe.libraryCode = baseDe.libraryCode;
    newCostDe.quantityExpression = '1';
    newCostDe.quantityExpressionNbr = 1;
    newCostDe.quantity = 1;
    newCostDe.standardId = baseDe.sequenceNbr;
    let deRow = await domain.createDeRow(newCostDe);
    await domain.appendBaseDe(args.constructId, args.unitId, baseDe.sequenceNbr, deRow.deRowId);
    await domain.updateQuantity(args.constructId, args.unitId, deRow.deRowId, 1);
    costDeArr.push(deRow);
    costDeBaseDe[deRow.sequenceNbr] = baseDeArr;
  }

  async customizeFilterBaseDe(args, baseDeList) {
    return await this.filterBaseDe(args.constructId, args.singleId, args.unitId, baseDeList);
  }

  async customizeFindBaseDeByCostDe(constructionMeasureType, costDe, baseDeList) {
    if (constructionMeasureType == CostDeMatchConstants.DYFBFX) {
      return baseDeList.filter(item => item.parentId == costDe.parentId);
    } else {
      // 22的中小型机械使用费只会有一条费用定额  所以所有的基数定额都是费用定额的基数
      return baseDeList;
    }
  }

}

GljFxthZxxjxFeeMatchHandle.toString = () => '[class GljFxthZxxjxFeeMatchHandle]';
module.exports = GljFxthZxxjxFeeMatchHandle;
