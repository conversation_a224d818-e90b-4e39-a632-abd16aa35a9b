const {ObjectUtils} = require("../utils/ObjectUtils");
const fs = require('fs');
const _ = require("lodash");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {NumberUtil} = require("../utils/NumberUtil");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../core');
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
let npm = require('getmac');
const UnitPriceConstant = require("../enum/UnitPriceConstant");
const RcjLevelMarkConstant = require("../enum/RcjLevelMarkConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const CalculateBaseType = require("../enum/CalculateBaseType");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const CostTypeJrgEnum = require("../enum/CostTypeJrgEnum");
const CostTypeSbLbEnum = require("../enum/CostTypeSbLbEnum");
const Fixs = require("../fixs");

//获取mac地址
class JsonToXmlHZBService extends Service {
    constructor(ctx) {

        super(ctx);
        let rcjArray=[];//人材机汇总接口返回值
        let RcjMap ={};
    }



    async generateXml(args) {
        let resObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        //改为从文件读取
        let projectObj = await PricingFileFindUtils.getProjectObjByPath(resObj.path);
        await  new Fixs(projectObj,projectObj.version).fix();
        this.res = "";
        let json = await this.convertXMLData(projectObj,args.type);
        let xmlString = '<?xml version="1.0" encoding="UTF-8"?>' + "\n"  + await this.printObj(json, "JingJiBiao", "");


        let path = projectObj.path;
        let pathArray = path.split("\\");
        let fileName = pathArray[pathArray.length - 1];
        let suffix = 'xml';
        if(args.vender === 0){
            suffix ='xml'
        }
        let newFileName = projectObj.constructName+"."+suffix;
        let newPath = path.replace(fileName, newFileName);

        //获取线上项目
        // let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(projectObj.constructName);
        let result = dialog.showSaveDialogSync(null, {
            title: '保存文件',
            defaultPath: newPath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [suffix]} // 可选的文件类型
            ]
        });

        if (!ObjectUtils.isEmpty(result)) {

            let filePath = result;
            try {
                fs.writeFileSync(filePath, xmlString);
                console.log('XML写入文件成功！');
            } catch (err) {
                console.log('XML写入文件失败：', err);
            }
            return true;
        }else {

            return false;

        }


    }

    /**
     * ysf格式转为河北标准格式XML对应JSON
     * @param projectObj
     */
    async convertXMLData(projectObj,type){


        this.type = type
        let taxCalculation = await this.service.baseFeeFileService.taxCalculationParam(projectObj);

        let json = {};
        json.文件版本 = '1.0'
        json.接口标准 = '河北建设工程工程量清单惠招标接口2019'
        json.造价规范 = ''
        json.文件类别 = '' //todo
        json.文件校验码 = '' //todo

        let 工程数据 ={}

        let 招投标信息 = {}

        招投标信息.招投标项目编码 = projectObj.constructCode;
        招投标信息.招投标项目名称 = projectObj.constructName;
        招投标信息.招投标单位编码 = '';
        招投标信息.招投标单位名称 = '';
        招投标信息.发包人 = '';//取 工程项目项目层级-工程基本信息-招标人(发包人)
        招投标信息.发包人法定代表人 = '';//todo
        招投标信息.承包人 = '';//todo
        招投标信息.承包人法定代表人 = '';//todo
        招投标信息.工程造价咨询人 = '';//todo
        招投标信息.编制人 = '';//todo
        招投标信息.核对人 = '';//todo
        招投标信息.编制日期 = '';//todo
        招投标信息.委托人 = '';//todo
        招投标信息.计价软件服务商 = '云算房智能造价平台（服务商：河北宏盛建通信息技术有限公司）';
        招投标信息.计价软件产品版本 = projectObj.version;
        工程数据.招投标信息 = 招投标信息
        if(ObjectUtils.isEmpty(global.microDog)){
            招投标信息.计价软件加密锁号 = ''
        }else {
            招投标信息.计价软件加密锁号 = global.microDog.serial
        }


        let 工程项目 = {}
        工程项目.项目编码 = projectObj.constructCode
        工程项目.项目名称 = projectObj.constructName
        工程项目.编制说明 =''//todo
        工程项目.填表须知 ='1  工程量清单与计价格式中所有要求签字、盖章的地方，必须由规定的单位和人员签字、盖章。\n' +
            '2  工程量清单与计价格式中除另有规定外，任何内容不得修改。\n' +
            '3  工程量清单与计价格式中要求填报的单价和合价，投标人均应填报，未填报的单价与合价，视为此项费用已包含在工程量清单的其他单价与合价中。\n' +
            '4  金额（价格）以招标文件规定的币种表示。'
        工程项目.报价说明 = 工程项目.编制说明
        let zjfx = await this.countZjfx({constructId:projectObj.sequenceNbr,levelType:1,type:this.type})
        工程项目.金额 =zjfx.gczj
        工程项目.其中安全生产文明施工费 =zjfx.safeFee
        工程项目.其中规费 =zjfx.gfee
        工程项目.其中设备费及相关费用 =zjfx.sbfsj
        工程项目.其中工程费 =zjfx.qzgcf
        工程项目.其中措施费 =zjfx.qzcsf
        工程项目.其中单价措施费 =zjfx.djcsxhj
        工程项目.其中总价措施费 =zjfx.zjcsxhj
        工程项目.计税方式 =taxCalculation.taxCalculationMethod == '1'?'增值税方式_一般计税':'增值税方式_简易计税';
        工程项目.地区标准 =''


        //单项工程

         //对应不同的新建预算项目
        if(projectObj.biddingType!== 2){
            await this.convertDxgcxx(json,projectObj.singleProjects)
        }else {
            await this.convertDWGXDwgcxx(json,projectObj.unitProject);
        }

        // //通过造价分析获取   投标总价、招标控制价
        // let zj = await this.countZj({constructId:projectObj.sequenceNbr,levelType:1})
        //
        // if(type === 0){
        //     await this.convertZhaoBiaoXx(json,projectObj);
        // }else if(type === 1){
        //     await this.convertTouBiaoXx(json,projectObj,zj);
        // }else if(type === 2){
        //     await this.convertZhaoBiaoKzjXx(json,projectObj,zj);
        // }
        // //对应不同的新建预算项目
        // if(projectObj.biddingType!== 2){
        //     await this.convertDxgcxx(json,projectObj.singleProjects)
        // }else {
        //     await this.convertDWGXDwgcxx(json,projectObj.unitProject);
        // }

        this.rcjArray =[];
        json.工程数据 = 工程数据
        return json;
    }

    //造价分析
    async countZjfx(args){
        let zjfxArray = await this.service.unitProjectService.getCostAnalysisData(args);
        if (ObjectUtils.isEmpty(zjfxArray.costAnalysisConstructVOList) && ObjectUtils.isEmpty(zjfxArray.costAnalysisUnitVOList)){
            return {};
        }else {
            let gczj = 0;
            let safeFee = 0;
            let gfee = 0;
            let sbfsj = 0;
            let qzgcf = 0;//其中工程费 工程项目-造价分析-合计行-金额列 减去 工程项目-造价分析-合计行-设备费及其税金列
            let qzcsf = 0; //其中措施费 取 工程项目-造价分析-合计行-措施项目列 加上 工程项目-造价分析-合计行-安全生产、文明施工费列
            let djcsxhj = 0;
            let zjcsxhj = 0;
            if(args.type !==0){
                //招标文件时固定为 0所以不進來
                if(args.levelType == 1){
                    for (let i = 0; i < zjfxArray.costAnalysisConstructVOList.length; i++) {
                        let costAnalysisConstructVO = zjfxArray.costAnalysisConstructVOList[i];
                        gczj = NumberUtil.add(gczj,costAnalysisConstructVO.gczj)
                        safeFee = NumberUtil.add(safeFee,costAnalysisConstructVO.safeFee)
                        gfee = NumberUtil.add(gfee,costAnalysisConstructVO.gfee)
                        sbfsj = NumberUtil.add(sbfsj,costAnalysisConstructVO.sbfsj)
                        qzgcf = NumberUtil.add(qzgcf,NumberUtil.subtract(costAnalysisConstructVO.gczj, costAnalysisConstructVO.sbfsj))
                        qzcsf =NumberUtil.addParams(qzcsf,costAnalysisConstructVO.djcsxhj,costAnalysisConstructVO.zjcsxhj,costAnalysisConstructVO.safeFee)
                        djcsxhj = NumberUtil.add(djcsxhj,costAnalysisConstructVO.djcsxhj)
                        zjcsxhj = NumberUtil.add(zjcsxhj,costAnalysisConstructVO.zjcsxhj)
                    }
                }else if(args.levelType == 2){
                    for (let i = 0; i < zjfxArray.costAnalysisSingleVOList.length; i++) {
                        let costAnalysisSingleVO = zjfxArray.costAnalysisSingleVOList[i];
                        gczj = NumberUtil.add(gczj,costAnalysisSingleVO.gczj)
                        safeFee = NumberUtil.add(safeFee,costAnalysisSingleVO.safeFee)
                        gfee = NumberUtil.add(gfee,costAnalysisSingleVO.gfee)
                        sbfsj = NumberUtil.add(sbfsj,costAnalysisSingleVO.sbfsj)
                        qzgcf = NumberUtil.add(qzgcf,NumberUtil.subtract(costAnalysisSingleVO.gczj, costAnalysisSingleVO.sbfsj))
                        qzcsf =NumberUtil.addParams(qzcsf,costAnalysisSingleVO.djcsxhj,costAnalysisSingleVO.zjcsxhj,costAnalysisSingleVO.safeFee)
                        djcsxhj = NumberUtil.add(djcsxhj,costAnalysisSingleVO.djcsxhj)
                        zjcsxhj = NumberUtil.add(zjcsxhj,costAnalysisSingleVO.zjcsxhj)
                    }
                }else {
                    args.levelType= 2
                    zjfxArray = await this.service.unitProjectService.getCostAnalysisData(args)
                    let unitZjfx =zjfxArray.costAnalysisSingleVOList.childrenList.find(item =>item.sequenceNbr == args.unitId)
                    gczj    = unitZjfx.gczj??gczj
                    safeFee = unitZjfx.safeFee??safeFee
                    gfee    = unitZjfx.gfee??gfee
                    sbfsj   = unitZjfx.sbfsj??sbfsj
                    qzgcf   = NumberUtil.subtract(unitZjfx.gczj, unitZjfx.sbfsj)
                    qzcsf   = NumberUtil.addParams(unitZjfx.djcsxhj,unitZjfx.zjcsxhj,unitZjfx.safeFee)
                    djcsxhj = unitZjfx.djcsxhj??djcsxhj
                    zjcsxhj = unitZjfx.zjcsxhj??djcsxhj
                }
            }

            let res ={
                gczj : gczj,
                safeFee : safeFee,
                gfee : gfee,
                sbfsj : sbfsj,
                qzgcf :qzgcf,
                qzcsf : qzcsf,
                djcsxhj : djcsxhj,
                zjcsxhj :zjcsxhj
            }
            return res;
        }

    }
    async convertZhaoBiaoXx(json,projectObj){
        let ZhaoBiaoXx ={};
        let constructProjectJBXX = projectObj.constructProjectJBXX;
        if(!ObjectUtils.isEmpty(constructProjectJBXX)){
            for (let i = 0; i < constructProjectJBXX.length; i++) {
                switch (constructProjectJBXX[i].name) {
                    case '招标人(发包人)':
                        ZhaoBiaoXx.Zbr =constructProjectJBXX[i].remark;
                        break;
                    case '招标人(发包人)法人或其授权人':
                        ZhaoBiaoXx.ZbrDb =constructProjectJBXX[i].remark;
                        break;
                    case '工程造价咨询人':
                        ZhaoBiaoXx.Zxr =constructProjectJBXX[i].remark;
                        break;
                    case '工程造价咨询人法人或其授权人':
                        ZhaoBiaoXx.ZxrDb =constructProjectJBXX[i].remark;
                        break;
                    case '编制人':
                        ZhaoBiaoXx.Bzr =constructProjectJBXX[i].remark;
                        break;
                    case '编制时间':
                        ZhaoBiaoXx.BzRq =constructProjectJBXX[i].remark;
                        break;
                    case '核对人(复核人)':
                        ZhaoBiaoXx.Fhr =constructProjectJBXX[i].remark;
                        break;
                    case '核对(复核)时间':
                        ZhaoBiaoXx.FhRq =constructProjectJBXX[i].remark;
                        break;
                }
            }

        }

        json.ZhaoBiaoXx = ZhaoBiaoXx;



    }

    async convertZhaoBiaoKzjXx(json, projectObj,zj) {
        let ZhaoBiaoKzjXx = {};

        let constructProjectJBXX = projectObj.constructProjectJBXX;
        if(!ObjectUtils.isEmpty(constructProjectJBXX)){
            for (let i = 0; i < constructProjectJBXX.length; i++) {
                switch (constructProjectJBXX[i].name) {
                    case '招标人(发包人)':
                        ZhaoBiaoKzjXx.Zbr =constructProjectJBXX[i].remark;
                        break;
                    case '招标人(发包人)法人或其授权人':
                        ZhaoBiaoKzjXx.ZbrDb =constructProjectJBXX[i].remark;
                        break;
                    case '工程造价咨询人':
                        ZhaoBiaoKzjXx.Zxr =constructProjectJBXX[i].remark;
                        break;
                    case '工程造价咨询人法人或其授权人':
                        ZhaoBiaoKzjXx.ZxrDb =constructProjectJBXX[i].remark;
                        break;
                    case '编制人':
                        ZhaoBiaoKzjXx.Bzr =constructProjectJBXX[i].remark;
                        break;
                    case '编制时间':
                        ZhaoBiaoKzjXx.BzRq =constructProjectJBXX[i].remark;
                        break;
                    case '核对人(复核人)':
                        ZhaoBiaoKzjXx.Fhr =constructProjectJBXX[i].remark;
                        break;
                    case '核对(复核)时间':
                        ZhaoBiaoKzjXx.FhRq =constructProjectJBXX[i].remark;
                        break;

                }
            }

        }
        ZhaoBiaoKzjXx.Zbkzj  = zj;
        ZhaoBiaoKzjXx.Gq = '0';
        //参考规则表改为string
        ZhaoBiaoKzjXx.Zlcn = '';

        json.ZhaoBiaoKzjXx = ZhaoBiaoKzjXx;
    }

    //投标
    async convertTouBiaoXx(json, projectObj,zj) {
        let TouBiaoXx = {}
        let constructProjectJBXX = projectObj.constructProjectJBXX;

        if(!ObjectUtils.isEmpty(constructProjectJBXX)){
            for (let i = 0; i < constructProjectJBXX.length; i++) {
                switch (constructProjectJBXX[i].name) {
                    case '招标人(发包人)':
                        TouBiaoXx.Zbr =constructProjectJBXX[i].remark;
                        break;
                    case '投标人(承包人)':
                        TouBiaoXx.Tbr =constructProjectJBXX[i].remark;
                        break;
                    case '投标人(承包人)法人或其授权人':
                        TouBiaoXx.TbrDb =constructProjectJBXX[i].remark;
                        break;
                    case '投标人(承包人)法人或其授权人':
                        TouBiaoXx.TbrDb =constructProjectJBXX[i].remark;
                        break;
                    case '编制人':
                        TouBiaoXx.Bzr =constructProjectJBXX[i].remark;
                        break;
                    case '编制时间':
                        TouBiaoXx.BzRq =constructProjectJBXX[i].remark;
                        break;
                }
            }
        }


        TouBiaoXx.Tbzj  = zj;
        TouBiaoXx.Gq  = '0';
        TouBiaoXx.Bzj  = '0';
        //参考规则表改为string
        TouBiaoXx.Zlcn  = '';
        TouBiaoXx.Dblx  = '0';//担保类型 应填充默认值为0（计价软件没有字段映射）
        if(ObjectUtils.isEmpty(global.microDog)){
            TouBiaoXx.DogNum  = ''; //加密锁硬件序列号，通过BASE64转码保存，不得出现填写“未检测到加密锁号码”之类无效的信息
            TouBiaoXx.DogTbdw  = '';
        }else {
            TouBiaoXx.DogNum  = global.microDog.serial; //加密锁硬件序列号，通过BASE64转码保存，不得出现填写“未检测到加密锁号码”之类无效的信息
            TouBiaoXx.DogTbdw  = global.microDog.busName;
        }

        json.TouBiaoXx = TouBiaoXx;
    }



    //单位工程xx
    async convertDxgcxx(json,singleProjects){

        if(ObjectUtils.isEmpty(singleProjects)){
            return;
        }
        let dxgcxxArray =new Array();
        for (let i = 0; i < singleProjects.length; i++) {
            let singleProject = singleProjects[i];
            let zjfx= await this.countZjfx({constructId:singleProject.constructId,singleId:singleProject.sequenceNbr,levelType:2,type: this.type})
            let 单项工程 = {};
            单项工程.编码 = singleProject.projectCode;
            单项工程.名称 =singleProject.projectName;
            单项工程.金额 = zjfx.gczj;
            单项工程.其中安全文明费 =zjfx.safeFee ;
            单项工程.其中规费 = zjfx.gfee;
            单项工程.其中设备费及相关费用 = zjfx.sbfsj;
            单项工程.其中工程费 =zjfx.qzgcf
            单项工程.其中措施费 =zjfx.qzcsf
            单项工程.其中单价措施费 =zjfx.djcsxhj
            单项工程.其中总价措施费 =zjfx.zjcsxhj

            if(!ObjectUtils.isEmpty(singleProject.subSingleProjects)){

                let  subSingleProjects =singleProject.subSingleProjects;
                let subDxgcxxArray =new Array();
                for (let j = 0; j < subSingleProjects.length; j++) {

                    let  subSingleProject = subSingleProjects[j];
                    zjfx= await this.countZjfx({constructId:singleProject.constructId,singleId:singleProject.sequenceNbr,levelType:2,type: this.type})
                    单项工程 = {};
                    单项工程.编码 = singleProject.projectCode;
                    单项工程.名称 =singleProject.projectName;
                    单项工程.金额 = zjfx.gczj;
                    单项工程.其中安全文明费 =zjfx.safeFee ;
                    单项工程.其中规费 = zjfx.gfee;
                    单项工程.其中设备费及相关费用 = zjfx.sbfsj;
                    单项工程.其中工程费 =zjfx.qzgcf
                    单项工程.其中措施费 =zjfx.qzcsf
                    单项工程.其中单价措施费 =zjfx.djcsxhj
                    单项工程.其中总价措施费 =zjfx.zjcsxhj

                    if(ObjectUtils.isNotEmpty(subSingleProject.subSingleProjects)){
                        await this.convertDxgcxx(单项工程,subSingleProject.subSingleProjects);
                    }else {
                        await this.convertDwgcxx(单项工程,subSingleProject.unitProjects);
                    }


                    subDxgcxxArray.push(单项工程)
                }
                单项工程.单项工程 = subDxgcxxArray;

            }else {
                await this.convertDwgcxx(单项工程,singleProject.unitProjects);
            }

            dxgcxxArray.push(单项工程);
        }
        json.单项工程 = dxgcxxArray;

    }

    // async  convertDxgcxx(singleProject, json = { 单项工程: [] }) {
    //     // 创建 单项工程 对象并填充数据
    //     const 单项工程 = {
    //         Dxgcbh: singleProject.projectCode,
    //         Dxgcmc: singleProject.projectName,
    //         Je: singleProject.total,
    //         Gf: singleProject.gfee,
    //         Aqwmf: singleProject.safeFee,
    //         SbfSJ: singleProject.sbfTax
    //     };
    //
    //     // 如果存在子项目，递归调用转换函数
    //     if (!ObjectUtils.isEmpty(singleProject.subSingleProjects)) {
    //         const subDxgcxxArray = await Promise.all(singleProject.subSingleProjects.map(async (subProject) =>
    //             await this.convertDxgcxx(subProject)
    //         ));
    //         单项工程.subDxgcxxArray = subDxgcxxArray;
    //     }
    //
    //     // 如果有关联的项目，处理它们
    //     if (!ObjectUtils.isEmpty(singleProject.unitProjects)) {
    //         await this.convertDwgcxx(单项工程, singleProject.unitProjects);
    //     }
    //
    //     // 将当前项目的 单项工程 对象添加到结果数组中
    //     json.单项工程.push(单项工程);
    //
    //     // 返回更新后的 json 对象
    //     return json;
    // }

    /**
     * 转换专业类别
     * 映射规则
     12:
     单位工程清单专业 为“建筑工程” 取 1
     清单专业 为“装饰工程” 取 2
     清单专业 为“安装工程” 取 3
     清单专业 为“市政工程” 且定额册选择为“2012-SZGC-DEK” 取 4
     清单专业 为“园林绿化工程” 且定额册选择为“2013-YLLH-DEK” 取 5
     清单专业 为 “市政工程” 且定额册选择为“2013-SZSS-DEX” 取 8
     清单专业 为“仿古建筑工程” 且定额册选择“2013-FGJZ-DEG” 取 9
     清单专业 为“园林绿化工程” 且定额册选择为“2014-YLLHYH-DEY” 取10
     清单专业 为“仿古建筑工程” 且定额册选择为“2014-GJXSGC-DEG” 取 11
     其他取 15

     22：
     单位工程清单专业 为 “建筑工程” 取 1
     清单专业为 “装饰装修工程” 取 2
     清单专业为“安装工程” 取 3
     清单专业为“市政工程” 取 4
     其他取 15  后续定额册更新后 再次补充映射规则
     */
    convertZylb(constructMajorType,mainDeLibrary){

        switch (constructMajorType) {
            case '建筑工程':{
                return 1
                break;
            }
            case '装饰工程':{
                return 2
                break;
            }
            case '安装工程':{
                return 3
                break;
            }
            case '市政工程':{
                if("2013-SZSS-DEX"==mainDeLibrary){
                    return 8
                }else {
                    return 4
                }
                break;
            }
            case '园林绿化工程':{
                if("2013-YLLH-DEK"==mainDeLibrary){
                    return 5
                }else  if("2014-YLLHYH-DEY"==mainDeLibrary){
                    return 10
                }else {
                    return 15
                }
                break;
            }
            case '仿古建筑工程':{
                if("2013-FGJZ-DEG"==mainDeLibrary){
                    return 9
                }else  if("2014-GJXSGC-DEG"==mainDeLibrary){
                    return 11
                }else {
                    return 15
                }
                break;
            }
        }
    }



    /**
     * 单位工程
     * @param 单项工程
     * @param unitProjects
     * @returns {Promise<void>}
     */
    async convertDwgcxx(单项工程,unitProjects){
        if(ObjectUtils.isEmpty(unitProjects)){
            return;
        }
        let dwgcxxArray = new Array();
        for (let i = 0; i < unitProjects.length; i++) {
            let unitProject = unitProjects[i];
            //人材机汇总查询
            this.rcjArray =await this.service.rcjProcess.queryConstructRcjByDeIdNew(2, 0, unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);
            // 获取单位下人材机list
            let rcjList =await PricingFileFindUtils.getRcjList(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);


            this.RcjMap =await this.convertRcjMap(rcjList);
            let zjfx= await this.countZjfx({constructId:unitProject.constructId,singleId:unitProject.spId,unitId:unitProject.sequenceNbr,levelType:3,type: this.type})

            let 单位工程 = {};
            单位工程.编码 = unitProject.upCode ;
            单位工程.名称 = unitProject.upName ;
            单位工程.金额 = zjfx.gczj;
            单位工程.其中安全文明费 =zjfx.safeFee ;
            单位工程.其中规费 = zjfx.gfee;
            单位工程.其中设备费及相关费用 = zjfx.sbfsj;
            单位工程.其中工程费 =zjfx.qzgcf
            单位工程.其中措施费 =zjfx.qzcsf
            单位工程.其中单价措施费 =zjfx.djcsxhj
            单位工程.其中总价措施费 =zjfx.zjcsxhj


            //分部分项工程
            await this.convertQdxm(单位工程,unitProject);
            //
            //
            //措施项目
            // await this.convertCsxm(单位工程,unitProject);
            //
            //
            // //Qtxm
            // await this.convertQtxm(单位工程,unitProject.otherProjects,unitProject.feeFiles);
            //
            //
            // //Zlje
            // await this.convertZlje(单位工程,unitProject.otherProjectProvisionals);
            //
            // //签证和索赔
            // //await this.convertVisaAndClaim(单位工程,unitProject.otherProjectQzAndSuoPeis);
            //
            // //Clzg
            // await this.convertClzg(单位工程,unitProject.otherProjectClZgjs);
            // //Sbzg
            // await this.convertSbzg(单位工程,unitProject.otherProjectSbZgjs);
            //
            // //Zygczg
            // await this.convertZygczg(单位工程,unitProject.otherProjectZygcZgjs);
            //
            //
            // //Zcbfwf
            // await this.convertZcbfwf(单位工程,unitProject.otherProjectServiceCosts);
            //
            //
            // //Jrg
            // await this.convertJrg(单位工程,unitProject.otherProjectDayWorks);
            //
            //

            //
            //

            //
            //

            //
            //
            // //ZbrClSb
            // await this.convertZbrClSb(单位工程,unitProject);
            //
            //
            //主要材料设备明细表
            await this.convertZyClSb(单位工程,unitProject);
            //增值税进项税额计算汇总表
            await this.convertZzsjxshzb(单位工程,unitProject.inputTaxDetails)
            //单位工程费汇总表
            await this.convertFywj(单位工程,unitProject.unitCostSummarys,unitProject.unitCostCodePrices)

            //人材机汇总表
            await this.convertRcjhz(单位工程,unitProject);
            //Aqwmsgf
            await this.convertAqwmsgf(单位工程,unitProject);

            //Gf
            await this.convertGf(单位工程,unitProject);




            dwgcxxArray.push(单位工程)

        }
        单项工程.单位工程 = dwgcxxArray;

    }

    //xxx单位工程信息
    async convertDWGXDwgcxx(单项工程,unitProject){

        // 获取单位下人材机list
        let rcjList =await PricingFileFindUtils.getRcjList(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);


        this.RcjMap =await this.convertRcjMap(rcjList);

        let 单位工程 = {};
        单位工程.Dwgcbh = unitProject.upCode ;
        单位工程.Dwgcmc = unitProject.upName ;
        单位工程.Zylb ==await this.convertZylb(unitProject.constructMajorType,unitProject.mainDeLibrary)
        单位工程.SbfSj = NumberUtil.getDefault(unitProject.sbfsj);
        单位工程.Aqwmf = unitProject.safeFee ;

        //分部分项工程
        await this.convertQdxm(单位工程,unitProject);

        //措施项目
        await this.convertCsxm(单位工程,unitProject);

        //Qtxm
        await this.convertQtxm(单位工程,unitProject.otherProjects,unitProject.feeFiles);
        //Zlje
        await this.convertZlje(单位工程,unitProject.otherProjectProvisionals);
        //签证和索赔
        //await this.convertVisaAndClaim(单位工程,unitProject.otherProjectQzAndSuoPeis);
        //Clzg
        await this.convertClzg(单位工程,unitProject.otherProjectClZgjs);
        //Sbzg
        await this.convertSbzg(单位工程,unitProject.otherProjectSbZgjs);
        //Zygczg
        await this.convertZygczg(单位工程,unitProject.otherProjectZygcZgjs);
        //Zcbfwf
        await this.convertZcbfwf(单位工程,unitProject.otherProjectServiceCosts);
        //Jrg
        await this.convertJrg(单位工程,unitProject.otherProjectDayWorks);


        //ZyClSb
        await this.convertZyClSb(单位工程,unitProject);
        //ZbrClSb
        await this.convertZbrClSb(单位工程,unitProject);
        //增值税进项税额计算汇总表
        await this.convertZzsjxshzb(单位工程,unitProject.unitCostCodePrices)
        //单位工程费汇总表
        await this.convertFywj(单位工程,unitProject.unitCostSummarys,unitProject.unitCostCodePrices)

        //人材机汇总表
        await this.convertRcjhz(单位工程,unitProject);
        //Aqwmsgf
        await this.convertAqwmsgf(单位工程,unitProject);
        //Gf
        await this.convertGf(单位工程,unitProject);


        单项工程.单位工程 = 单位工程;

    }

    //单位工程费用汇总
    async convertFywj(单位工程,unitCostSummarys,unitCostCodePrices){

        if(ObjectUtils.isEmpty(unitCostSummarys)){
            return;
        }
        let 单位工程费汇总表 ={};
        let 单位工程费用子目Array = new Array();
        for (let i = 0; i < unitCostSummarys.length; i++) {

            let unitCostSummary = unitCostSummarys[i];
            let 单位工程费用子目 ={};

            单位工程费用子目.序号 = unitCostSummary.dispNo  ;
            单位工程费用子目.费用编码 = unitCostSummary.code  ;
            单位工程费用子目.费用名称 = unitCostSummary.name  ;
            单位工程费用子目.计算基础 = unitCostSummary.calculateFormula  ;
            单位工程费用子目.计算基础说明 = unitCostSummary.instructions  ;
            单位工程费用子目.费率 = ObjectUtils.isEmpty(unitCostSummary.rate)?'100':unitCostSummary.rate  ;
            单位工程费用子目.金额 = unitCostSummary.price
            单位工程费用子目.其中人工费 = '0'  ;
            单位工程费用子目.其中材料费 = '0' ;
            单位工程费用子目.其中机械费 = '0'  ;
            if('分部分项工程量清单计价合计' ==unitCostSummary.name){
                单位工程费用子目.其中人工费 = unitCostCodePrices.find(item => item.code == 'RGF').price
                单位工程费用子目.其中材料费 = unitCostCodePrices.find(item => item.code == 'CLF').price
                单位工程费用子目.其中机械费 = unitCostCodePrices.find(item => item.code == 'JXF').price
            }
            if('措施项目清单计价合计' ==unitCostSummary.name){
                单位工程费用子目.其中人工费 = NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_RGF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_RGF').price);
                单位工程费用子目.其中材料费 = NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_CLF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_CLF').price);
                单位工程费用子目.其中机械费 = NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_JXF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_JXF').price);
            }
            if('单价措施项目工程量清单计价合计' ==unitCostSummary.name){
                单位工程费用子目.其中人工费 = unitCostCodePrices.find(item => item.code == 'DJCS_RGF').price
                单位工程费用子目.其中材料费 = unitCostCodePrices.find(item => item.code == 'DJCS_CLF').price
                单位工程费用子目.其中机械费 = unitCostCodePrices.find(item => item.code == 'DJCS_JXF').price
            }
            if('其他总价措施项目清单计价合计' ==unitCostSummary.name){
                单位工程费用子目.其中人工费 = unitCostCodePrices.find(item => item.code == 'QTZJCS_RGF').price
                单位工程费用子目.其中材料费 = unitCostCodePrices.find(item => item.code == 'QTZJCS_CLF').price
                单位工程费用子目.其中机械费 = unitCostCodePrices.find(item => item.code == 'QTZJCS_JXF').price
            }
            单位工程费用子目.费用类别 =unitCostSummary.type;
            单位工程费用子目.备注 = unitCostSummary.remark


            单位工程费用子目Array.push(单位工程费用子目)
        }
        单位工程费汇总表.单位工程费用子目 = 单位工程费用子目Array;

        单位工程.单位工程费汇总表 = 单位工程费汇总表;

    }



    //分部分项清单计价表
    async convertQdxm(单位工程, unitProject) {
        //获取单位工程所有人材机
        let itemBillProjects = unitProject.itemBillProjects;
        let rcjArray = this.rcjArray;
        let 分部分项工程 = {}
        分部分项工程.金额 = 0
        if(ObjectUtils.isEmpty(itemBillProjects)){
            单位工程.分部分项工程 =分部分项工程;
            return
        }

        let dwgc = itemBillProjects.find(item => item.kind === '0');
        let bt = null;
        if (ObjectUtils.isNotEmpty(dwgc)) {
            //获取dwgc的所有标题(可能会出现没有标题只有清单的情况)
            分部分项工程.金额 = dwgc.price
            bt = itemBillProjects.filter(item => item.parentId === dwgc.sequenceNbr);
        }

        if(ObjectUtils.isEmpty(bt)){
            return
        }

        const createItemBillProjects = async (QdBtArray,bt) =>{

            if(bt[0].kind === '03'){
                //(需要考虑没有分布的情况)此处就是清单
                await this.convertFbFxQdDeRcj(itemBillProjects,rcjArray,bt,分部分项工程,unitProject)

            }else {
                //有分布
                for (let i = 0; i < bt.length; i++) {
                    let 分部子目 ={};
                    let btElement = bt[i];

                    分部子目.编码 = btElement.dispNo;
                    分部子目.名称 = btElement.name;
                    分部子目.金额 = btElement.total;

                    // 标题下的第一层
                    let filter1 = itemBillProjects.filter(item => item.parentId === btElement.sequenceNbr);

                    if (!ObjectUtils.isEmpty(filter1)){
                        //判断当前层为标题还是清单
                        let filter2 = filter1.filter(item =>{

                            return (item.kind === '01' ||  item.kind === '02')
                        } );
                        if(!ObjectUtils.isEmpty(filter2)){
                            //标题
                            let  QdBtArray = new Array()
                            let QdBtArrayS = await createItemBillProjects(QdBtArray,filter2);
                            分部子目.分部子目 = QdBtArrayS;

                        }else {
                            await this.convertFbFxQdDeRcj(itemBillProjects,rcjArray,filter1,分部子目,unitProject)
                        }

                    }
                    QdBtArray.push(分部子目)
                }

                return QdBtArray;
            }

        }

        let QdBtArray = new Array()
        分部分项工程.分部子目 = await createItemBillProjects(QdBtArray,bt);
        if (ObjectUtils.isEmpty(分部分项工程.分部子目)){
            delete 分部分项工程.分部子目;
        }
        单位工程.分部分项工程 =分部分项工程;
    }


    //措施项目
    async convertCsxm(单位工程, unitProject) {
        //获取单位工程所有人材机
        let rcjArray =this.rcjArray;

        let measureProjectTables = unitProject.measureProjectTables;
        let 措施项目 = {}
        if(ObjectUtils.isEmpty(measureProjectTables)){
            单位工程.措施项目 =措施项目;
            return
        }
        let csxm = measureProjectTables.find(item => item.kind === '0');
        //获取csxm下的所有标题（）
        let bt = measureProjectTables.filter(item => item.parentId === csxm.sequenceNbr);
        if(ObjectUtils.isEmpty(bt)){
            return
        }

        let ZjCs = {};
        let ZjCsBtArray = new Array();

        let DjCs = {}
        let DjCsBtArray = new Array();


        //其他总价措施标题
        let QtZjCsBt = {};

        QtZjCsBt.Mc = '其他总价措施';
        QtZjCsBt.Je = 0;
        //存放其他总价措施清单
        let QtZjCsQd = new Array();


        for (let i = 0; i < bt.length; i++) {
            let btElement = bt[i];
            let qdArray = measureProjectTables.filter(item => item.parentId === btElement.sequenceNbr);
            if(  btElement.constructionMeasureType === 3 ) {// 1单价措施 2安文费 3其他总价措施
                //3其他总价措施
                QtZjCsBt.Xmlb = '2' //其他总价措施项目
                QtZjCsBt.Je = NumberUtil.add(QtZjCsBt.Je,btElement.total);
                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){
                        for (let j = 0; j < qdArray.length; j++) {

                            let fb = qdArray[j];

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){
                                for (let k = 0; k < qdS.length; k++) {
                                    QtZjCsQd.push(qdS[k]);
                                }

                            }
                        }
                    }else {
                        for (let j = 0; j < qdArray.length; j++) {
                            QtZjCsQd.push(qdArray[j]);
                        }

                    }
                }

            }else if(btElement.constructionMeasureType === 2){

                //2安文费
                let ZjCsBt = {};
                ZjCsBt.Xh = btElement.dispNo;
                ZjCsBt.Mc = btElement.name;
                ZjCsBt.Je = btElement.total;
                ZjCsBt.Xmlb ='1'; // 安文费是1
                let ZjCsMxArray = new Array();
                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){
                        //分布

                        let awfQd = new Array();
                        for (let j = 0; j < qdArray.length; j++) {

                            let fb = qdArray[j];

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){
                                for (let k = 0; k < qdS.length; k++) {
                                    awfQd.push(qdS[k]);
                                }
                            }

                        }
                        await this.convertQTZJCsQdDeRcj(awfQd, unitProject, measureProjectTables, ZjCsMxArray, ZjCsBt);
                        // ZjCsBt.ZjCsBt = zjCsBtArray;
                    }else {
                        //清单
                        await this.convertQTZJCsQdDeRcj(qdArray, unitProject, measureProjectTables, ZjCsMxArray, ZjCsBt);
                    }
                }
                ZjCsBtArray.push(ZjCsBt);
            }else if(btElement.constructionMeasureType === 1){
                //单价措施
                let DjCsBt = {};
                DjCsBt.Xh = btElement.dispNo;
                DjCsBt.Mc = btElement.name;
                DjCsBt.Je = ObjectUtils.isEmpty(btElement.total)?'0':btElement.total;
                // DjCsBt.Xmlb = btElement.projectType;

                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){
                        //分布
                        let djCsBtArray = new Array();

                        for (let j = 0; j < qdArray.length; j++) {
                            let DjCsBt1 = {};
                            let fb = qdArray[j];
                            DjCsBt1.Xh = fb.dispNo;
                            DjCsBt1.Mc = fb.name;
                            DjCsBt1.Je = fb.total;

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){
                                await this.convertDJCsQdDeRcj(measureProjectTables,rcjArray,qdS,DjCsBt1,unitProject)
                            }

                            djCsBtArray.push(DjCsBt1);
                        }
                        DjCsBt.DjCsBt = djCsBtArray;
                    }else {
                        //清单
                        await this.convertDJCsQdDeRcj(measureProjectTables,rcjArray,qdArray,DjCs,unitProject)
                    }
                }
                DjCsBtArray.push(DjCsBt)
                DjCs.DjCsBt = DjCsBtArray;


            }

        }

        if (!ObjectUtils.isEmpty(QtZjCsQd)){
            let ZjCsMxArray = new Array();
            await this.convertQTZJCsQdDeRcj(QtZjCsQd, unitProject, measureProjectTables, ZjCsMxArray, QtZjCsBt,true);
        }

        ZjCsBtArray = [QtZjCsBt,...ZjCsBtArray];//把总价措施加入
        ZjCs.ZjCsBt = ZjCsBtArray;
        措施项目.ZjCs = ZjCs;
        措施项目.DjCs =DjCs;
        单位工程.措施项目 =措施项目;


    }

    //其他总价措施项目列表
    async QTZJCsXmlb(fxCode){

        if(ObjectUtils.isEmpty(fxCode)){
            return 0;
        }
        let res;
        fxCode = fxCode.slice(0,9);
        switch (fxCode) {
            case '011707001' :
                res =  1;
                break;
            case '021007001' :
                res =  1;
                break;
            case '031302001' :
                res =  1;
                break;
            case '041109001' :
                res =  1;
                break;
            case '050405001' :
                res =  1;
                break;
            case '060305001' :
                res =  1;
                break;
            case '070306001' :
                res =  1;
                break;
            case '081311001' :
                res =  1;
                break;


            case '011707002' :
                res =  2;
                break;
            case '021007002' :
                res =  2;
                break;
            case '031302002' :
                res =  2;
                break;
            case '041109002' :
                res =  2;
                break;
            case '050405002' :
                res =  2;
                break;
            case '060305002' :
                res =  2;
                break;
            case '070306002' :
                res =  2;
                break;
            case '081311002' :
                res =  2;
                break;

            case '011707004' :
                res =  3;
                break;
            case '021007004' :
                res =  3;
                break;
            case '021007004' :
                res =  3;
                break;
            case '041109003' :
                res =  3;
                break;
            case '050405003' :
                res =  3;
                break;
            case '060305003' :
                res =  3;
                break;
            case '070306003' :
                res =  3;
                break;
            case '081311003' :
                res =  3;
                break;

            case '011707007' :
                res =  4;
                break;
            case '021007007' :
                res =  4;
                break;
            case '050405008' :
                res =  4;
                break;
            case '070306006' :
                res =  4;
                break;
            case '031302006' :
                res =  4;
                break;
            case '041109007' :
                res =  4;
                break;
            case '060305006' :
                res =  4;
                break;
            case '081311006' :
                res =  4;
                break;


            case '011707B01' :
                res =  5;
                break;
            case '021007B01' :
                res =  5;
                break;
            case '031302B01' :
                res =  5;
                break;
            case '041109B01' :
                res =  5;
                break;
            case '050405B01' :
                res =  5;
                break;
            case '070306B01' :
                res =  5;
                break;

            case '011707B02' :
                res =  6;
                break;
            case '021007B02' :
                res =  6;
                break;
            case '031302B02' :
                res =  6;
                break;
            case '041109B02' :
                res =  6;
                break;
            case '050405B02' :
                res =  6;
                break;
            case '070306B02' :
                res =  6;
                break;

            case '011707B03' :
                res =  7;
                break;
            case '021007B03' :
                res =  7;
                break;
            case '031302B03' :
                res =  7;
                break;
            case '041109B03' :
                res =  7;
                break;
            case '050405B03' :
                res =  7;
                break;
            case '070306B03' :
                res =  7;
                break;

            case '011707B04' :
                res =  8;
                break;
            case '021007B04' :
                res =  8;
                break;
            case '031302B04' :
                res =  8;
                break;
            case '041109B04' :
                res =  8;
                break;
            case '050405B04' :
                res =  8;
                break;
            case '070306B04' :
                res =  8;
                break;

            case '011707B05' :
                res =  9;
                break;
            case '021007B05' :
                res =  9;
                break;
            case '031302B05' :
                res =  9;
                break;
            case '041109B05' :
                res =  9;
                break;
            case '050405B05' :
                res =  9;
                break;
            case '070306B05' :
                res =  9;
                break;


            case '011707B06' :
                res =  10;
                break;
            case '021007B06' :
                res =  10;
                break;
            case '031302B06' :
                res =  10;
                break;
            case '041109B06' :
                res =  10;
                break;
            case '050405B06' :
                res =  10;
                break;
            case '070306B06' :
                res =  10;
                break;



            case '011707B07' :
                res =  11;
                break;
            case '041109B07' :
                res =  11;
                break;
            case '031302B08' :
                res =  11;
                break;
            case '070306B07' :
                res =  11;
                break;

            case '011707B08' :
                res =  12;
                break;
            case '031302B09' :
                res =  12;
                break;
            case '041109B08' :
                res =  12;
                break;
            case '070306B08' :
                res =  12;
                break;


            case '041109005' :
                res =  13;
                break;


            case '021007B08' :
                res =  14;
                break;
            case '050405B08' :
                res =  14;
                break;

            case '021007B07' :
                res =  15;
                break;
            case '050405B07' :
                res =  15;
                break;


            case '041109B09' :
                res = 18 ;
                break;

            default:
                res  = 0;
                break;
        }
        return res;
    }

    //总价措施基数
    async connertZjcsJs(value){
        if(CalculateBaseType.RGFSCJ_JXFSCJ == value){
            return '人工费市场价+机械费市场价'
        }

        if(CalculateBaseType.RGFDEJ_JXFDEJ == value){
            return '人工费定额价+机械费定额价'
        }
        return '';
    }

    //其他总价措施清单的人材机
    async convertQTZJCsQdDeRcj(qdArray, unitProject, measureProjectTables, ZjCsMxArray, ZjCsBt,zjcsMark) {
        let memUnit = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr);
        for (let j = 0; j < qdArray.length; j++) {
            let ZjCsMx = {}
            let qd = qdArray[j];
            ZjCsMx.Xh = qd.dispNo;
            ZjCsMx.Xmbm = qd.fxCode;
            ZjCsMx.Mc = qd.name;
            ZjCsMx.Js = memUnit.zjcsCostMathCache==undefined?'0':await this.connertZjcsJs( memUnit.zjcsCostMathCache.csfyCalculateBaseCode);
            ZjCsMx.Fl = '0'//清单的费率0 xhc
            ZjCsMx.Rgf = NumberUtil.getDefault(qd.rfee);
            ZjCsMx.Clf = NumberUtil.getDefault(qd.cfee);
            ZjCsMx.Jxf = NumberUtil.getDefault(qd.jfee);
            ZjCsMx.Rgjj = '0';
            ZjCsMx.Cljj = '0';
            ZjCsMx.Jxjj = '0';
            //单价构成
            let djgc = ObjectUtils.isEmpty(memUnit.feeBuild) ? null : memUnit.feeBuild[qd.sequenceNbr];
            if (!ObjectUtils.isEmpty(djgc)) {


                let glf =djgc.find(f=>f.type==="管理费")
                if(ObjectUtils.isNotEmpty(glf)){
                    ZjCsMx.Glf = NumberUtil.getDefault(NumberUtil.numberScale2( glf.allPrice));
                }else {
                    ZjCsMx.Glf = '0';
                }
                ZjCsMx.Glffl = '0'//清单的费率0 xhc

                let lr =djgc.find(f=>f.type==="利润")
                if(ObjectUtils.isNotEmpty(lr)){
                    ZjCsMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                }else {
                    ZjCsMx.Lr = '0';
                }
                ZjCsMx.Lrfl = '0'//清单的费率0 xhc

                let gf = djgc.find(f => f && f.type === "规费")
                if(ObjectUtils.isNotEmpty(gf)){
                    ZjCsMx.Gf = NumberUtil.getDefault(NumberUtil.numberScale2(NumberUtil.numberScale2( gf.allPrice)));
                }else {
                    ZjCsMx.Gf = '0';
                }

                let aqwmf = djgc.find(f => f && f.type === "安全文明施工费");
                if(ObjectUtils.isNotEmpty(aqwmf)){
                    ZjCsMx.Aqwmf = NumberUtil.getDefault(NumberUtil.numberScale2( aqwmf.allPrice));
                }else {
                    ZjCsMx.Aqwmf = '0';
                }

            }else {
                ZjCsMx.Glf='0'
                ZjCsMx.Glffl='0'
                ZjCsMx.Lr='0'
                ZjCsMx.Lrfl='0'
                ZjCsMx.Gf='0'
                ZjCsMx.Aqwmf='0'
            }
            ZjCsMx.Je = NumberUtil.getDefault(qd.total);
            if(zjcsMark){
                ZjCsMx.Xmlb = await this.QTZJCsXmlb(qd.fxCode);
            }else {
                ZjCsMx.Xmlb = qd.projectType;
            }

            ZjCsMx.Rgdj = '0';//xhc说清单的值取0
            ZjCsMx.Bz = qd.description;
            //当前清单下的定额
            let deArrayFilter = measureProjectTables.filter(item => item.parentId === qd.sequenceNbr);

            if (!ObjectUtils.isEmpty(deArrayFilter)) {
                let deArray = deArrayFilter.filter(item => item.kind === '04');
                if (!ObjectUtils.isEmpty(deArray)) {

                    let ZjCsDe = {};
                    let ZjCsDeArray = new Array();
                    for (let k = 0; k < deArray.length; k++) {
                        let de = deArray[k];
                        let ZjCsDezjMx = {};
                        ZjCsDezjMx.Debm = de.fxCode;
                        ZjCsDezjMx.DeGuid = '';
                        ZjCsDezjMx.YsDebm = de.fxCode;
                        ZjCsDezjMx.Dekbz = await this.convertDekbz(de.description);
                        ZjCsDezjMx.Mc = de.bdName;
                        ZjCsDezjMx.Dw = de.unit;
                        ZjCsDezjMx.Sl = de.quantity
                        ZjCsDezjMx.Dj = de.price;
                        ZjCsDezjMx.Hj = de.total;
                        ZjCsDezjMx.Rgf = de.rfee;
                        ZjCsDezjMx.Zcf = ObjectUtils.isEmpty(de.zcfee)?'0':de.zcfee;
                        ZjCsDezjMx.Sbf = '0';
                        ZjCsDezjMx.Fcf = ObjectUtils.isEmpty(de.cfee)?'0':de.cfee;
                        ZjCsDezjMx.Clf = NumberUtil.numberScale2(NumberUtil.add(de.zcfee,de.cfee));
                        ZjCsDezjMx.Jxf = ObjectUtils.isEmpty(de.jfee)?'0':de.jfee;
                        let deDjgc =memUnit.feeBuild[de.sequenceNbr];
                        if (!ObjectUtils.isEmpty(deDjgc)) {
                            ZjCsDezjMx.Rgjj = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.RGF_TYPE).unitPrice);
                            ZjCsDezjMx.Zcjj = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.ZCF_TYPE).unitPrice);
                        } else {
                            ZjCsDezjMx.Rgjj = '0';
                            ZjCsDezjMx.Zcjj = '0';
                        }

                        ZjCsDezjMx.Fcjj = '0';
                        ZjCsDezjMx.Sbjj = '0';
                        if (!ObjectUtils.isEmpty(deDjgc)) {
                            ZjCsDezjMx.Cljj = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.CLF_TYPE).unitPrice);
                            ZjCsDezjMx.Jxjj = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.JXF_TYPE).unitPrice);

                        } else {
                            ZjCsDezjMx.Cljj = '0';
                            ZjCsDezjMx.Jxjj = '0';
                        }
                        ZjCsDezjMx.Glf = de.managerFee;
                        if (!ObjectUtils.isEmpty(deDjgc)) {
                            ZjCsDezjMx.Glffl = deDjgc.find(f => f.type === UnitPriceConstant.GLF_TYPE).rate;
                            ZjCsDezjMx.Glf = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.GLF_TYPE).allPrice);
                        } else {
                            ZjCsDezjMx.Glffl = '0';
                        }

                        if (!ObjectUtils.isEmpty(deDjgc)) {
                            ZjCsDezjMx.Lrfl =deDjgc.find(f => f.type === UnitPriceConstant.LR_TYPE).rate;
                            ZjCsDezjMx.Lr = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.LR_TYPE).allPrice);
                        } else {
                            ZjCsDezjMx.Lrfl = '0';
                        }
                        ZjCsDezjMx.Rgdj = de.totalRgdj;

                        //查询定额下的人材机
                        // let deRcjArry = rcjArray.filter(item =>item.deId ===de.sequenceNbr );
                        let deRcjArry = this.RcjMap.get(de.sequenceNbr)
                        if (!ObjectUtils.isEmpty(deRcjArry)) {
                            let Csxdercjhl = {};
                            let CsxdercjhlMxArray = new Array();
                            for (let l = 0; l < deRcjArry.length; l++) {
                                let deRcjElement = deRcjArry[l];
                                let CsxdercjhlMx = {};
                                CsxdercjhlMx.RcjId = deRcjElement.standardId;
                                CsxdercjhlMx.Rcjhl = ObjectUtils.isEmpty(deRcjElement.resQty) ? 0 : deRcjElement.resQty;
                                CsxdercjhlMx.RcjDehj = NumberUtil.multiplyToString(deRcjElement.dePrice, deRcjElement.totalNumber, 2);//单位定额现合价 Decimal 必填）
                                CsxdercjhlMx.Rcjhj = NumberUtil.multiplyToString(deRcjElement.marketPrice, deRcjElement.totalNumber, 2);
                                CsxdercjhlMxArray.push(CsxdercjhlMx);
                            }
                            Csxdercjhl.CsxdercjhlMx = CsxdercjhlMxArray;
                            ZjCsDezjMx.Csxdercjhl = Csxdercjhl;
                        }
                        ZjCsDeArray.push(ZjCsDezjMx);
                    }
                    ZjCsDe.ZjCsDezjMx = ZjCsDeArray;

                    ZjCsMx.ZjCsDe = ZjCsDe;
                }
            }
            ZjCsMxArray.push(ZjCsMx);


        }
        if(!ObjectUtils.isEmpty(ZjCsBt.ZjCsMx)){
            ZjCsBt.ZjCsMx = [...ZjCsBt.ZjCsMx,...ZjCsMxArray] ;
        }else {
            ZjCsBt.ZjCsMx = ZjCsMxArray;
        }
    }

    /**
     * 处理 招标文件时固定为 0
     * type ==0 招标
     * @returns {Promise<number>}
     */
    async dualZBType(value){
       if( this.type ==0){
          return 0
       }else {
           return value??0
       }

    }


    /**
     * 分布分项清单的人材机
     * 类型
     * @param itemBillProjects
     * @param rcjArray
     * @param filter1
     * @param 分部分项工程
     * @returns {Promise<void>}
     */
    async convertFbFxQdDeRcj(itemBillProjects,rcjArray,filter1,分部分项工程,unitProject){
        let QdmxArray = new Array();
        let filterQd = filter1.filter(item =>item .kind === '03');
        if(!ObjectUtils.isEmpty(filterQd)){
            // let unitTmp = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);
            //清单
            for (let j = 0; j < filterQd.length; j++) {
                let filter1Element = filterQd[j];
                let 分部分项工程量清单 = {};
                // 分部分项工程量清单.Xh = filter1Element.dispNo;
                分部分项工程量清单.项目编码 = filter1Element.bdCode;
                分部分项工程量清单.项目名称 = filter1Element.name;
                分部分项工程量清单.Xmtz = filter1Element.projectAttr;
                // 分部分项工程量清单.Gcnr = '';//工作内容
                // 分部分项工程量清单.Jsgz = '';//计算规则
                分部分项工程量清单.计量单位 = filter1Element.unit;
                分部分项工程量清单.工程量 = filter1Element.quantity;
                分部分项工程量清单.人工费单价 =   await this.dualZBType(filter1Element.rfee)
                分部分项工程量清单.材料费单价 =   await this.dualZBType(filter1Element.cfee)
                分部分项工程量清单.机械费单价 =   await this.dualZBType(filter1Element.jfee)
                分部分项工程量清单.未计价材费单价=  await this.dualZBType(filter1Element.zcfee)
                分部分项工程量清单.设备费单价 =   await this.dualZBType(filter1Element.sbfPrice)
                分部分项工程量清单.管理费单价 =  await this.dualZBType(filter1Element.managerFee)
                分部分项工程量清单.利润单价 =   await this.dualZBType(filter1Element.profitFee)
                分部分项工程量清单.综合单价 =   await this.dualZBType(filter1Element.price)
                分部分项工程量清单.综合合价 =   await this.dualZBType(filter1Element.total)
                分部分项工程量清单.工作内容 =   ''
                分部分项工程量清单.项目特征 =   filter1Element.projectAttr
                分部分项工程量清单.主要标志 =   filter1Element.ifMainQd?1:0




                //定额
                let deFilter = itemBillProjects.filter(item => item.parentId === filter1Element.sequenceNbr);

                if(!ObjectUtils.isEmpty(deFilter)){
                    let 定额组价 = {};
                    let deArray = new Array();
                    //存放当前清单下所有人才机
                    let qdRcjArray = new Array();
                    for (let k = 0; k < deFilter.length; k++) {
                        let deFilterElement = deFilter[k];
                        let 定额子目 ={};
                        定额子目.序号 = deFilterElement.dispNo;
                        定额子目.定额编号 = deFilterElement.bdCode;
                        定额子目.计量单位 = deFilterElement.unit;
                        定额子目.工程量 = deFilterElement.quantity;
                        定额子目.取费基数 = 0;//需要分情况处理，新奔腾中相关费用定额 如 其他总价措施、安文费 均展示对应的计算基数值，gld中展示为0，如何处理？ 考虑简单处理直接参考gld
                        定额子目.费率 =   0;//需要分情况处理，新奔腾中相关费用定额 如 其他总价措施、安文费 均展示对应的计算基数值，gld中展示为0，如何处理？ 考虑简单处理直接参考gld
                        定额子目.人工费单价 = await this.dualZBType(deFilterElement.rfee)
                        定额子目.材料费单价 = await this.dualZBType(deFilterElement.cfee)
                        定额子目.机械费单价 =  await this.dualZBType(deFilterElement.jfee)
                        定额子目.未计价材费单价= await this.dualZBType(deFilterElement.zcfee)
                        定额子目.设备费单价 =   await this.dualZBType(deFilterElement.sbfPrice)
                        定额子目.管理费单价 =  await this.dualZBType(deFilterElement.managerFee)
                        定额子目.利润单价 =   await this.dualZBType(deFilterElement.profitFee)
                        定额子目.综合单价 =   await this.dualZBType(deFilterElement.price)
                        定额子目.综合合价 =   await this.dualZBType(deFilterElement.total)
                        //todo
                        定额子目.工日单价 =   await this.dualZBType(deFilterElement.total) //取 定额中综合用工二类的市场价，若不含综合用工二类数据或存在多条人工费取第一条类别为人工单位为工日的数据市场价




                        //查询定额下的人材机

                        let deRcjArry = this.RcjMap.get(deFilterElement.sequenceNbr)
                        if(!ObjectUtils.isEmpty(deRcjArry)){
                            let 消耗量表 = {}

                            let 消耗量子目Array = new Array();
                            for (let l = 0; l < deRcjArry.length; l++) {
                                let deRcjElement = deRcjArry[l];
                                let 消耗量子目  = {}
                                消耗量子目.序号 = l+1
                                消耗量子目.编码 = deRcjElement.materialCode
                                消耗量子目.类型 = deRcjElement.kind
                                消耗量子目.名称 = deRcjElement.materialName
                                消耗量子目.计量单位 = deRcjElement.unit
                                消耗量子目.数量 = deRcjElement.totalNumber
                                消耗量子目.预算价 = deRcjElement.dePrice
                                消耗量子目.除税系数 = deRcjElement.taxRemoval
                                消耗量子目.市场价 = deRcjElement.materialCode
                                消耗量子目.除税市场价 = deRcjElement.materialCode
                                消耗量子目.结算价 = deRcjElement.materialCode
                                消耗量子目.暂估标志 = deRcjElement.ifProvisionalEstimate
                                消耗量子目.主要材料标志 = deRcjElement.materialCode//todo
                                消耗量子目.规格型号 = deRcjElement.specification
                                消耗量子目.质量等级 = deRcjElement.qualityGrade
                                消耗量子目.厂家 = deRcjElement.manufactor
                                消耗量子目.产地 = deRcjElement.producer
                                消耗量子目.含税合价 = deRcjElement.materialCode
                                消耗量子目.除税合价 = deRcjElement.materialCode
                                消耗量子目.销项税额合计 = deRcjElement.materialCode
                                消耗量子目.进项税额合计 = deRcjElement.jxTotal
                                消耗量子目.供应时间 = ''
                                消耗量子目.甲供数量 = deRcjElement.donorMaterialNumber
                                消耗量子目.送达地点 = deRcjElement.deliveryLocation
                                消耗量子目.备注 = ''

                                消耗量子目Array .push(消耗量子目);
                                qdRcjArray.push(deRcjElement);
                            }
                            消耗量表.消耗量子目  =消耗量子目Array ;

                            定额子目.消耗量表 = 消耗量表

                        }
                        deArray.push(定额子目);

                        let feeFiles= unitProject.feeFiles.find(item=>item.feeFileId ==deFilterElement.feeFileId)
                        let 子目费用表 ={}
                        子目费用表.费用ID = 1 //gld应是按自己业务数据处理，新奔腾固定展示为1，考虑使用新奔腾形式处理
                        子目费用表.费用代码 = feeFiles.feeCode //取 当前定额对应的取费文件的取费编码
                        子目费用表.费用表名称 = feeFiles.feeFileName //取 当前定额对应的取费文件名称

                        let feeBuildElement = await this.service.unitPriceService.getPriceBuild(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr,deFilterElement.sequenceNbr)
                        if(feeBuildElement){

                            let 费用子目Array =[]
                            for (let i = 0; i < feeBuildElement.length; i++) {
                                let 费用子目 ={}
                                费用子目.序号 = feeBuildElement[i].sort
                                费用子目.费用编码 = feeBuildElement[i].typeCode
                                费用子目.费用名称 = feeBuildElement[i].name
                                费用子目.计算基础 = feeBuildElement[i].caculateBase
                                费用子目.计算基础说明 = feeBuildElement[i].desc
                                费用子目.费率 = feeBuildElement[i].rate
                                费用子目.单价 = feeBuildElement[i].unitPrice
                                费用子目.金额 = feeBuildElement[i].allPrice
                                费用子目.费用类别 = feeBuildElement[i].type
                                费用子目.备注 = ''
                                费用子目Array.push(费用子目)
                            }
                            子目费用表.费用子目 = 费用子目Array
                            定额子目.子目费用表 = 子目费用表
                        }


                    }
                    定额组价.定额子目 = deArray;
                    分部分项工程量清单.定额组价 =定额组价;
                }
                QdmxArray.push(分部分项工程量清单)
            }
            分部分项工程.分部分项工程量清单 = QdmxArray;
        }
    }

    //单价措施清单的人材机
    async convertDJCsQdDeRcj(measureProjectTables,rcjArray,filter1,DjCsBt,unitProject){
        let memUnit = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr);
        let djCsMxArray = new Array();
        let filterQd = filter1.filter(item =>item .kind === '03');
        if(!ObjectUtils.isEmpty(filterQd)){
            //清单
            for (let j = 0; j < filterQd.length; j++) {
                let filter1Element = filterQd[j];
                let DjCsMx = {};
                DjCsMx.Xh = filter1Element.dispNo;
                DjCsMx.Qdbm = filter1Element.bdCode;
                DjCsMx.Mc = filter1Element.bdName;
                DjCsMx.Xmtz = filter1Element.projectAttr;
                DjCsMx.Gcnr = '';//工作内容
                DjCsMx.Jsgz = '';//计算规则
                DjCsMx.Dw = filter1Element.unit;
                DjCsMx.Sl = filter1Element.quantity;
                DjCsMx.Zhdj = filter1Element.price;
                DjCsMx.Rgf = filter1Element.rfee;
                DjCsMx.Zcf = ObjectUtils.isEmpty(filter1Element.zcfee)?'0':filter1Element.zcfee;
                DjCsMx.Sbf = '0';
                DjCsMx.Fcf = ObjectUtils.isEmpty(filter1Element.zcfee)?'0':filter1Element.cfee;//辅材费用
                DjCsMx.Clf = NumberUtil.numberScale2(NumberUtil.add(filter1Element.zcfee,filter1Element.cfee));
                DjCsMx.Jxf = ObjectUtils.isEmpty(filter1Element.jfee)?'0':filter1Element.jfee;
                DjCsMx.Rgjj = '0';
                DjCsMx.Zcjj = '0';
                DjCsMx.Fcjj = '0';
                DjCsMx.Sbjj = '0';
                DjCsMx.Cljj = '0';
                DjCsMx.Jxjj = '0';
                DjCsMx.Glf = filter1Element.managerFee;
                DjCsMx.Lr = filter1Element.profitFee;


                let djgc = ObjectUtils.isEmpty(memUnit.feeBuild) ? null : memUnit.feeBuild[filter1Element.sequenceNbr];
                if (!ObjectUtils.isEmpty(djgc)) {


                    let glf =djgc.find(f=>f.type==="管理费")
                    if(ObjectUtils.isNotEmpty(glf)){
                        DjCsMx.Glf = NumberUtil.numberScale2( glf.allPrice);
                    }else {
                        DjCsMx.Glf = '0';
                    }

                    let lr =djgc.find(f=>f.type==="利润")
                    if(ObjectUtils.isNotEmpty(lr)){
                        DjCsMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                    }else {
                        DjCsMx.Lr = '0';
                    }

                    let gf = djgc.find(f => f && f.type === "规费")
                    if(ObjectUtils.isNotEmpty(gf)){
                        DjCsMx.Gf =    NumberUtil.numberScale2( gf.allPrice);
                    }else {
                        DjCsMx.Gf = '';
                    }

                    let aqwmf = djgc.find(f => f && f.type === "安全文明施工费");
                    if(ObjectUtils.isNotEmpty(aqwmf)){
                        DjCsMx.Aqwmf = NumberUtil.numberScale2( aqwmf.allPrice);
                    }else {
                        DjCsMx.Aqwmf = '';
                    }

                }else {
                    DjCsMx.Glf='0'
                    DjCsMx.Lr='0'
                    DjCsMx.Gf='0'
                    DjCsMx.Aqwmf='0'
                }
                DjCsMx.Zgj =   '';
                DjCsMx.Zhhj = filter1Element.total;
                DjCsMx.Rgdj = filter1Element.rfeePrice;
                DjCsMx.Bz = filter1Element.description;

                //定额
                let deFilter = measureProjectTables.filter(item => item.parentId === filter1Element.sequenceNbr);

                if(!ObjectUtils.isEmpty(deFilter)){
                    let Csxdezj = {};
                    let deArray = new Array();
                    //存放当前清单下所有人才机
                    let qdRcjArray = new Array();
                    for (let k = 0; k < deFilter.length; k++) {
                        let deFilterElement = deFilter[k];
                        let CsxdezjMx ={};
                        CsxdezjMx.Debm = deFilterElement.bdCode;
                        CsxdezjMx.DeGuid = "";
                        CsxdezjMx.YsDebm = "";
                        CsxdezjMx.Dekbz  =await this.convertDekbz(deFilterElement.description);
                        CsxdezjMx.Mc = deFilterElement.bdName;
                        CsxdezjMx.Dw = deFilterElement.unit;
                        CsxdezjMx.Sl = deFilterElement.quantity;
                        CsxdezjMx.Dj = deFilterElement.price;
                        CsxdezjMx.Hj = deFilterElement.total;
                        CsxdezjMx.Rgf = deFilterElement.rfee;
                        CsxdezjMx.Zcf = ObjectUtils.isEmpty(deFilterElement.zcfee)?'0':deFilterElement.zcfee;
                        CsxdezjMx.Sbf = '0';
                        CsxdezjMx.Fcf = ObjectUtils.isEmpty(deFilterElement.cfee)?'0':deFilterElement.cfee;
                        CsxdezjMx.Clf = NumberUtil.numberScale2(NumberUtil.add(deFilterElement.zcfee,deFilterElement.cfee));
                        CsxdezjMx.Jxf = ObjectUtils.isEmpty(deFilterElement.zcfee)?'0':deFilterElement.jfee;
                        CsxdezjMx.Rgjj =  '0';
                        CsxdezjMx.Zcjj =  '0';
                        CsxdezjMx.Fcjj =  '0';
                        CsxdezjMx.Sbjj =  '0';
                        CsxdezjMx.Cljj =  '0';
                        CsxdezjMx.Jxjj =  '0';

                        let deFeeBuild = ObjectUtils.isEmpty(memUnit.feeBuild) ? null : memUnit.feeBuild[deFilterElement.sequenceNbr];
                        if(ObjectUtils.isNotEmpty(deFeeBuild)){

                            let glf =deFeeBuild.find(f=>f.type==="管理费")
                            if(ObjectUtils.isNotEmpty(glf)){
                                CsxdezjMx.Glf = NumberUtil.numberScale2( glf.allPrice);
                                CsxdezjMx.Glffl = glf.rate;
                            }else {
                                CsxdezjMx.Glf = '0';
                                CsxdezjMx.Glffl = '0';
                            }

                            let lr =deFeeBuild.find(f=>f.type==="利润")
                            if(ObjectUtils.isNotEmpty(lr)){
                                CsxdezjMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                                CsxdezjMx.Lrfl =  lr.rate;
                            }else {
                                CsxdezjMx.Lr = '0';
                                CsxdezjMx.Lrfl = '0';
                            }
                        }else {
                            CsxdezjMx.Glf = '0';
                            CsxdezjMx.Glffl = '0';
                            CsxdezjMx.Lr = '0';
                            CsxdezjMx.Lrfl = '0';
                        }
                        let deRcjArry = this.RcjMap.get(deFilterElement.sequenceNbr)
                        if(!ObjectUtils.isEmpty(deRcjArry)){
                            let rgfRcj = deRcjArry.filter(item => (item.deId ==deFilterElement.sequenceNbr && item.type =='人工费' ))
                            if(ObjectUtils.isNotEmpty(rgfRcj) &&   ObjectUtils.isNotEmpty(rgfRcj[0].marketPrice)){
                                CsxdezjMx.Rgdj = rgfRcj[0].marketPrice
                            }else {
                                CsxdezjMx.Rgdj = '0'
                            }
                        }else {
                            CsxdezjMx.Rgdj = '0';
                        }

                        //查询定额下的人材机
                        // let deRcjArry = rcjArray.filter(item =>item.deId ===deFilterElement.sequenceNbr );
                        if(!ObjectUtils.isEmpty(deRcjArry)){
                            let Csxrcjhl = {};
                            let CsxrcjhlMxArray = new Array();
                            for (let l = 0; l < deRcjArry.length; l++) {
                                let deRcjElement = deRcjArry[l];
                                let CsxdercjhlMx = {};
                                CsxdercjhlMx.RcjId =deRcjElement.standardId ;
                                CsxdercjhlMx.Rcjhl =ObjectUtils.isEmpty(deRcjElement.resQty)?0:deRcjElement.resQty;
                                CsxdercjhlMx.RcjDehj =NumberUtil.multiplyToString(deRcjElement.dePrice,deRcjElement.totalNumber,2);//单位定额现合价 Decimal 必填）
                                CsxdercjhlMx.Rcjhj   =NumberUtil.multiplyToString(deRcjElement.marketPrice,deRcjElement.totalNumber,2);
                                CsxrcjhlMxArray.push(CsxdercjhlMx);
                                qdRcjArray.push(deRcjElement);
                            }
                            Csxrcjhl.CsxdercjhlMx  =CsxrcjhlMxArray;

                            CsxdezjMx.Csxrcjhl = Csxrcjhl;
                        }
                        deArray.push(CsxdezjMx);
                    }
                    Csxdezj.CsxdezjMx = deArray;
                    DjCsMx.Csxdezj =Csxdezj;

                    //Csxrcjhl 此处是查询当前清单下所有定额下的人材机并根据编码分组求和
                    let Csxrcjhl = {};
                    if(!ObjectUtils.isEmpty(qdRcjArray)){
                        let group = ArrayUtil.group(qdRcjArray,'materialCode');
                        let CsxdercjhlMxS = new Array();
                        for ( let [key, value] of group.entries()) {
                            let CsxdercjhlMx = {};
                            CsxdercjhlMx.RcjId =value[0].standardId ;
                            CsxdercjhlMx.Rcjhl =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , constructProjectRcj.resQty);
                            }, 0);
                            CsxdercjhlMx.RcjDehj =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , NumberUtil.multiply(constructProjectRcj.dePrice,constructProjectRcj.totalNumber) );
                            }, 0);
                            CsxdercjhlMx.Rcjhj =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , NumberUtil.multiply(constructProjectRcj.marketPrice,constructProjectRcj.totalNumber) );
                            }, 0);
                            CsxdercjhlMxS.push(CsxdercjhlMx);
                        }
                        Csxrcjhl.CsxdercjhlMx = CsxdercjhlMxS;
                    }
                    DjCsMx.Csxrcjhl = Csxrcjhl;
                }
                djCsMxArray.push(DjCsMx)
            }
            DjCsBt.DjCsMx = djCsMxArray;
        }
    }
    /**
     * 其他项目
     * @param 单位工程
     * @param otherProjects
     * @returns {Promise<void>}
     */
    async convertQtxm(单位工程, otherProjects,feeFiles) {
        let Qtxm ={};
        if(ObjectUtils.isEmpty(otherProjects)){
            单位工程.Qtxm =Qtxm;
            return ;
        }
        let mainFeeFiles = {};
        if(!ObjectUtils.isEmpty(feeFiles)){
            mainFeeFiles  = feeFiles.find(item => item.defaultFeeFlag === 1);
        }else {
            mainFeeFiles.anwenRateBase = 0;
        }


        let QtxmMxArray = new Array();
        for (let i = 0; i < otherProjects.length; i++) {
            let otherProject = otherProjects[i];
            let QtxmMx ={};
            QtxmMx.Xh = otherProject.dispNo ;
            QtxmMx.Mc = otherProject.extraName ;
            QtxmMx.Dw = otherProject.unit ;
            QtxmMx.Jsgs = otherProject.calculationBase ;
            QtxmMx.Je  = otherProject.total ;
            QtxmMx.Aqwmf  = NumberUtil.multiplyToString(mainFeeFiles.anwenRateBase/100,otherProject.total,2) ;
            QtxmMx.Aqwmffl = mainFeeFiles.anwenRateBase ;
            QtxmMx.Bz = otherProject.description ;
            QtxmMx.Xmlb = await this.handleQtxmXmlb(otherProject.type);
            QtxmMxArray.push(QtxmMx)
        }
        Qtxm.QtxmMx =QtxmMxArray ;
        单位工程.Qtxm =Qtxm;
    }

    /**
     * 其他项目项目类别转换
     * @returns {Promise<string>}
     */
    async handleQtxmXmlb(type){

        let res =''
        switch (type) {
            case OtherProjectCalculationBaseConstant.zljr :{
                res = '1'
                break;
            }
            case OtherProjectCalculationBaseConstant.zgj :{
                res = '2'
                break;
            }
            case OtherProjectCalculationBaseConstant.clzgj :{
                res = '2.1'
                break;
            }
            case OtherProjectCalculationBaseConstant.sbzgj :{
                res = '2.2'
                break;
            }
            case OtherProjectCalculationBaseConstant.zygczgj :{
                res = '2.3'
                break;
            }
            case OtherProjectCalculationBaseConstant.jrg :{
                res = '3'
                break;
            }
            case OtherProjectCalculationBaseConstant.zcbfwf :{
                res = '4'
                break;
            }


        }
        return res
    }

    //暂列金额
    async convertZlje(单位工程, otherProjectProvisionals) {
        let zljeMxArray = new Array();
        let Zlje ={};
        if(ObjectUtils.isEmpty(otherProjectProvisionals)){
            let ZljeMx = {};
            ZljeMx.Xh = '';
            ZljeMx.Mc = '';
            ZljeMx.Dw = '';
            ZljeMx.Zdje = '0';
            ZljeMx.Csxs = '0';
            ZljeMx.Jxse = '0';
            ZljeMx.Bz = '';
            zljeMxArray.push(ZljeMx)
            单位工程.Zlje = zljeMxArray;
            return
        }
        for (let i = 0; i < otherProjectProvisionals.length; i++) {
            let otherProjectProvisional = otherProjectProvisionals[i];
            let ZljeMx = {};
            ZljeMx.Xh = otherProjectProvisional.dispNo;
            ZljeMx.Mc = otherProjectProvisional.name;
            ZljeMx.Dw = otherProjectProvisional.unit;
            ZljeMx.Zdje = otherProjectProvisional.provisionalSum;
            ZljeMx.Csxs = NumberUtil.getDefault(otherProjectProvisional.taxRemoval);
            ZljeMx.Jxse = NumberUtil.getDefault(otherProjectProvisional.jxTotal);
            ZljeMx.Bz = otherProjectProvisional.description;
            zljeMxArray.push(ZljeMx)
        }
        Zlje.ZljeMx =zljeMxArray;
        单位工程.Zlje = Zlje;
    }

    /**
     * 材料暂估价
     * @param 单位工程
     * @param otherProjectClZgjs
     * @returns {Promise<void>}
     */
    async convertClzg(单位工程, otherProjectClZgjs) {
        let clzgMxArray = new Array();
        let Clzg ={};
        if(ObjectUtils.isEmpty(otherProjectClZgjs)){
            let ClzgMx = {};
            ClzgMx.Xh = '';
            ClzgMx.RcjId = '';
            ClzgMx.Clbh  = '';
            ClzgMx.Mc = '';
            ClzgMx.Ggxh = '';
            ClzgMx.Dw  = '';
            ClzgMx.Sl  = '0';


            ClzgMx.Dj  = '0';
            clzgMxArray.push(ClzgMx)
            单位工程.Clzg =clzgMxArray;
            return
        }

        for (let i = 0; i < otherProjectClZgjs.length; i++) {
            let otherProjectZgj = otherProjectClZgjs[i];
            let ClzgMx = {};

            ClzgMx.Xh = otherProjectZgj.dispNo;
            ClzgMx.RcjId = '';
            ClzgMx.Clbh  = otherProjectZgj.unit;
            ClzgMx.Mc = otherProjectZgj.name;
            ClzgMx.Ggxh = otherProjectZgj.attr;
            ClzgMx.Dw  = otherProjectZgj.unit;
            ClzgMx.Sl  = '';
            ClzgMx.Dj  = otherProjectZgj.price;
            clzgMxArray.push(ClzgMx)
        }
        Clzg.ZljeMx =clzgMxArray;
        单位工程.Clzg = Clzg;
    }

    //设备暂估价
    async convertSbzg(单位工程, otherProjectSbZgjs) {
        let SbzgMxArray = new Array();
        let Sbzg ={};
        if(ObjectUtils.isEmpty(otherProjectSbZgjs)){
            let SbzgMx = {};
            SbzgMx.Xh = '';
            SbzgMx.Rcjld = '';
            SbzgMx.Sbbh = '';
            SbzgMx.Mc  = '';
            SbzgMx.Ggxh  = '';
            SbzgMx.Dw = '';
            SbzgMx.Sl = '0';
            SbzgMx.Dj = '0';
            SbzgMx.Hj = '0';
            SbzgMx.Bz = '';
            SbzgMxArray.push(SbzgMx);
            单位工程.Sbzg = SbzgMxArray;
            return
        }
    }

    /**
     * 专用工程暂估价
     * @param 单位工程
     * @param otherProjectZygcZgjs
     * @returns {Promise<void>}
     */
    async convertZygczg(单位工程, otherProjectZygcZgjs) {
        let Zygczg = {}
        let zygczgMxArray =new Array();
        if(ObjectUtils.isEmpty(otherProjectZygcZgjs)){
            let ZygczgMx = {};
            ZygczgMx.Xh = '';
            ZygczgMx.Mc = '';
            ZygczgMx.Gcnr = '';
            ZygczgMx.Dw  = '';
            ZygczgMx.Je = '0';
            ZygczgMx.Csxs  = '0';
            ZygczgMx.Jxse = '0';
            ZygczgMx.Bz = '';
            zygczgMxArray.push(ZygczgMx);
            单位工程.Zygczg =zygczgMxArray;
            return
        }

        for (let i = 0; i < otherProjectZygcZgjs.length; i++) {
            let otherProjectZygcZgj = otherProjectZygcZgjs[i];
            let ZygczgMx = {};
            ZygczgMx.Xh = otherProjectZygcZgj.dispNo ;
            ZygczgMx.Gcmc = otherProjectZygcZgj.name;
            ZygczgMx.Gcnr = otherProjectZygcZgj.content;
            ZygczgMx.Dw  = otherProjectZygcZgj.unit ;
            ZygczgMx.Je = otherProjectZygcZgj.total ;
            ZygczgMx.Csxs  = otherProjectZygcZgj.taxRemoval ;
            ZygczgMx.Jxse = otherProjectZygcZgj.jxTotal ;
            ZygczgMx.Bz = otherProjectZygcZgj.description ;
            zygczgMxArray.push(ZygczgMx);
        }
        Zygczg.ZygczgMx = zygczgMxArray;
        单位工程.Zygczg =Zygczg;
    }

    /**
     * 总承包服务费
     * @param 单位工程
     * @param otherProjectServiceCosts
     * @returns {Promise<void>}
     */
    async convertZcbfwf(单位工程, otherProjectServiceCosts) {
        let Zcbfwf ={};
        if(ObjectUtils.isEmpty(otherProjectServiceCosts)){
            单位工程.Zcbfwf = Zcbfwf;
            return
        }
        let parentList = otherProjectServiceCosts.filter(item =>item.parentId === null);
        let sonList = otherProjectServiceCosts.filter(item =>item.parentId !== null);

        let  zcbfwfMxS = new Array()

        if(!ObjectUtils.isEmpty(sonList)){
            for (let i = 0; i < sonList.length; i++) {
                let filterElement = sonList[i];
                let ZcbfwfMx = {};
                ZcbfwfMx.Xh = filterElement.dispNo;
                ZcbfwfMx.Mc = filterElement.fxName;
                ZcbfwfMx.Xmjz = filterElement.xmje;
                ZcbfwfMx.Fl = filterElement.rate;
                ZcbfwfMx.Je = filterElement.fwje;
                zcbfwfMxS.push(ZcbfwfMx);
            }
        }


        if(ObjectUtils.isEmpty(parentList)){
            //如果没有标题只生成明细数据
            if(!ObjectUtils.isEmpty(sonList)){

                Zcbfwf.ZcbfwfMx =zcbfwfMxS;
                单位工程.Zcbfwf = Zcbfwf;
                return ;
            }
        }



        let ZcbfwfBtArray = new Array();

        for (let i = 0; i < parentList.length; i++) {

            let element = parentList[i];

            let ZcbfwfBt = {};
            ZcbfwfBt.Xh = element.dispNo;
            ZcbfwfBt.Mc = element.fxName;
            ZcbfwfBt.Je = element.fwje;

            let zcbfwfMxArray = new Array();

            //获取当前标题下的子项
            let filter = sonList.filter(item => item.parentId === element.sequenceNbr );
            if(!ObjectUtils.isEmpty(filter)){
                for (let j = 0; j < filter.length; j++) {
                    let filterElement = filter[j];
                    let ZcbfwfMx = {};
                    ZcbfwfMx.Xh = filterElement.dispNo;
                    ZcbfwfMx.Mc = filterElement.fxName;
                    ZcbfwfMx.Xmjz = filterElement.xmje;
                    ZcbfwfMx.Fl = filterElement.rate;
                    ZcbfwfMx.Je = filterElement.fwje;
                    zcbfwfMxArray.push(ZcbfwfMx);
                }
                ZcbfwfBt.ZcbfwfMx = zcbfwfMxArray;

            }
            ZcbfwfBtArray.push(ZcbfwfBt)

        }
        Zcbfwf.ZcbfwfBt =ZcbfwfBtArray;
        Zcbfwf.ZcbfwfMx =zcbfwfMxS;
        单位工程.Zcbfwf = Zcbfwf;

    }

    /**
     * 签证与索赔
     * @param 单位工程
     * @param otherProjectVisaAndClaim
     * @returns {Promise<void>}
     */
    async convertVisaAndClaim(单位工程, otherProjectVisaAndClaim) {
        let VisaAndClaimArray = new Array();
        let VisaAndClaim ={};
        if(ObjectUtils.isEmpty(otherProjectVisaAndClaim)){
            let VisaAndClaimMx = {};
            VisaAndClaimMx.Xh = '';
            VisaAndClaimMx.Mc = '';
            VisaAndClaimMx.Dw = '';
            VisaAndClaimMx.Zdje = '0';
            VisaAndClaimMx.Csxs = '0';
            VisaAndClaimMx.Jxse = '0';
            VisaAndClaimMx.Bz = '';
            VisaAndClaimArray.push(VisaAndClaimMx)
            单位工程.VisaAndClaim = VisaAndClaimArray;
            return
        }
        for (let i = 0; i < otherProjectVisaAndClaim.length; i++) {
            let VisaAndClaim = otherProjectVisaAndClaim[i];
            let VisaAndClaimMx = {};
            VisaAndClaimMx.Xh = VisaAndClaim.dispNo;
            VisaAndClaimMx.Mc = VisaAndClaim.project;
            VisaAndClaimMx.Dw = VisaAndClaim.type;
            VisaAndClaimMx.Zdje = VisaAndClaim.total;
            VisaAndClaimMx.Csxs = NumberUtil.getDefault(VisaAndClaim.price);
            VisaAndClaimMx.Jxse = NumberUtil.getDefault(VisaAndClaim.amount);
            VisaAndClaimMx.Bz = VisaAndClaim.zhPrice;
            VisaAndClaimArray.push(VisaAndClaimMx)
        }
        VisaAndClaim.VisaAndClaimMx = VisaAndClaimArray;
        单位工程.VisaAndClaim = VisaAndClaim;
    }

    //计日工
    async convertJrg(单位工程, otherProjectDayWorks) {
        let Jrg ={}
        if(ObjectUtils.isEmpty(otherProjectDayWorks)){
            单位工程.jrg =Jrg;
            return;
        }
        let parentList = otherProjectDayWorks.filter(item =>item.parentId === null);
        let sonList = otherProjectDayWorks.filter(item =>item.parentId !== null);

        let JrgMxS  = new Array();
        if(ObjectUtils.isEmpty(parentList)){
            //如果没有标题只生成明细数据
            if(!ObjectUtils.isEmpty(sonList)){

                for (let i = 0; i < sonList.length; i++) {
                    let sonListElement = sonList[i];
                    let  JrgMx = {};
                    JrgMx.Xh = sonListElement.dispNo;
                    JrgMx.Mc = sonListElement.worksName;
                    JrgMx.Dw = sonListElement.unit;
                    JrgMx.Zdsl = sonListElement.tentativeQuantity;
                    JrgMx.Zhdj = sonListElement.price;
                    JrgMx.Zhhj = sonListElement.total;
                    JrgMx.Csxs = sonListElement.taxRemoval;
                    JrgMx.Jxse = sonListElement.jxTotal;
                    JrgMxS.push(JrgMx)
                }
                单位工程.JrgMx = JrgMxS;
                return ;
            }
        }else {
            let JrgBtArray = new Array();
            for (let i = 0; i < parentList.length; i++) {
                let parentListElement = parentList[i];
                let JrgBt = {};
                JrgBt.Xh = parentListElement.dispNo;
                JrgBt.Mc = parentListElement.worksName;
                JrgBt.Je = parentListElement.total;
                JrgBt.LB = ''
                if(JrgBt.Mc!== undefined){
                    //遍历枚举
                    for(let key in CostTypeJrgEnum){
                        if(JrgBt.Mc === CostTypeJrgEnum[key].desc){
                            JrgBt.LB = CostTypeJrgEnum[key].code;
                        }
                    }
                }
                let JrgMxArray  = new Array();
                //获取当前标题下的子项
                let filter = sonList.filter(item => item.parentId === parentListElement.sequenceNbr );

                if(!ObjectUtils.isEmpty(filter)){
                    for (let j = 0; j < filter.length; j++) {
                        let filterElement = filter[j];
                        let  JrgMx = {};
                        JrgMx.Xh   = filterElement.dispNo;
                        JrgMx.Mc   = filterElement.worksName;
                        //规格型号
                        JrgMx.Ggxh = filterElement.specification;
                        JrgMx.Dw   = filterElement.unit;
                        JrgMx.Zdsl = filterElement.tentativeQuantity;
                        JrgMx.Zhdj = filterElement.price;
                        JrgMx.Zhhj = filterElement.total;
                        JrgMx.Csxs = filterElement.taxRemoval;
                        JrgMx.Jxse = filterElement.jxTotal;
                        JrgMxArray.push(JrgMx)

                    }
                    JrgBt.JrgMx = JrgMxArray;
                }
                JrgBtArray.push(JrgBt)
            }
            Jrg.JrgBt =JrgBtArray;
            单位工程.Jrg =Jrg;
        }

    }



    //人材机汇总
    async convertRcjhz(单位工程, unitProject) {

        let 人材机汇总表 ={};

        let rcjArray =this.rcjArray;

        if(ObjectUtils.isEmpty(rcjArray)){
            单位工程.人材机汇总表 =人材机汇总表;
            return
        }
        //将二次解析的父级过滤掉
        let rcjArrayFilter = rcjArray.filter(i=>!(i.markSum ===1 && (i.levelMark ===RcjLevelMarkConstant.SINK_JX ||  i.levelMark ===RcjLevelMarkConstant.SINK_PB)));

        if(ObjectUtils.isEmpty(rcjArrayFilter)){
            单位工程.人材机汇总表 =人材机汇总表;
            return
        }
        let 人材机子目Array = new Array();
        for (let i = 0; i < rcjArrayFilter.length; i++) {
            let rcj = rcjArrayFilter[i];
            let 人材机子目 = {};
            人材机子目.序号 = i+1;
            人材机子目.编码 = rcj.materialCode;
            人材机子目.类型 = rcj.kind;
            人材机子目.名称 = rcj.materialName;
            人材机子目.计量单位 = rcj.unit;
            人材机子目.数量 = rcj.totalNumber;
            人材机子目.预算价 = rcj.dePrice;
            人材机子目.除税系数 =rcj.taxRemoval
            人材机子目.市场价 = rcj.marketPrice;
            人材机子目.除税市场价 = NumberUtil.costPriceAmountFormat( NumberUtil.subtract(rcj.市场价,NumberUtil.multiply(rcj.市场价,rcj.taxRemoval)));//取 人材机汇总对应人材机 市场价列 - 进项税额列
            人材机子目.结算价 = rcj.marketPrice; //取 人材机汇总对应人材机 市场价列
            人材机子目.暂估标志 = rcj.ifProvisionalEstimate==1?1:0  //取 人材机汇总对应人材机暂估状态，勾选展示为1，未勾选展示为0
            人材机子目.主要材料标志 ='主要材料标志'//todo
            人材机子目.规格型号 = rcj.specification;
            人材机子目.质量等级 = rcj.qualityGrade;
            人材机子目.厂家 = rcj.manufactor;
            人材机子目.产地 = rcj.producer;
            人材机子目.含税合价 = rcj.total;
            人材机子目.除税合价 = NumberUtil.subtract(rcj.total,rcj.jxTotal);
            人材机子目.销项税额合计 = NumberUtil.costPriceAmountFormat( NumberUtil.multiply( rcj.total,unitProject.projectTaxCalculation.outputTaxRate));
            人材机子目.进项税额合计 = rcj.jxTotal;
            人材机子目.供应时间 = rcj.jxTotal;
            人材机子目.送达地点 = rcj.deliveryLocation;
            人材机子目.备注 = rcj.description;
           

            人材机子目Array.push(人材机子目)
        }
        人材机汇总表.人材机子目 = 人材机子目Array;
        单位工程.人材机汇总表 = 人材机汇总表;
    }


    //增值税进项税汇总表
    async convertZzsjxshzb(单位工程, inputTaxDetails) {
        let 增值税进项税额计算汇总表 ={};
        //查询 增值税进项税额计算汇总数据
        let 进项税额费用子目Array = new Array();
        if(inputTaxDetails){
            for (let i = 0; i < inputTaxDetails.length; i++) {
                let findElement = inputTaxDetails[i];
                let 进项税额费用子目 = {};
                进项税额费用子目.序号 =i+1+"";
                进项税额费用子目.名称 =findElement.name;
                进项税额费用子目.计算基础 =findElement.calculateFormula;
                进项税额费用子目.除税系数 =ObjectUtils.isEmpty(findElement.rate)?100:findElement.rate;
                进项税额费用子目.金额 =findElement.price
                进项税额费用子目.备注 =findElement.remark;

                进项税额费用子目Array.push(进项税额费用子目)
            }
        }

        增值税进项税额计算汇总表.进项税额费用子目 = 进项税额费用子目Array;
        单位工程.增值税进项税额计算汇总表 = 增值税进项税额计算汇总表;
    }

    /**
     * 主要材料设备
     * 红成  如果勾选了
     * @param 单位工程
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZyClSb(单位工程, unitProject) {
        let ZyClSb = {}
        if(ObjectUtils.isEmpty(this.rcjArray)){
            单位工程.ZyClSb= ZyClSb;
            return;
        }
        let res =  this.rcjArray.filter(i=>i.mainMaterial ==1);//主要材料 设备表逻辑
        if(ObjectUtils.isEmpty(res)){
            单位工程.ZyClSb= ZyClSb;
            return;
        }

        //将二次解析的父级过滤掉
        let rcjArrayFilter = res.filter(i=>!(i.markSum ===1 && (i.levelMark ===RcjLevelMarkConstant.SINK_PB ||  i.levelMark ===RcjLevelMarkConstant.SINK_JX)));
        let ZyClSbMxArray = new Array();
        for (let i = 0; i < rcjArrayFilter.length; i++) {
            let rcj = rcjArrayFilter[i];
            let ZyClSbMx = {};
            ZyClSbMx.Xh = rcj.dispNo;
            ZyClSbMx.RcjId  = rcj.standardId;
            ZyClSbMx.Clbh   = rcj.materialCode;
            ZyClSbMx.Mc     = rcj.materialName;
            ZyClSbMx.Ggxh   = rcj.specification;
            ZyClSbMx.Dw     = rcj.unit;
            ZyClSbMx.Sl     = rcj.totalNumber;
            ZyClSbMx.Dj     = rcj.dePrice;
            ZyClSbMx.Hj     = rcj.total;
            ZyClSbMx.Bz     = rcj.description;
            //主要材料设备类别lqs
            ZyClSbMx.Lb     = this.judgeRcjhzLb(rcj.ifDonorMaterial);
            for(let key in CostTypeSbLbEnum){
                if(rcj.materialCategory === CostTypeSbLbEnum[key].desc){
                    ZyClSbMx.Lb = CostTypeSbLbEnum[key].code;
                }
            }

            ZyClSbMxArray.push(ZyClSbMx);
        }
        ZyClSb.ZyClSbMx = ZyClSbMxArray;
        单位工程.ZyClSb= ZyClSb;
    }

    //招标人材料设备明细表
    async convertZbrClSb(单位工程, unitProject) {
        let ZbrClSb = {}
        let res =  this.rcjArray;
        if (ObjectUtils.isEmpty(res)){
            单位工程.ZbrClSb = ZbrClSb;
            return;
        }
        let jgRcj = res.filter(item => item.ifDonorMaterial === 1);
        if (ObjectUtils.isEmpty(jgRcj)){
            单位工程.ZbrClSb = ZbrClSb;
            return;
        }else {
            let ZbrClSbMxArray = new Array();
            for (let i = 0; i < jgRcj.length; i++) {
                let rcj = jgRcj[i];
                let ZbrClSbMx = {};
                ZbrClSbMx.Xh =    rcj.dispNo;
                ZbrClSbMx.RcjId  = rcj.standardId;
                ZbrClSbMx.Mc     = rcj.materialName;
                ZbrClSbMx.Ggxh   = rcj.specification;
                ZbrClSbMx.Dw     = rcj.unit;
                ZbrClSbMx.Sl     = rcj.totalNumber;
                ZbrClSbMx.Dj     = rcj.dePrice;
                ZbrClSbMx.Hj     = rcj.total;
                ZbrClSbMx.Zldj   = rcj.qualityGrade;
                ZbrClSbMx.Gysj   = '';
                ZbrClSbMx.Sddd   = rcj.deliveryLocation;
                ZbrClSbMx.Bz     = rcj.description;
                ZbrClSbMx.Lb     = rcj.kind === 4? 2: 1 ;
                ZbrClSbMxArray.push(ZbrClSbMx);
            }
            ZbrClSb.ZbrClSbMx = ZbrClSbMxArray;
            单位工程.ZbrClSb = ZbrClSb;
        }


    }

    //安全文明施工费
    async convertAqwmsgf(单位工程, unitProject) {
        let 安全文明施工费列表 ={};

        let args ={};
        args.constructId = unitProject.constructId;
        args.singleId = unitProject.spId;
        args.unitId = unitProject.sequenceNbr;
        let safeFee = await this.service.safeFeeService.getSafeFee(args);
        if(ObjectUtils.isEmpty(safeFee)){
            单位工程.安全文明施工费列表 = 安全文明施工费列表;
            return
        }

        let 安全文明施工费用子目Array = new Array();
        for (let i = 0; i < safeFee.length; i++) {
            let safeFeeElement = safeFee[i];
            let 安全文明施工费用子目 ={};

            安全文明施工费用子目.序号 = i+1+"";
            安全文明施工费用子目.取费专业编码 = safeFeeElement.feeCode;
            安全文明施工费用子目.取费专业名称 = safeFeeElement.costMajorName;
            安全文明施工费用子目.取费基数 = 'FBFXHJ+CSXMHJ+QTXMHJ+GF'
            安全文明施工费用子目.取费基数金额 =safeFeeElement.costFeeBase ;
            安全文明施工费用子目.费率 = safeFeeElement.basicRate;
            安全文明施工费用子目.金额 = safeFeeElement.feeAmount;

            安全文明施工费用子目Array.push(安全文明施工费用子目);
        }
        安全文明施工费列表.安全文明施工费用子目 = 安全文明施工费用子目Array;
        单位工程.安全文明施工费列表 = 安全文明施工费列表;
    }

    //规费
    async convertGf(单位工程, unitProject) {
        let 规费列表 ={};
        let args ={};
        args.constructId = unitProject.constructId;
        args.singleId = unitProject.spId;
        args.unitId = unitProject.sequenceNbr;
        let gfeeFee = await this.service.gfeeService.getGfeeFee(args);

        if(ObjectUtils.isEmpty(gfeeFee)){
            单位工程.规费列表 = 规费列表;
            return;
        }
        let 规费子目Array = new Array();
        for (let i = 0; i < gfeeFee.length; i++) {
            let 规费子目 = {};
            let gfeeFeeElement = gfeeFee[i];
            规费子目.序号 = i+1+"";
            规费子目.取费专业编码 =gfeeFeeElement.feeCode;
            规费子目.取费专业名称 = gfeeFeeElement.costMajorName;
            规费子目.取费基数 = ObjectUtils.isEmpty(unitProject.feeCalculateBaseList.find(item=>item.type =='gf'))?'RGF_DEJ+JXF_DEJ':unitProject.feeCalculateBaseList.find(item=>item.type =='gf').code;
            规费子目.取费基数金额 = gfeeFeeElement.costFeeBase;
            规费子目.费率 = gfeeFeeElement.gfeeRate;
            规费子目.金额 = gfeeFeeElement.feeAmount;
            规费子目Array.push(规费子目)
        }
        规费列表.规费子目 = 规费子目Array;
        单位工程.规费列表 = 规费列表;

    }

    /**
     * 转换Dekbz
     * @returns {Promise<void>}
     */
    async convertDekbz(dekbz){

        if(ObjectUtils.isEmpty(dekbz)){
            return '';
        }else {
            let res = '';
            switch (dekbz) {
                case '河北省建筑工程消耗量定额（2012）' :
                    res= '12jz';
                    break;
                case '河北省装饰装修工程消耗量定额（2012）' :
                    res= '12zs';
                    break;
                case '河北省安装工程消耗量定额（2012）' :
                    res= '12az';
                    break;
                case '河北省市政工程消耗量定额（2012）' :
                    res= '12sz';
                    break;
                case '河北省仿古建筑工程消耗量定额（2013）' :
                    res= '13fg';
                    break;
                case '河北省园林绿化工程消耗量定额（2013）' :
                    res= '13yl';
                    break;
                case '河北省房屋修缮工程消耗量定额（土建分册）（2013）' :
                    res= '13xsjz';
                    break;
                case '河北省房屋修缮工程消耗量定额（安装分册）（2013）' :
                    res= '13xsaz';
                    break;
                case '河北省市政设施维修养护工程消耗量定额（2013）' :
                    res= '13szyh';
                    break;
                case '河北省城市园林绿化养护管理定额（2014）' :
                    res= '13ylyh';
                    break;
                case '古建（明清）修缮工程消耗量定额（2014）' :
                    res= '14gjxs';
                    break;
                case '京津翼城市地下综合管廊工程消耗量定额（2018）' :
                    res= '18gl';
                    break;
                case '城市轨道交通工程预算定额河北省消耗量定额（2015）' :
                    res= '15gd';
                    break;
                case '河北省人防工程预算定额（2015）' :
                    res= '15rf';
                    break;
                case '河北省装配式建筑工程定额（试行）（2018）' :
                    res= '18zp';
                    break;
                case '独立费' :
                    res= '独';
                    break;
                case '自编定额及企业定额等' :
                    res= '其他';
                    break;
                case '2022年《河北省建设工程消耗量标准》-建筑工程' :
                    res= '22jz';
                    break;
                case '2022年《河北省建设工程消耗量标准》-装饰装修工程' :
                    res= '22zs';
                    break;
                case '2022年《河北省建设工程消耗量标准》-安装工程' :
                    res= '22az';
                    break;
                case '2022年《河北省建设工程消耗量标准》-市政工程' :
                    res= '22sz';
                    break;
                default :
                    res = '';
            }
            return  res;
        }
    }

    /**
     * 用所有人材机构成
     * @param rcjList
     * @param rcjDetailList
     * @returns {Promise<void>}
     */
    async convertRcjMap(rcjList) {



        if(ObjectUtils.isEmpty(rcjList)){
            return new Map();
        }
        return  ArrayUtil.group(rcjList,'deId');


    }


    /**
     * Lb主要材料设备类别判断
     */
    judgeRcjhzLb(number) {

        return '1';


    }

    judgeRcjlbMx(number) {

        return '1';

    }


}

JsonToXmlHZBService.toString = () => '[class JsonToXmlHZBService]';
module.exports = JsonToXmlHZBService;
