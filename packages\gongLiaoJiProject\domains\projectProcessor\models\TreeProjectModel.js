const { TreeNode } = require('../../../core/tools/ConstructTreeMap');
const BranchProjectDisplayConstant = require('../../../constants/BranchProjectDisplayConstant');
const {ProjectTaxCalculation} = require("../../../models/GljProjectTaxCalculation");


class TreeProjectModel extends TreeNode{

  sequenceNbr;
  type;// project single unit
  parentId;
  displaySign;
  children;
  name;
  code;
  constructMajorType;  // 单位工程专业类型
  qfMajorType; // 取费专业类型
  deLibrary;
  BCRGFCount = 0;  //记录补充人工数量
  BCCLFCount = 0;  //记录补充材料数量
  BCJXFCount = 0;  //记录补充机械数量
  BCZCFCount = 0;  //记录补充主材数量
  BCSBFCount = 0;  //记录补充设备数量

  deStandardReleaseYear; // 定额标准发布年份
  projectTaxCalculation;// 计税方式


  constructor(sequenceNbr,type,parentId = null) {
    super();
    this.sequenceNbr = sequenceNbr;
    this.type = type;
    this.parentId = parentId;
    this.displaySign = BranchProjectDisplayConstant.noSign
    this.children = [];
  }
  init(value){
    for (const valueKey in value) {
      this[valueKey]= value[valueKey];
    }
  }

}
TreeProjectModel.toString = () => 'TreeProjectModel';
module.exports = TreeProjectModel;

