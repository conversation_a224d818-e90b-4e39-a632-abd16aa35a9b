<!--
 * @Descripttion: 补充清单
 * @Author: liuxia
 * @Date: 2023-05-25 17:52:23
 * @LastEditors: wangru
 * @LastEditTime: 2025-02-20 17:28:53
-->
<template>
  <common-modal
    className="dialog-comm"
    title="补充清单"
    width="auto"
    v-model:modelValue="props.qdVisible"
    :destroyOnClose="true"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="form-wrap">
      <div class="form-content">
        <a-spin
          tip="解析中..."
          :spinning="spinning"
        >
          <a-form
            :model="inputData"
            ref="form"
            @finish="onSubmit"
          >
            <a-form-item
              label="清单编码"
              name="bdCode"
              class="form-item-two"
              :rules="[
                {
                  required: true,
                  message: '请输入清单编码!',
                },
              ]"
            >
              <a-input
                v-model:value.trim="inputData.bdCode"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="单位"
              class="form-item-two"
              name="unit"
              :rules="[{ required: true, message: '请选择单位!' }]"
            >
              <vxeTableEditSelect
                :filedValue="inputData.unit"
                :list="projectStore.unitListString"
                :isNotLimit="true"
                @update:filedValue="
                  newValue => {
                    saveCustomInput(newValue, inputData, 'unit', $rowIndex);
                  }
                "
              ></vxeTableEditSelect>
            </a-form-item>
            <a-form-item
              label="清单名称"
              name="bdName"
              class="form-item-one"
              :rules="[{ required: true, message: '请输入清单名称!' }]"
            >
              <a-input
                v-model:value="inputData.bdName"
                placeholder="请输入清单名称"
              />
            </a-form-item>
            <a-form-item
              label="工程量表达式"
              name="quantityExpression"
              class="form-item-one"
              :rules="[
                { validator: checkQuantityExpression, trigger: 'change' },
              ]"
            >
              <a-input
                v-model:value.trim="inputData.quantityExpression"
                placeholder="请输入工程量表达式"
                @blur="expressionBlur"
              />
            </a-form-item>
            <a-form-item class="form-item-one">
              <div class="footer-btn-list">
                <a-button
                  type="primary"
                  ghost
                  @click="cancel"
                >取消</a-button>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="spinning || loading"
                >新建</a-button>
              </div>
            </a-form-item>
          </a-form>
        </a-spin>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import api from '../../../../../api/projectDetail.js';
import {
  isNumericExpression,
  everyNumericHandler,
  quantityExpressionHandler,
} from '@/utils/index';
import { ref, reactive, watch, onMounted, toRaw, toRefs } from 'vue';
import infoMode from '../../../../../plugins/infoMode';
import { projectDetailStore } from '@/store/projectDetail.js';
import { useBcData } from '@/hooks/useBcData.js';

const form = ref();
const loading = ref(false);
const spinning = ref(false);
const emit = defineEmits(['update:qdVisible', 'onSuccess', 'saveData']);

const checkQuantityExpression = (rule, value) => {
  if (value === null || !value.length) return Promise.resolve();
  const [isSuccess, msg] = quantityExpressionHandler(inputData);
  if (isSuccess) {
    return Promise.reject(msg);
  }
  return Promise.resolve();
};

const expressionBlur = () => {
  inputData.quantityExpression = everyNumericHandler(
    inputData.quantityExpression
  );
};

const lists = reactive({
  lists: [],
});
const props = defineProps({
  qdVisible: {
    type: Boolean,
    default: false,
  },
  code: {
    type: String,
    default: '',
  },
  currentInfo: {
    type: Object,
    default: null,
  },
  type: {
    type: Number,
    default: 1,
  },
});

const projectStore = projectDetailStore();

const colStyle = reactive({
  colSize: null,
});

const inputData = reactive({
  bdName: null, //项目名称
  bdCode: null, //项目编码
  unit: null, // 单位
  quantityExpression: null, // 工程量表达式
  type: 1, // 1 分部分项 2 措施项目
});

onMounted(() => {});

// input框输入值置为空
const reset = () => {
  loading.value = false;
  for (let key in inputData) {
    inputData[key] = null;
  }
};

let { defaultCodeColl, bcCode } = useBcData();

const cancel = () => {
  emit('bcCancel', 1);
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};

watch(
  () => [props.qdVisible, bcCode.value],
  (val, oldVal) => {
    if (val) {
      lists.lists = [];
      form.value?.resetFields();
      reset();
      console.log('补充清单编码', props.code);
      inputData.bdCode = props.code;
      if (!props.code) {
        defaultCodeColl(1);
      }
      if (!props.code && bcCode.value) {
        inputData.bdCode = bcCode.value;
      }
    }
  }
);

const onSubmit = () => {
  console.log('onsubmit');
  loading.value = true;
  inputData.type = props.type;
  inputData.bdName = inputData.bdName.trim();
  isStandQd();
};

// 判断是否是标准清单
const isStandQd = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    code: inputData.bdCode,
  };
  api.isStandQd(apiData).then(res => {
    if (res.status === 200) {
      if (!res.result) {
        emit('saveData', inputData);
      } else {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-querenshanchu',
          infoText: '清单编码不可与标准清单相同,请重新输入',
          confirm: () => {
            infoMode.hide();
            loading.value = false;
          },
        });
      }
    }
  });
};
</script>
<style lang="scss" scoped>
@import './style.scss';
</style>
