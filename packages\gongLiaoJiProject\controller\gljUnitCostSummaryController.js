const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const ProjectDomain = require("../domains/ProjectDomain");

class GljUnitCostSummaryController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取默认费用汇总
     * @returns {ResponseData}
     */
    async defaultUnitCostSummary(args) {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 获取费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async getUnitCostSummaryList(args) {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummaryList(args);
        return ResponseData.success(res);
    }

    /**
     * 获取局部费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async getUnitPartCostSummaryList(args) {
        let params = {
            constructId: args.constructId,
            unitId: args.unitId,
            singleId: args.singleId
        }
        args.qfMajorType = args.constructMajorType

        // let costSummaryMajorMenuList = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorMenuList(params)
        // let unitProject = ProjectDomain.getDomain(args.constructId).getProjectById(args.unitId);
        // if (unitProject.isSingleMajorFlag !== false) {
        //     args.isCostSummary = true;
        //     const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitPartCostSummaryList(args);
        //     return ResponseData.success(res);
        // }
        if (ObjectUtils.isEmpty(args.constructMajorType)) {
            // 局部多专业汇总
            const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getPartCostSummaryMajorsTotal(args);
            return ResponseData.success(res);
        }
        args.isCostSummary = true;
        const res = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.countCostCodePrice2(args)
        return ResponseData.success(res);
    }

    /**
     * 导出局部费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportUnitPartCostSummary(args) {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.exportUnitPartCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 获取计算后费用汇总
     * @param args
     * @returns {ResponseData}
     */
    countUnitCostSummary(args) {
        const res = this.service.gongLiaoJiProject.gljUnitCostSummaryService.countUnitCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 插入费用汇总
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async addCostSummary(args, redo="插入- -费用项") {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.addCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 删除费用汇总
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteCostSummary(args, redo="删除- -费用项") {
        const res = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.deleteCostSummary(args);
        return ResponseData.success(res);
    }

    /**
     * 保存或者修改费用汇总
     * @param args
     * @returns {*}
     */
    async saveCostSummary(args,redo="编辑- -费用项") {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.saveCostSummary(args);
    }

    /**
     * 获取工程专业下拉框
     * @param args
     * @returns {ResponseData}
     */
    async getConstructMajorTypeEnum(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getConstructMajorTypeEnum(args));
    }

    /**
     * 获取费用汇总左侧树
     * @param args
     * @returns {ResponseData}
     */
    async getCostSummaryMajorMenuList(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getCostSummaryMajorMenuList(args));
    }

    /**
     * 多专业汇总-多专业汇总补充工程专业
     * @param args
     * @returns {ResponseData}
     */
    async supplyCostSummaryMajors(args, redo="设置- -多专业取费") {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.supplyCostSummaryMajors(args);
    }

    /**
     * 导入费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async importUnitCostSummary(args, redo="载入- -费用汇总模板") {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.importUnitCostSummary(args);
    }

    /**
     * 导出费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async exportUnitCostSummary(args) {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.exportUnitCostSummary(args);
    }

    /**
     * 获取费用汇总应用范围
     * @param args
     */
    async scopeOfApplicationsCostSummary(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.scopeOfApplicationsCostSummary(args));
    }

    /**
     * 批量应用费用汇总
     * @param args
     * @returns {ResponseData}
     */
    async batchApplicationsCostSummary(args, redo="批量应用- -费用汇总") {
        return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.batchApplicationsCostSummary(args);
    }

    /**
     * 获取安文费明细汇总
     * @param args
     * @returns {Promise<*>}
     */
    async getAWFSummary(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getAWFSummary(args));
    }

    /**
     * 获取是否进行多专业汇总
     * @param args
     * @returns {Promise<*>}
     */
    async getIsSingleMajorFlag(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getIsSingleMajorFlag(args));
    }

    /**
     * 获取当前单位的取费专业
     * @param args
     * @returns {Promise<*>}
     */
    async getQfMajorTypeByUnit(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getQfMajorTypeByUnit(args));
    }
}

GljUnitCostSummaryController.toString = () => '[class GljUnitCostSummaryController]';
module.exports = GljUnitCostSummaryController;