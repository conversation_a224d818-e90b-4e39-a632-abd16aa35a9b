/*
 * @Author: wa<PERSON><PERSON>
 * @Date: 2023-05-29 09:34:55
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-17 18:00:47
 */
import { defineStore } from 'pinia';
import detailApi from '@/api/projectDetail.js';

export const projectDetailStore = defineStore('projectDetail', {
  state: () => {
    return {
      pricingMethod: 0, // 组价方式，1，按含税市场价组价
      projectType: 'ZB', //项目类型， ZB,TB,DW
      type: '', // ys：预算 yssh 预算审核 js 结算 jssh 结算审核 gs 概算
      projectName: '', //当前项目名称
      mainContentRefresh: true, // main-content组件重新加载
      mainContentComponentRefresh: false, // 修改送审main-content组件重新加载
      asideTreeData: null, // 左侧树结构
      currentTreeInfo: null, // 左侧树形结构当前点击信息
      constructConfigInfo: null, // 当前项目配置信息
      asideMenuCurrentInfo: null, // 侧边菜单当前选中信息
      feeWithDrawalInfo: null, //取费表侧边选中取费文件信息
      queryCostAnalysisCode: null, //查询造价分析接口传参的code
      subCurrentInfo: null, // 分部分项当前选中数据
      subCurrentMaterialInfo: null, // 分部、措施当前选中得人材机明细
      tabSelectName: null, //项目详情上部tab栏选中的类型名称
      currentTreeGroupInfo: null, // 当前点击左侧树形结构的所有父级
      proCheckTab: null, //左侧项目点击选中的tab--除单项工程（造价分析)项目
      gljCheckTab: null, //工料机左侧项目点击选中的tab
      biddingType: null, //进入项目详情获取到的biddingType决定单位取费表的计税方式是否可选
      unitListString: null, //其他项目单位列表字符串
      dataType: null, //其他项目总承包服务费和计日工选中行数据的dataType
      otherProCopyInfo: null, //其他项目子页面复制行信息
      summaryCopyInfo: null, //费用汇总子页面复制行信息
      humanUpdataData: null, //人材机汇总工程项目级别编辑的数据
      isFeeCollection: false, //取费表是否修改
      componentId: null, //当前选中componentId
      isRefreshProjectTree: false, // 是否刷新左侧项目树
      isFileSaveAs: false, //是否另存为-需要刷新
      isRefreshBaseInfo: false, //是否刷新项目概况中的名称
      userInfoList: null, //登录获取到的用户身份列表信息
      loginUserInfo: null, //目前登录用户信息
      hasOpenWinList: null, //目前已经打开的窗口列表
      lastInfo: null, //上次登录信息
      globalLoading: { loading: false, info: '数据加载中...' }, //子窗口全局loading
      maingWinLoading: false, //主窗口全局loading
      childComponentRef: null, // 项目详情组件ref,
      headerMenu: null, // 头部tab栏ref
      tabMenuRef: null, // 项目详情上部tab栏ref
      quotaInfoTabsRef:null,  //底部ref
      asideTreeRef: null, // 侧边树形结构ref
      asideMenuRef: null, // 其他项目左侧tab栏ref
      isAutoPosition: false, // 是否是自动切换项目
      subItemProjectAutoPosition: null, // 分部自动切换方法
      measuresItemProjectAutoPosition: null, // 措施自动
      dailyWorkProjectAutoPosition: null, // 计日工自动
      serviceProjectAutoPosition: null, // 总承包服务费自动
      summaryProjectAutoPosition: null, // 人材机汇总自动
      otherProjectAutoPosition: null, // 其它项目自动
      feeWithDrawalAutoPosition: null, //取费刷新
      costAnalysisComponentRef: null,
      materialRef: null, // 人材机明细ref
      positionId: null, // 反向定位id，用于分部分项和措施项目
      combinedSearchList: [], // 组价筛选值
      combinedVisible: false, // 组价筛选弹框是否打开
      summaryExpenseGetList: null, // 费用汇总接口
      measuresItemGetList: null, // 措施项目接口
      otherProItemGetList: null, // 其他项目接口
      checkCgZsIdList: [
        '1658012394309423165',
        '1658012394317811727',
        '1658012394112290838',
        '1658012394158428199',
        '1658012394158428213',
        '1658012394162622477',
        '1658012394250702899',
        '1658012394250702918',
        '1658012394250702942',
        '1658012394254897182',
        '1658012394355560495',
      ], // 超高，装饰，等id
      deType: '12', //定额类型   12-12定额，22-22定额
      taxMade: '1', //计税方式   1-一般计税  0-简易计税
      moveRow: {
        isTree: false,
        tableData: [],
        useRowList: [],
      }, //上下移动的数据
      currentSelectRow: null,
      AsideMenuExpose: {},
      moveRowList: [], //上下移动的数据
      asideTreeCopyInfo: null, //左侧树右键复制信息
      standardGroupOpenInfo: {
        isOpen: false, //是否打开子工作台
        info: null, //打开子工作台是需要存储的数据
        selectProjectId: null, //打开子工作台前选中的单位id
        type: 'parentPage', //打开标准组价子工作台还是主工作台
        modalTip: '', //进入子窗口弹窗信息
        treeGroup: {}, //进入子窗口单位，单项，工程项目id,
        oldTreeInfo: {
          currentTreeInfo: null,
          currentTreeGroupInfo: null,
        },
      }, //标准组价后子工作台信息
      isOpenIndexModal: {
        open: false,
        tab: null,
      }, //是否打开清单定额索引弹框
      isOpenUpdateModal: false, //检查更新弹框是否打开
      checkColorList: [], // 过滤筛选选择的颜色值
      globalSettingInfo: null, // 全局设置信息
      updateSS: false, //修改送审按钮点击tab需要切换至分部分项
      currentStageInfo: null, // 人材机调整中当前选中的分期数信息
      stageCount: 0, // 获取当前单位分期次数
      isAddUnit: false, //是否添加单位
      loginPhone: localStorage.getItem('loginPhone') || null, // 登录成功时手机号
      customMenu: '', // 报表点击右键菜单
      checkVisible: false, // 项目自检弹框是否显示
      reportNextOption: false, // 导出xml项目自检后下一步操作
      //编辑区切换项目特征等单行展示、多行展示，默认多行展 true
      rowHeight: true,
      startMatch: false, //开始组价---开始组价页面定时器关闭，组价结束重新开启
        setOption: {
            isScj: false,
        },
        isFirstCheckRangeScope: true,
        convenienceSettings: null, // 便捷设置
      constructReadOnlyStatus: false, // 工程项目是否只读状态
    };
  },
  getters: {
    deStandardReleaseYear(state) {
      return state.currentTreeInfo?.deStandardReleaseYear || state.deType;
    },
  },
  actions: {
    SET_TYPE(type) {
      this.type = type;
    },
    SET_PROJECT_NAME(name) {
      this.projectName = name;
    },
    SET_CURRENT_TREE_INFO(info, type) {
      // 如果为概算项目
      // if(type===1){
      //   info.id=info.sequenceNbr
      // }
      this.currentTreeInfo = info;
    },
    SET_CONSTRUCT_CONFIG_INFO(info) {
      this.constructConfigInfo = info;
    },
    SET_ASIDE_MENU_CURRENT_INFO(info) {
      this.asideMenuCurrentInfo = info;
    },
    SET_Fee_With_Drawal_Info(info) {
      this.feeWithDrawalInfo = info;
      console.log(this.feeWithDrawalInfo, 'store.feeWithDrawalInfo');
    },
    SET_Cost_Analysis_Code(code) {
      this.queryCostAnalysisCode = code;
    },
    SET_SUB_CURRENT_INFO(info) {
      this.subCurrentInfo = info;
    },
    SET_TAB_SELECT_NAME(info) {
      this.tabSelectName = info;
    },
    SET_CURRENT_TREE_GROUP_INFO(info) {
      this.currentTreeGroupInfo = info;
    },
    SET_PRO_CHECK_TAB(info) {
      this.proCheckTab = info;
    },
    SET_GLJ_CHECK_TAB(info) {
      this.gljCheckTab = info;
    },
    SET_PRO_BIDDINGTYPE(info) {
      this.biddingType = info;
    },
    SET_COMPONENT_ID(info) {
      this.componentId = info;
    },
    SET_UNITLISTSTRING(info) {
      this.unitListString = info;
    },
    SET_DATATYPE(info) {
      this.dataType = info;
    },
    SET_OTHERPRO_COPYINFO(info) {
      this.otherProCopyInfo = info;
    },
    SET_SUMMARY_COPYINFO(info) {
      this.summaryCopyInfo = info;
    },
    SET_HUMAN_UPDATA_DATA(info) {
      this.humanUpdataData = info;
    },
    SET_IS_REFRESH_PROJECT_TREE(info) {
      this.isRefreshProjectTree = info;
    },
    SET_IS_FILE_SAVE_AS(info) {
      this.isFileSaveAs = info;
    },
    SET_IS_REFRESH_BASE_INFO(info) {
      this.isRefreshBaseInfo = info;
    },
    SET_IS_USER_INFO_LIST(info) {
      this.userInfoList = info;
    },
    SET_IS_LOGIN_USER_INFO(info) {
      this.loginUserInfo = info;
    },
    SET_IS_OPEN_WIN_LIST(info) {
      this.hasOpenWinList = info;
    },
    SET_LAST_INFO(info) {
      this.lastInfo = info;
    },
    SET_GLOBAL_LOADING(info) {
      this.globalLoading = info;
    },
    SET_POSITION_ID(info) {
      this.positionId = info;
    },
    SET_COMBINED_SEARCH_LIST(info) {
      this.combinedSearchList = info;
    },
    SET_COMBINED_VISIBLE(info) {
      this.combinedVisible = info;
    },
    SET_ASIDETREE_COPY_INFO(info) {
      this.asideTreeCopyInfo = info;
    },
    SET_STANDARD_GROUP_OPEN_INFO(info) {
      this.standardGroupOpenInfo = info;
    },
    SET_SUB_CURRENT_MATERIAL_INFO(info) {
      this.subCurrentMaterialInfo = info;
    },
    SET_GLOBAL_SETTING_INFO(info) {
      this.globalSettingInfo = {...(this.globalSettingInfo || {}), ...info};
    },
    INIT_GLOBAL_SETTING_INFO(constructId) {
      // 初始化全局设置信息,由于目前全局设置接口未统一，暂切更新关联子目字段
      detailApi.queryProjectConvenientSetColl({
        constructId,
      }).then(res => {
        if (res.status === 200 && res.result) {
          console.log("🚀 ~ INIT_GLOBAL_SETTING_INFO ~ res.result:", res.result)
          this.SET_GLOBAL_SETTING_INFO(res.result)
        }
      })
    },
    async SET_CONSTRUCT_READ_ONLY_STATUS(constructId) {
      const res = await detailApi.fsIsReadOnly({constructId});
      console.log("🚀 ~ SET_IS ~ res:", res)
      this.constructReadOnlyStatus = res?.result;
    },
    SET_CURRENT_STAGE_INFO(info) {
      this.currentStageInfo = info;
    },
    SET_STAGE_COUNT(info) {
      this.stageCount = info;
    },
    SET_LOGIN_PHONE(info) {
      this.loginPhone = info;
      if (info) {
        localStorage.setItem('loginPhone', info);
      } else {
        localStorage.removeItem('loginPhone');
      }
    },
    SET_CHECK_COLOR_LIST(info) {
      this.checkColorList = info;
    },
    SET_CHECK_VISIBLE(info) {
      this.checkVisible = info;
    },
    //切换是否多行
    SET_ROW_HEIGHT() {
      this.rowHeight = !this.rowHeight;
    },

    SET_START_MATCH() {
      this.startMatch = !this.startMatch;
    },
    SET_REPORT_NEXT_OPTION(info) {
      this.reportNextOption = info;
    },
    SET_OPTION(info) {
      this.setOption = info;
    },
    SET_FIRST_SET_CHECK_RANGE(isFirst) {
      this.isFirstCheckRangeScope = isFirst;
    },
    SET_CURRENTSELECTROW(row) {
      this.currentSelectRow = row;
    },
    SET_ASIDETREE_DATA(data) {
      this.asideTreeData = data;
    },
    SET_CONVENIENCE_SETTINGS(data) {
      this.convenienceSettings = data;
    },
  },
});
