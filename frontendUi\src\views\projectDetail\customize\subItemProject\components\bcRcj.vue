<!--
 * @Descripttion: 补充人材机
 * @Author: liuxia
 * @Date: 2024-04-07 10:26:00
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-07 16:25:31
-->
<template>
  <common-modal
    className="dialog-comm"
    title="补充人材机"
    width="auto"
    v-model:modelValue="props.visible"
    :destroy-on-close="true"
    @cancel="cancel"
    @close="cancel"
  >
    <!-- align="left" -->
    <div class="form-wrap">
      <div class="form-content">
        <a-spin tip="解析中..." :spinning="spinning">
          <a-form :model="inputData" ref="form" @finish="onSubmit">
            <a-form-item
              label="材料编码"
              name="materialCode"
              class="form-item-two"
              :rules="[
                {
                  required: true,
                  message: '请输入材料编码!',
                },
              ]"
            >
              <a-input
                v-model:value.trim="inputData.materialCode"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="类型"
              class="form-item-two"
              name="kind"
              :rules="[{ required: true, message: '请选择类型!' }]"
            >
              <a-select
                v-model:value="inputData.kind"
                :options="lists.rcjTypeList"
                placeholder="请选择"
                :size="colStyle.colSize"
                @change="selectChange"
              ></a-select>
            </a-form-item>
            <a-form-item
              label="材料名称"
              name="materialName"
              class="form-item-one"
              :rules="[{ required: true, message: '请输入材料名称!' }]"
            >
              <a-input
                v-model:value="inputData.materialName"
                placeholder="请输入"
              />
            </a-form-item>

            <a-form-item
              label="单位"
              name="unit"
              class="form-item-two"
              :rules="[{ required: true, message: '请选择单位!' }]"
            >
              <vxeTableEditSelect
                :filedValue="inputData.unit"
                :list="projectStore.unitListString"
                :isNotLimit="true"
                :transfer="true"
                @update:filedValue="
                  newValue => {
                    saveCustomInput(newValue, inputData, 'unit', $rowIndex);
                  }
                "
              ></vxeTableEditSelect>
            </a-form-item>

            <a-form-item label="消耗量" name="resQty" class="form-item-two">
              <a-input
                v-model:value.trim="inputData.resQty"
                placeholder="请输入"
                @blur="formatBlur(inputData, 'resQty')"
              />
            </a-form-item>

            <a-form-item
              label="规格型号"
              name="constructName"
              class="form-item-one"
            >
              <a-input
                v-model:value.trim="inputData.specification"
                placeholder="请输入"
              />
            </a-form-item>

            <a-form-item
              :label="
                projectStore.deStandardReleaseYear === '22'
                  ? projectStore.taxMade === 1
                    ? '不含税市场价'
                    : '含税市场价'
                  : '市场价'
              "
              name="dePrice"
              class="form-item-two"
              :rules="[{ required: true, message: '请输入单价!' }]"
            >
              <a-input
                v-model:value.trim="inputData.dePrice"
                placeholder="请输入"
                @blur="formatBlur(inputData, 'dePrice')"
              />
            </a-form-item>
            <a-form-item
              label="除税系数(%)"
              name="taxRemoval"
              class="form-item-two"
              v-if="projectStore.deStandardReleaseYear === '12'"
            >
              <a-input
                v-model:value.trim="inputData.taxRemoval"
                placeholder="请输入"
                :disabled="inputData.kind === 1"
                @blur="formatBlur(inputData, 'taxRemoval')"
              />
            </a-form-item>
            <a-form-item
              label="税率"
              name="taxRemoval"
              class="form-item-two"
              v-if="
                projectStore.deStandardReleaseYear === '22' &&
                inputData.kind &&
                ![3, 6, 7, 8, 9, 10].includes(inputData.kind)
              "
            >
              <a-input
                v-model:value.trim="inputData.taxRate"
                placeholder="请输入"
                :disabled="inputData.kind === 1"
                @blur="formatBlur(inputData, 'taxRate')"
              />
            </a-form-item>
            <a-form-item class="form-item-one">
              <div class="footer-btn-list">
                <a-button type="primary" ghost @click="cancel">取消</a-button>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="spinning || loading"
                  >新建</a-button
                >
              </div>
            </a-form-item>
          </a-form>
        </a-spin>
        <vxe-table
          v-if="inputData.kind && [3, 6, 7, 8, 9, 10].includes(inputData.kind)"
          ref="vexTable"
          keep-source
          :data="tableData"
          :row-config="{ isCurrent: true, keyField: 'sortNo' }"
          :tree-config="{
            children: 'rcjDetailsDTOs',
            expandAll: true,
          }"
          height="300"
          @cell-click="
            cellData => {
              useCellClickEvent(cellData, null, [
                'materialName',
                'specification',
                'unit',
                'resQty',
                'marketPrice',
                'taxRemoval',
                'priceMarket',
                'priceMarketTax',
                'taxRate',
              ]);
            }
          "
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            beforeEditMethod: cellBeforeEditMethod,
          }"
          :cell-class-name="selectedClassName"
          @edit-closed="editClosedEvent"
          class="table-scrollbar table-edit-common"
          :menu-config="contextmenuList"
          @menu-click="onContextMenuClick"
          :scroll-y="{ enabled: true, gt: 0 }"
        >
          <vxe-column width="50" field="sortNo"> </vxe-column>
          <vxe-column tree-node title="编码" field="materialCode" width="120">
            <template #default="{ row }">
              <div>{{ row.materialCode }}</div>
            </template>
          </vxe-column>
          <vxe-column title="类别" field="type" width="100">
            <template #default="{ row }">
              <span>{{ row.type }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="名称"
            field="materialName"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.materialName }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                :maxlength="50"
                v-model="row.materialName"
                v-if="row.isFyrcj"
              />
              <span v-else>{{ row.materialName }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="规格型号"
            field="specification"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.specification }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                :maxlength="50"
                v-model="row.specification"
                v-if="row.isFyrcj"
              />
              <span v-else>{{ row.specification }}</span>
            </template>
          </vxe-column>
          <vxe-column title="单位" field="unit" width="70" :edit-render="{}">
            <template #default="{ row }">
              <span>{{ row.unit }}</span>
            </template>
            <template #edit="{ row }">
              <vxeTableEditSelect
                :transfer="true"
                :filedValue="row.unit"
                :list="projectStore.unitListString"
                @update:filedValue="
                  newValue => {
                    row.unit = newValue;
                  }
                "
                v-if="row.isFyrcj"
              ></vxeTableEditSelect>
              <span v-else>{{ row.unit }}</span>
            </template>
          </vxe-column>
          <vxe-column
            title="消耗量"
            field="resQty"
            width="90"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row }">
              <span>{{ row.resQty }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-if="row.isFyrcj"
                v-model="row.resQty"
                @blur="formatBlur(row, 'resQty')"
              />
              <span v-else>{{ row.resQty }}</span>
            </template>
          </vxe-column>

          <vxe-column
            v-if="projectStore.deStandardReleaseYear === '12'"
            :title="'市场价'"
            field="marketPrice"
            width="110"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ row.marketPrice }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-if="isEditPrice(row, 'marketPrice')"
                v-model="row.marketPrice"
                @blur="formatBlur(row, 'marketPrice')"
              />
              <span v-else>{{ row.marketPrice }}</span>
            </template>
          </vxe-column>
          <vxe-column
            v-if="projectStore.deStandardReleaseYear === '22'"
            :title="'不含税市场价'"
            field="priceMarket"
            width="110"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ isChangeAva(row) ? '-' : row.priceMarket }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.priceMarket"
                v-if="isEditPrice(row, 'priceMarket')"
                @blur="formatBlur(row, 'priceMarket')"
              />
              <span v-else>{{ isChangeAva(row) ? '-' : row.priceMarket }}</span>
            </template>
          </vxe-column>
          <vxe-column
            v-if="projectStore.deStandardReleaseYear === '22'"
            :title="'含税市场价'"
            field="priceMarketTax"
            width="110"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ isChangeAva(row) ? '-' : row.priceMarketTax }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.priceMarketTax"
                v-if="isEditPrice(row, 'priceMarketTax')"
                @blur="formatBlur(row, 'priceMarketTax')"
              />
              <span v-else>{{
                isChangeAva(row) ? '-' : row.priceMarketTax
              }}</span>
            </template>
          </vxe-column>
          <vxe-column
            v-if="projectStore.deStandardReleaseYear === '12'"
            title="除税系数"
            field="taxRemoval"
            width="100"
          >
            <template #default="{ row, column }">
              <span>{{ row.taxRemoval }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-if="row.isFyrcj"
                v-model="row.taxRemoval"
                @blur="formatBlur(row, 'taxRemoval')"
              />
              <span v-else>{{ row.taxRemoval }}</span>
            </template>
          </vxe-column>
          <vxe-column
            v-if="projectStore.deStandardReleaseYear === '22'"
            title="税率"
            field="taxRate"
            width="100"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
          >
            <template #default="{ row, column }">
              <span>{{ isChangeAva(row) ? '-' : row.taxRate }}</span>
            </template>

            <template #edit="{ row }">
              <vxe-input
                v-model="row.taxRate"
                v-if="
                  row.ifLockStandardPrice !== 1 &&
                  (row.levelMark === 0 ||
                    !row.rcjDetailsDTOs ||
                    (row.levelMark !== 0 && row.rcjDetailsDTOs.length === 0)) &&
                  isPartEdit &&
                  row.libraryCode.indexOf('2022') !== -1 &&
                  !isChangeAva(row) &&
                  row.supplementDeRcjFlag !== 1 &&
                  row.isFyrcj
                "
                @blur="formatBlur(row, 'taxRate')"
              />
              <span v-else>{{ isChangeAva(row) ? '-' : row.taxRate }}</span>
            </template>
          </vxe-column>
        </vxe-table>
        <material-machine-index
          v-model:indexVisible="indexVisible"
          :currentMaterialInfo="currentMaterialInfo"
          :indexLoading="indexLoading"
          @addChildrenRcjData="addChildrenRcjData"
          @currentInfoReplace="currentInfoReplace"
          :modalProps="{ lockView: true }"
        ></material-machine-index>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import api from '../../../../../api/projectDetail.js';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import {
  pureNumber,
  rcjPrice,
  removeSpecialCharsFromPrice,
} from '@/utils/index';
import infoMode from '@/plugins/infoMode.js';
import { projectDetailStore } from '@/store/projectDetail.js';
import { useBcData } from '@/hooks/useBcData.js';
import MaterialMachineIndex from '../../materialMachineIndex/index.vue';
import { useCellClick } from '@/hooks/useCellClick.js';
import { modalProps } from 'ant-design-vue/lib/modal/Modal.js';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
const { costPriceFormat, rcjDetailAmountFormat, rateFormat } =
  useDecimalPoint();
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick({ rowKey: 'sortNo' });
const store = projectDetailStore();
const form = ref();
const loading = ref(false);
const spinning = ref(false);
const emit = defineEmits(['update:visible', 'rcjSaveData']);
const lists = reactive({
  rcjTypeList: [],
});
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  code: {
    type: String,
    default: '',
  },
  currentInfo: {
    // 编辑区当前行数据
    type: Object,
    default: () => {},
  },
  materialInfo: {
    // 当前人材机明细行数据
    type: Object,
    default: () => {},
  },
});
const formatBlur = (row, filed) => {
  let regex = /-?\d{0,8}(\.\d{0,6}|100)?/;
  let val = (row[filed]?.match(regex) || [''])[0];
  const defaultValue = ['resQty', 'dePrice'].includes(filed) ? 0 : '';
  if (filed === 'taxRate') {
    val = rateFormat(val);
  }
  if (['taxRemoval', 'resQty'].includes(filed)) {
    val = rcjDetailAmountFormat(val, defaultValue);
  }
  if (
    ['priceMarketTax', 'priceMarket', 'marketPrice', 'dePrice'].includes(filed)
  ) {
    val = costPriceFormat(val, defaultValue);
  }
  row[filed] = val;
};
const projectStore = projectDetailStore();
const colStyle = reactive({
  colSize: null,
});

const inputData = reactive({
  materialName: null, //项目名称
  materialCode: null, //项目编码
  specification: null, // 规格及型号
  unit: null, // 单位
  taxRemoval: null, // 除税系数
  resQty: 1, // 材料消耗量
  dePrice: null, // 定额价
  marketPrice: null, // 市场价
  taxRate: 0,
});

let tableData = ref([]);
const indexVisible = ref(false);
let currentMaterialInfo = ref(null);
let indexLoading = ref(false); // 索引页面loading
const currentInfo = ref();
const vexTable = ref();
let dePrice = ref(0); // 明细区总单价

onMounted(() => {});

// input框输入值置为空
const reset = () => {
  loading.value = false;
  for (let key in inputData) {
    inputData[key] = null;
  }
  tableData.value = [];
};
const isEditPrice = (row, column) => {
  //判断市场价-含税/不含税市场价  税率是否可以编辑
  console.log(
    isPartEdit.value &&
      ![1, 2].includes(Number(row.levelMark)) &&
      !isChangeAva(row) &&
      !isOtherMaterial.value &&
      row.isFyrcj,
    'isEditPrice',
    row
  );
  let isFlag =
    isPartEdit.value &&
    ![1, 2].includes(Number(row.levelMark)) &&
    !isChangeAva(row) &&
    !isOtherMaterial.value &&
    row.isFyrcj;
  let isEdit =
    (['marketPrice', 'priceMarket', 'priceMarketTax'].includes(column) &&
      isFlag) ||
    (column === 'taxRate' &&
      isFlag &&
      !row.de2012In2022 &&
      !isDeType('12', row.deStandardReleaseYear) &&
      row.kind !== 1);
  return isEdit;
};
const otherCodeList = [
  'QTCLFBFB',
  '34000001-2',
  'J00004',
  'J00031',
  'J00031',
  'C11384',
  'C00007',
  'C000200',
  'C11408',
  'C11388',
  'J00006',
  'J00008',
];
const isOtherMaterial = computed(() => {
  const { materialCode } = currentInfo.value || {};
  return otherCodeList.includes(materialCode);
});
const cancel = () => {
  emit('bcCancel', 3);
  indexVisible.value = false;
};

const saveCustomInput = (newValue, row, name, index) => {
  row[name] = newValue;
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};

let { defaultCodeColl, bcCode } = useBcData();
const getUnit = unit => {
  if (isNaN(parseInt(unit))) {
    return unit;
  }
  return unit.replace(parseInt(unit), '');
};

watch(
  () => props.visible,
  (val, oldVal) => {
    console.log('不进来么', props.currentInfo);
    if (val) {
      form.value?.resetFields();
      reset();
      inputData.taxRemoval = 11.28;
      inputData.dePrice = 0;
      inputData.taxRate = 0;
      inputData.resQty = 1;
      inputData.materialCode = props.code;
      inputData.materialName = props.currentInfo?.name;
      inputData.unit = getUnit(props.currentInfo?.unit);
      if (!props.code) {
        defaultCodeColl(3);
      }
      if (bcCode.value) {
        inputData.materialCode = bcCode.value;
      }
      getTypeList();
    }
  }
);
watch(
  () => bcCode.value,
  (val, oldVal) => {
    console.log('不进来么');
    if (val) {
      inputData.materialCode = val;
    }
  }
);

// ['QTCLFBFB','34000001-2','J00004','J00031','J00031','C11384','C00007','C000200'] 不能编辑
const isPartEdit = computed(() => {
  return !(
    [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
    ].includes(currentInfo.value?.materialCode) &&
    currentInfo.value?.unit === '%'
  );
});

const getTypeList = () => {
  console.log('这儿来不');
  lists.rcjTypeList = [];
  api.getTypeList().then(res => {
    console.log('33333', res);
    if (res) {
      res.forEach(item => {
        if (item.kind === 2) {
          inputData.kind = 2;
        }
        lists.rcjTypeList.push({
          label: item.type,
          value: item.kind,
        });
      });
    }
  });
};

// 单元格退出编辑事件
const editClosedEvent = async ({ row, column }) => {
  const $table = vexTable.value;
  let field = column.field;
  console.log(
    'row11111111',
    row,
    $table.isUpdateByRow(row, field),
    needSave.includes(field)
  );
  if (
    ($table.isUpdateByRow(row, field) && !needSave.includes(field)) ||
    (needSave.includes(field) && row[`${field}lastEdit`] !== row[field])
  ) {
    if (needSave.includes(field)) {
      row[`${field}lastEdit`] = row[field];
    }
    if (tableData.value.length > 0) {
      if (
        field === 'resQty' ||
        field === 'priceMarket' ||
        field === 'priceMarketTax' ||
        field === 'marketPrice' ||
        field === 'taxRate'
      ) {
        inputData.dePrice = 0;
        tableData.value.forEach(item => {
          console.log(
            '5555555555555',
            Number(item.resQty),
            Number(item.priceMarket),
            Number(item.priceMarketTax),
            Number(item.marketPrice)
          );
          inputData.dePrice +=
            Number(item.resQty) *
            (projectStore.deStandardReleaseYear === '22'
              ? projectStore.taxMade === 1
                ? Number(item.priceMarket)
                : Number(item.priceMarketTax)
              : Number(item.marketPrice));
        });
        inputData.dePrice = costPriceFormat(inputData.dePrice, 0);
        dePrice.value = inputData.dePrice;
        // 修改不含税市场价 含税市场价= 不含税市场价*(1+税率%）
        if (field === 'priceMarket') {
          row.priceMarketTax = costPriceFormat(
            row.priceMarket * (1 + row.taxRate / 100)
          );
          row.priceMarketTaxlastEdit = row.priceMarketTax;
        } else if (field === 'priceMarketTax') {
          row.priceMarket = costPriceFormat(
            row.priceMarketTax * (1 + row.taxRate / 100)
          );
          row.priceMarketlastEdit = row.priceMarket;
        } else if (field === 'taxRate') {
          row.priceMarketTax = costPriceFormat(
            row.priceMarket * (1 + row.taxRate / 100)
          );
          row.priceMarketTaxlastEdit = row.priceMarketTax;
        }
        if (projectStore.deStandardReleaseYear === '22') {
          row.marketPrice = +projectStore.taxMade
            ? row.priceMarket
            : row.priceMarketTax;
        }
        setTimeout(() => {
          // 保存完成后将行恢复到初始状态
          $table.reloadRow(row, {});
        }, 300);
      }
    }
  }
};

const codeExistInUnit = () => {
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    code: inputData.materialCode,
  };
  console.log('判断编码是否存在', apiData);
  api.codeExistInUnit(apiData).then(res => {
    if (res.status === 200) {
      if (res.result) {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-querenshanchu',
          infoText: '单位工程中已存在该材料编码,请重新输入',
          confirm: () => {
            infoMode.hide();
            loading.value = false;
          },
        });
      } else {
        const taxRemoval = inputData.taxRemoval || 0;
        const resQty = inputData.resQty || 1;
        indexVisible.value = false;
        emit('rcjSaveData', { ...inputData, taxRemoval, resQty });
      }
    }
  });
};
/**
 * 是否材料类型
 * @param {*} kind
 */
const isMaterials = kind => {
  return [2, 5, 6, 7, 8, 9, 10].includes(kind);
};
const onSubmit = () => {
  if (tableData.value.length > 0) {
    inputData.dePrice = dePrice.value;
  }
  inputData.marketPrice = inputData.dePrice;
  inputData.materialName = inputData.materialName.trim();
  inputData.rcjList = JSON.parse(JSON.stringify(tableData.value));
  const materialInfo = props.materialInfo;
  console.log(materialInfo, inputData);
  if (
    materialInfo &&
    !materialInfo?.rcjDetailsDTOs &&
    ((isMaterials(materialInfo.kind) && !isMaterials(inputData.kind)) ||
      (materialInfo.kind === 3 && materialInfo.kind !== inputData.kind))
  ) {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '明细数据仅可插入同主数据一致类型的数据',
      confirm: () => {
        infoMode.hide();
      },
    });
    return true;
  }
  loading.value = true;
  isStandardRcj();
};

// 判断输入的材料编码是否标准人材机数据
const isStandardRcj = () => {
  let apiData = {
    unitId: store.currentTreeInfo?.id,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    code: inputData.materialCode,
  };
  api.isStandardRcj(apiData).then(res => {
    if (res.status === 200) {
      if (res.result) {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-querenshanchu',
          infoText: '材料编码不可与标准材料编码相同，请重新输入',
          confirm: () => {
            infoMode.hide();
            loading.value = false;
          },
        });
      } else {
        codeExistInUnit();
      }
    }
  });
};

const selectChange = value => {
  inputData.taxRemoval = null;
  inputData.materialName = '';
  inputData.unit = '';
  tableData.value = [];
  switch (value) {
    case 1:
      inputData.taxRemoval = 0;
      break;
    case 2:
    case 5:
      inputData.materialName = props.currentInfo?.name;
      inputData.unit = getUnit(props.currentInfo?.unit);
      inputData.taxRemoval = 11.28;
      break;
    case 3:
      inputData.taxRemoval = 8.66;
      break;
    case 4:
      inputData.materialName = props.currentInfo?.name;
      inputData.unit = getUnit(props.currentInfo?.unit);
      inputData.taxRemoval = 11.36;
      break;
  }
  defaultCodeColl(3, value);
};

const contextmenuList = reactive({
  className: 'my-menus',
  body: {
    options: [
      [
        {
          code: 1,
          name: '插入人材机',
          visible: true,
          disabled: false,
        },
        {
          code: 3,
          name: '删除',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod: ({ options, column, columnIndex, row, rowIndex }) => {
    console.log('🚀 ~ row:', row, options);
    options.forEach(list => {
      list.forEach(async (item, index) => {
        console.log('row', row);

        if (item.code === 3) {
          if (tableData.value.length === 0) {
            item.disabled = true;
          } else {
            item.disabled = false;
          }
        }
      });
    });
    return true;
  },
});

const onContextMenuClick = ({ menu, row }) => {
  // if (!row) {
  //   message.warning('请先选择要材料');
  //   return;
  // }
  currentInfo.value = row;
  console.log(
    '🚀 ~ onContextMenuClick ~ currentInfo.value:',
    currentInfo.value
  );
  const value = menu.code;
  switch (value) {
    case 1:
      const $table = vexTable.value;
      let parentData = $table.getParentRow(row);
      console.log('parentData', parentData);
      if (row) {
        row.parentId = parentData?.sequenceNbr;
      }
      openIndexList(row);
      break;
    case 2:
      let obj = {
        materialName: null, //项目名称
        materialCode: null, //项目编码
        specification: null, // 规格及型号
        unit: null, // 单位
        taxRemoval: null, // 除税系数
        resQty: null, // 材料消耗量
        dePrice: null, // 定额价
        marketPrice: null, // 市场价
      };
      tableData.value.push(obj);
      break;
    case 3:
      let currentIndex = vexTable.value.getRowIndex(row);
      tableData.value.splice(currentIndex, 1);
    default:
      break;
  }
};

const openIndexList = row => {
  indexVisible.value = true;
  currentMaterialInfo.value = row;
  projectStore.SET_SUB_CURRENT_MATERIAL_INFO(row);
};
const needSave = ['priceMarket', 'priceMarketTax', 'marketPrice', 'taxRate']; //-为了编辑判断是否改变
const addChildrenRcjData = row => {
  if (
    row.levelMark === 0 &&
    ((isMaterials(row.kind) && isMaterials(inputData.kind)) ||
      row.kind === inputData.kind)
  ) {
    indexLoading.value = true;
    row.isSupplement = 0;
    row.resQty = 0;
    row.type = row.level1;
    row.priceMarketTax = row.priceBaseJournalTax;
    row.priceMarket = row.priceBaseJournal;
    row.marketPrice = row.dePrice;
    let newRow = { ...row };
    needSave.map(key => {
      newRow[`${key}lastEdit`] = newRow[key];
    });
    tableData.value.push(newRow);
    indexLoading.value = false;
    tableData.value = tableData.value.map((item, index) => ({
      sortNo: index + 1,
      ...item,
    }));
  } else {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '请插入同类别非配比数据哟',
      confirm: () => {
        infoMode.hide();
      },
    });
  }
};

const currentInfoReplace = row => {
  if (
    row.levelMark === 0 &&
    ((isMaterials(row.kind) && isMaterials(inputData.kind)) ||
      row.kind === inputData.kind)
  ) {
    let currentIndex = vexTable.value.getRowIndex(currentInfo.value);
    console.log('index', row, currentIndex);
    let tempList = JSON.parse(JSON.stringify(tableData.value));
    tempList[currentIndex] = row;
    tableData.value = tempList;
    console.log('tableData', tableData.value);
    tableData.value.forEach((item, index) => {
      item.sortNo = index + 1;
    });
  } else {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '请插入同类别非配比数据哟',
      confirm: () => {
        infoMode.hide();
      },
    });
  }
};

// 基期价、市场价为“-
const isChangeAva = row => {
  return (
    projectStore.deStandardReleaseYear === '22' &&
    ![null, undefined].includes(row.isChangeAva) &&
    Number(row.isChangeAva) === 0
  );
};
</script>
<style lang="scss" scoped>
@import './style.scss';
</style>
