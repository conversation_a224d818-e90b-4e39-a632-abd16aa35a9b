const {ObjectUtil} = require('../../common/ObjectUtil');
const path = require('path');
const { app} = require('electron');
const Loader = require('../loader');

class ObjectCacheUtils {
   static ysRCacheClass = null;
   static ysWCacheClass = null;
   static gsCacheClass = null;
   static gljCacheClass = null;
   static reloadCache = false;
   static GS_CONTROLLER_SUBSTR = "PreliminaryEstimate";
   static GLJ_CONTROLLER_SUBSTR = "gongLiaoJiProject";

  static init(args){
    if(ObjectCacheUtils.reloadCache){
      return ;
    }
    let filePaths = [{type:"ysread",filePath:path.join("electron","utils","PricingFileFindUtils.js")}
      ,{type:"yswrite",filePath:path.join("electron","utils","PricingFileWriteUtils.js")}
      ,{type:"gs",filePath:path.join("packages",ObjectCacheUtils.GS_CONTROLLER_SUBSTR,"core","container","APPContext.js")}
      ,{type:"glj",filePath:path.join("packages",ObjectCacheUtils.GLJ_CONTROLLER_SUBSTR,"core","container","APPContext.js")}]


    for(let cachePath of filePaths){
      
      let filePath = path.join(app.getAppPath(),cachePath.filePath);
      let mod = Loader.loadJsFile(filePath);
      if (ObjectUtil.isNotEmpty(mod)) {

        if(cachePath.type === "ysread"){
          ObjectCacheUtils.ysRCacheClass = mod.PricingFileFindUtils;
        }

        if(cachePath.type === "yswrite"){
          ObjectCacheUtils.ysWCacheClass = mod.PricingFileWriteUtils;
        }

        if(cachePath.type === "gs"){
          ObjectCacheUtils.gsCacheClass = mod;
        }
        if(cachePath.type === "glj"){
          ObjectCacheUtils.gljCacheClass = mod;
        }
      }
    }
    ObjectCacheUtils.reloadCache = true;
  }

  // "PreliminaryEstimate",
  // "shenHeYuSuanProject",
  // "jieSuanProject"
  // "gongLiaoJiProject"
  static get(constructId,channel) {
    
    if(channel.indexOf(ObjectCacheUtils.GS_CONTROLLER_SUBSTR) > -1 && ObjectCacheUtils.gsCacheClass && ObjectCacheUtils.gsCacheClass.getContext){
      return ObjectCacheUtils.gsCacheClass.getContext(constructId);
    }else if(channel.indexOf(ObjectCacheUtils.GLJ_CONTROLLER_SUBSTR) > -1 && ObjectCacheUtils.gljCacheClass && ObjectCacheUtils.gljCacheClass.getContext){
      return ObjectCacheUtils.gljCacheClass.getContext(constructId);
    }else if(ObjectCacheUtils.ysRCacheClass && ObjectCacheUtils.ysRCacheClass.getProjectObjById){
      return ObjectCacheUtils.ysRCacheClass.getProjectObjById(constructId);
    };
  }

  static updateObject(constructId, newObject, channel){

    if(channel.indexOf(ObjectCacheUtils.GS_CONTROLLER_SUBSTR) > -1 &&  ObjectCacheUtils.gsCacheClass && ObjectCacheUtils.gsCacheClass.removeContext){
      ObjectCacheUtils.gsCacheClass.removeContext(constructId);
      ObjectCacheUtils.gsCacheClass.addContext(constructId, newObject);
    }else if(channel.indexOf(ObjectCacheUtils.GLJ_CONTROLLER_SUBSTR) > -1 &&  ObjectCacheUtils.gljCacheClass && ObjectCacheUtils.gljCacheClass.removeContext){
      ObjectCacheUtils.gljCacheClass.removeContext(constructId);
      ObjectCacheUtils.gljCacheClass.addContext(constructId, newObject);
    }else if(ObjectCacheUtils.ysWCacheClass && ObjectCacheUtils.ysWCacheClass.writeToMemory){
      ObjectCacheUtils.ysWCacheClass.writeToMemory(newObject);
    };
  }    
}

module.exports = ObjectCacheUtils