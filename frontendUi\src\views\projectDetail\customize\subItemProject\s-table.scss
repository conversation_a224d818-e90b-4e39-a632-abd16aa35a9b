.table-content {
  height: 100%;
  //user-select: none;
  ::v-deep(.surely-table) {
    font-size: var(--project-detail-table-font-size);
    height: 100%;
    ::-webkit-scrollbar-thumb {
      background-color: rgba(24, 144, 255, 0.2);
      background-clip: padding-box;
      min-height: 28px;
      border-radius: 5px;
    }
    :hover::-webkit-scrollbar-thumb {
      background-color: rgba(24, 144, 255, 0.8);
      background-clip: padding-box;
      min-height: 28px;
      border-radius: 5px;
    }
  }
  .s-table {
    height: 100%;
    ::v-deep(.ant-spin-nested-loading) {
      height: 100%;
    }
    ::v-deep(.ant-spin-container) {
      height: 100%;
    }
    .vxe-pulldown {
      width: 100%;
    }
    .bdCodeContent {
      height: 100%;
      ::v-deep(.vxe-pulldown--content) {
        height: 100% !important;
      }
    }
    ::v-deep(.association-selected) {
      position: relative;
    }
    ::v-deep(.association-popover) {
      // position: absolute;
      // left: 0 !important;
      width: 280px !important;
    }
  }
  ::v-deep(.surely-table-cell-content) {
    padding: 4px 1px;
    min-height: 24px;
  }
  ::v-deep(.deCode-center .surely-table-header-cell-title-inner) {
    text-align: center !important;
  }
  ::v-deep(.surely-table-body) {
    .vxe-icon-caret-right:before {
      position: relative;
      top: 1px;
      content: '+';
    }
    .rotate90 {
      transform: rotate(180deg) !important;
    }
    .rotate90:before {
      content: '-';
    }
    .ant-popover-inner-content {
      padding: 0;
    }
  }
  ::v-deep(.surely-table-body-cell) {
    overflow: visible !important;
    &.projectAttr-item {
      overflow: initial !important;
      .vxe-pulldown,
      .vxe-pulldown--content,
      .vxe-pulldown--content > div {
        height: 100%;
      }
    }
  }
  ::v-deep(.surely-table-body .row-unit) {
    background: #e6dbeb;
  }
  ::v-deep(.surely-table-body .row-sub) {
    background: #efe9f2 !important;
  }
  ::v-deep(.surely-table-body .row-qd) {
    background: #dce6fa;
  }
  ::v-deep(.surely-table-body .none) {
    //设置成无色时背景色白色文字才不会被覆盖
    // background: white !important;
  }

  ::v-deep(.surely-table-body .priceFlag) {
    color: #ce2929;
  }

  ::v-deep(.surely-table-body .red) {
    background: #ef7c77 !important;
  }

  ::v-deep(.surely-table-body .green) {
    background: #e3fada !important;
  }

  ::v-deep(.surely-table-body .orange) {
    background: #e59665 !important;
  }

  ::v-deep(.surely-table-body .yellow) {
    background: #fdfdac !important;
  }

  ::v-deep(.surely-table-body .blue) {
    background: #8fa9fa !important;
  }

  ::v-deep(.surely-table-body .purple) {
    background: #cfaadd !important;
  }

  ::v-deep(.surely-table-body .lightBlue) {
    background: #a5d7f0 !important;
  }

  ::v-deep(.surely-table-body .deepYellow) {
    background: #fbdf89 !important;
  }

  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  ::v-deep(.surely-table-body .code-color) {
    color: #a73d3d;
    padding-left: 10px;
  }
  ::v-deep(.surely-table-body .index-bg) {
    background-color: #ffffff;
  }
  ::v-deep(.surely-table-cell-edit-wrapper .ant-select) {
    font-size: 12px;
    width: 100%;
  }
  ::v-deep(.surely-table-cell-edit-wrapper .ant-select .ant-select-arrow) {
    width: 2px !important;
    height: 7px !important;
  }
  ::v-deep(
      .surely-table-cell-edit-wrapper .ant-select .ant-select-selection-item
    ) {
    padding-right: 2px;
  }
  ::v-deep(
      .surely-table-body--render-default.is--tree-line
        .vxe-body--row
        .vxe-body--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(
      .surely-table-body--render-default.is--tree-line .vxe-header--column
    ) {
    background-image: linear-gradient(#b9b9b9, #b9b9b9),
      linear-gradient(#b9b9b9, #b9b9b9);
  }
  ::v-deep(.surely-table-body .temp-delete) {
    background: #f3f2f3;
    color: #a7a7a7;
    .surely-table-cell-content,
    .surely-table-cell-content > *:not(i) {
      text-decoration: line-through;
    }
  }
  ::v-deep(.surely-table-row) {
    transition: none;
  }
  ::v-deep(.surely-table-row.surely-table-row-selected) {
    background-color: var(--surely-table-row-selected-bg);
    z-index: 10;
  }
  ::v-deep(.surely-table-center) {
    z-index: 2;
  }
  ::v-deep(.surely-table-row.surely-table-row-selected.surely-table-row-hover) {
    background: var(--surely-table-row-selected-hover-bg);
  }
  ::v-deep(.surely-table-header-cell):hover {
    .icon-close-s {
      opacity: 1;
    }
  }
  ::v-deep(.surely-table.surely-table-small .surely-table-cell-box) {
    padding: 0 0px;
  }
  ::v-deep(.surely-table-cell-edit-inner) {
    padding: 1px 0;
    height: 100%;
    display: inline-block;
    .code {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
    .ant-input {
      height: 100%;
      font-size: 12px;
      resize: none;
      min-height: 18px;
    }
  }
  ::v-deep(.surely-table-popup) {
    z-index: 200;
  }
  ::v-deep(.ant-select) {
    font-size: 12px;
  }
  .nameEdit {
    display: flex;
    align-items: center;
    text-align: left;
    .name {
      flex: 1;
      margin-right: 2px;
      margin-left: 15px;
    }
    .pre-name {
      margin: 0 2px 0 15px;
      width: 100%;
      text-align: left;
      white-space: pre-wrap; /* CSS3 */
      word-wrap: break-word; /* Internet Explorer 5.5+ */
      overflow-wrap: break-word; /* CSS3 */
    }

    .more-icon {
      visibility: hidden;
      margin-left: auto;
    }
  }
  .nameEdit:hover {
    .more-icon {
      visibility: visible;
    }
  }
  .vxe-icon-caret-down,
  .vxe-icon-caret-right {
    width: 12px;
    height: 12px;
    display: inline-block;
    text-align: center;
    line-height: 8px;
    border-radius: 50%;
    position: relative;
    top: -1px;
    left: 2px;
    border: 1px solid #87b2f2;
    color: #87b2f2;
    font-size: 12px;
  }
  .vxe-icon-caret-down:before {
    content: '-';
  }
  .vxe-icon-caret-right:before {
    content: '+';
  }
  .custom-header .icon-close-s {
    position: absolute;
    right: 2px;
    background: #ffffff;
    z-index: 20;
    padding: 3px;
    opacity: 0;
  }
  .custom-header:hover {
    .icon-close-s {
      opacity: 1;
    }
  }
}
// 表格剪切时的样式
$borderColor: rgba(
  22,
  119,
  255,
  1
); //var(--surely-table-cell-focus-border-color);
$borderColorOpacity: var(--surely-table-border-color);
@keyframes highlight-animation {
  0% {
    border-color: $borderColorOpacity;
  }
  50% {
    border-color: $borderColor;
  }
  100% {
    border-color: $borderColorOpacity;
  }
}
.dashed-table {
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-top
    ),
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-left
    ),
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-right
    ),
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-bottom
    ) {
    border: 0;
  }

  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-top
    ):before {
    border-top: 2px dashed $borderColor;
  }
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-left
    ):before {
    border-left: 2px dashed $borderColor;
  }
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-right
    ):before {
    border-right: 2px dashed $borderColor;
  }
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-bottom
    ):before {
    border-bottom: 2px dashed $borderColor;
  }

  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-top
    ):before,
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-left
    ):before,
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-right
    ):before,
  ::v-deep(
      .surely-table-body-cell-range-selected.surely-table-body-cell-range-bottom
    ):before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    animation: highlight-animation 0.8s infinite;
  }
}
